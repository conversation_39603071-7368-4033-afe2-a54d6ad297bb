# open-wx-k8s
```mermaid
flowchart TD
    %% Title of the flowchart

    %% Weather File Arrival and Initial Processing
    A[Weather File Arrives] -->|Upload| B[S3 Bucket]
    B -->|Send Message| C[DataToShred SQS]
    C -->|Trigger| D[Splinter Function]

    %% Splinter Function Decision
    D -->|Check Pattern| E{Matches Regex?}
    E -- Yes --> F[ShredderInputQueue SQS]
    E -- No --> G((End))

    %% Shredder Processing
    F -->|Trigger| H[Shredder Function]
    H -->|Send to Queue| I[ShredderOutputQueue SQS]

    %% Enqueue Processing
    I -->|Trigger| J[Enqueue Function]
    J -->|Initiate| K[Create Temp Data]

    %% Selection-Compiler Data Storage
    K --> L[COMPILER_SPOOL]
    K --> M[COMPILER_STATE]

    %% Scheduling Process
    L -->|Schedule| N[Schedule Process]
    M -->|Schedule| N
    N -->|Check| O{Pending Files?}
    O -- Yes --> P[Process Files]
    O -- No --> G

    %% Processing and Final Steps
    P -->|Send Message| Q[COMPILER_QUEUE SQS]
    Q -->|Trigger| R[Issue Function]

    %% Finalizing Selection
    R -->|Store| S[Selection-Repository]
    R -->|Publish| T[SELECTION_TOPIC]
```

# Currently able to build container for shredding/splinter/selection-compiler with
* note --platform is necessary for building on Apple Silicon M1/M2/M3
```
# build container for shredding/splinter/selection-compiler 
docker build --platform=linux/amd64 -f Dockerfile-Shredder -t open-weather-shredder .

# building to deploy to ecr
docker build -f Dockerfile-Shredder -t 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-shredder:latest .
```

# Currently able to build container for ogc-services with
* note --platform is necessary for building on Apple Silicon M1/M2/M3
```
# build container for ogc-services
docker build --platform=linux/amd64 -f Dockerfile-Services -t open-weather-services . 

# building to deploy to ecr
docker build -f Dockerfile-Services -t 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-services:latest .

# push to ecr

```

# Currently able to build container for ogc-proxy with
```
# building to deploy to ecr
docker build -f Dockerfile-Proxy -t 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-proxy:latest .

# push to ecr
docker push 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-proxy:latest
```

# login to ECR
* note, ECR login is done in `./update-k8s-aws-secrets.sh` for minikube acces, but you will \
need to run it manually to push
`aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 125100372861.dkr.ecr.us-east-2.amazonaws.com`

# on k8s dev box
```
# Start minikube
minikube start --driver=docker
# If you are deploying on a beefy box, increase the allocated cpus and memory
minikube start --driver=docker --cpus=12 --memory=56000mb


# Run the update secrets script to refresh AWS and ECR credentials
./update-k8s-aws-secrets.sh

# if using istio enable injection on the namespace
kubectl label namespace open-weather-shredder istio-injection=enabled

# Helm deploy
helm install open-weather-services helm -n open-weather

# Helm uninstall
helm uninstall open-weather-services -n open-weather


# Delete all pods
kubectl delete pods --all -n open-weather-shredder

# Run Minikube dashboard to see application health
minikube dashboard

# Forward Port 8080 to ingressgateway
kubectl port-forward -n istio-system svc/istio-ingressgateway 8080:80
```

# Telemetry Tools

`istioctl dashboard jaeger`

`istioctl dashboard kiali`

`istioctl dashboard grafana`


# K8S Secrets
You need to generate a secret for K8s to pull the image

```
aws ecr get-login-password --region us-east-2 | kubectl create secret docker-registry ecr-creds \
  --namespace=open-weather \
  --docker-server=125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-shredder \
  --docker-username=AWS \
  --docker-password=$(aws ecr get-login-password --region us-east-2) \
  --docker-email=<EMAIL>
```

You also need to generate AWS credentials for the application to run. 
Run `update-k8s-aws.secrets.sh` on a system that has a role with ample permission and it will \
create the secrets to pass into the pods for access.

Need to add the terraform scripts to setup s3 bucket, sqs/sns topics, and opensearch instance.

Then will add the helm chart to deploy.

# Overview of ENV variables

# Environment Variables


| Variable | Type | Local | AWS | Localstack |
|----|----|----|----|----|
| $GED_BUCKET | File or S3 | local://ged-data/data/ | s3://ged-bucket | http://localstack:4566/gedbucket/ | 
| $COMPILER_STATE | File or ES | local://ged-data/compiler/state/ | elasticsearch://compiler/state/<sup>1</sup> |
| $COMPILER_SPOOL | File or S3 | local://ged-data/compiler/spool/ | s3://ged-bucket/compiler/spool/ |
| $DATA_EXPIRATION | ISO8601 | P1D | P1D | 
| $SELECTION_TOPIC | Topic name | local://ged-selection | ged-selection |
| $IELASTICSEARCHSTORAGE | ES endpoint | N/A | https://search-master-metadata.eu-west-2.es.amazonaws.com?index=objectstorage&aws-signature-v4 |
| $ICONTENTREPOSITORY | repository prefixed endpoint (es/local) | N/A | es:https://search-master-metadata.eu-west-2.es.amazonaws.com?index=ged&aws-signature-v4 |
| $AWS_ENDPOINT_URL | Path to localstack | N/A | N/A | http://localstack:4566<sup>2</sup> |

<sup>1</sup> When referencing `elasticsearch://` prefix, $IELASTICSEARCHSTORAGE must be defined and is what will be referenced

<sup>2</sup> $AWS_ENDPOINT_URL only needs to be defined when using localstack to overwrite AWS

COMPILER_SPOOL=s3://master-ged-data/tmp/compiler/
COMPILER_STATE=elasticsearch://state/compiler/
IELASTICSEARCHSTORAGE=https://search-master-metadata.eu-west-2.es.amazonaws.com?index=master-state&aws-signature-v4
ICONTENTREPOSITORY=es:https://search-master-metadata.eu-west-2.es.amazonaws.com?index=master&aws-signature-v4

