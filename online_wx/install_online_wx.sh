#!/usr/bin/env bash
set -euo pipefail
IFS=$'\n\t'

# ── CONFIG ─────────────────────────────────────────────────────────
VERSION_TAG="ow-7.1.7-20250416"
TARGET_BASE="/home/<USER>/ibl/packages"
TARGET_DIR="${TARGET_BASE}/${VERSION_TAG}"
S3_BUCKET="s3://ibl-resources/OnlineWeather/"
AWS_REGION="us-east-2"

# ── 1. Grab the platform root from $METPATH ────────────────────────
: "${METPATH:?METPATH must be set to your VisualWeather/OpenWeather install root}"
PROCESS_DIR="${METPATH}/config/processes"
echo "Using installation at ${METPATH}"

echo "—— OnlineWeather installer starting ——"

# ── 2. Ensure METPATH and process directory exist ──────────────────
echo "Verifying install root ${METPATH} exists..."
if [ ! -d "${METPATH}" ]; then
  echo "ERROR: install root ${METPATH} not found. Please check METPATH."
  exit 1
fi

echo "Verifying processes directory ${PROCESS_DIR} exists..."
if [ ! -d "${PROCESS_DIR}" ]; then
  echo "ERROR: processes directory ${PROCESS_DIR} not found."
  echo "Please create the directory at ${PROCESS_DIR}."
  exit 1
fi

echo "Environment checks complete."

# ── 3. Ensure root ────────────────────────────────────────────────
echo "Verifying script is running as root..."
if [ "$(id -u)" -ne 0 ]; then
  echo "This script must run as root. Use sudo."
  exit 1
fi
echo "Root check complete."

# ── 4. AWS CLI & S3 access ────────────────────────────────────────
echo "Locating AWS CLI..."
AWS_BIN="$(command -v aws || true)"
[ -z "$AWS_BIN" ] && [ -x /usr/local/bin/aws ] && AWS_BIN=/usr/local/bin/aws

if [ -z "$AWS_BIN" ]; then
  echo "AWS CLI not found. Install it (e.g. sudo yum install -y awscli)."
  exit 1
fi

echo "AWS CLI version: $("$AWS_BIN" --version 2>&1 | head -n1)"
echo "Testing access to ${S3_BUCKET}..."
if ! "$AWS_BIN" s3 ls "${S3_BUCKET}" --region "${AWS_REGION}" &> /dev/null; then
  echo "Cannot list ${S3_BUCKET}. Check AWS credentials or region."
  exit 1
fi
echo "AWS connectivity check complete."

# ── 5. Pull packages from S3 ───────────────────────────────────────
echo "Creating package directory ${TARGET_DIR}..."
mkdir -p "${TARGET_DIR}"
chown -R vwadmin:vwadmin "${TARGET_BASE}"
echo "Package directory ready."

echo "Syncing OnlineWeather packages from S3 → ${TARGET_DIR} (exact mirror)…"
"$AWS_BIN" s3 sync "${S3_BUCKET}" "${TARGET_DIR}" \
          --region "${AWS_REGION}" --delete
echo "Package sync complete."

echo "Extracting packages..."
for zip in "${TARGET_DIR}"/*.zip; do
  [ -f "$zip" ] || continue
  echo "Unzipping $(basename "$zip")..."
  unzip -qo "$zip" -d "${TARGET_DIR}"
done

echo "Extraction complete."

# ── 6. Install the OW backend ──────────────────────────────────────
echo "Searching for ow-backend install.sh…"
BACKEND_INSTALL=$(find "${TARGET_DIR}" -type f -name install.sh -path '*ow-backend*' | head -n1)

if [ -z "${BACKEND_INSTALL}" ]; then
  echo "ERROR: install.sh not found inside an ow-backend package."
  echo "Directory listing:"
  find "${TARGET_DIR}" -maxdepth 3 -type d -name 'ow-backend*'
  exit 1
fi

echo "Running ${BACKEND_INSTALL} ..."
bash "${BACKEND_INSTALL}" --vw "${METPATH}" --no-dry
echo "ow-backend install complete."

echo "Fixing ownership so vwadmin can write session data..."
chown -R vwadmin:vwadmin "${METPATH}/lib/node_modules/@ibl"
echo "Ownership fix complete."


# ── 8. Reload or start platform services, then restart middleware ──
# echo "Reloading services (or starting if they are down)..."
# if ! su -l vwadmin -c 'isystem reload' ; then
#   echo "starting via isystem option 2."
#   su -l vwadmin -c "printf '2\n' | isystem start"
# fi

# echo "Restarting ow-middleware service..."
# su -l vwadmin -c 'isystem s ow-middleware restart'

# echo "Waiting 30 seconds for middleware to initialise..."
# sleep 30

# echo "Verifying ow-middleware status..."
# if su -l vwadmin -c 'isystem list | grep -qE "ow-middleware.*RUNNING"' ; then
#   echo "ow-middleware is RUNNING – installation successful. Go to localhost:3000 to check"
# else
#   echo "ow-middleware failed to start. Run:"
#   echo "  su -l vwadmin -c '\''isystem list'\''"
#   echo "for details."
#   exit 1
# fi

# echo "Packages in ${TARGET_DIR}, configuration applied."

# ── 9. Install OW Apps ─────────────────────────────────────────────
APPS_DIR="/opt/OnlineWeather/apps"

echo "Creating OW Apps directory ${APPS_DIR} (if missing)…"
mkdir -p "${APPS_DIR}"
chown -R vwadmin:vwadmin "${APPS_DIR}"
echo "OW Apps directory ready."

echo "Discovering OW App packages under ${TARGET_DIR}…"
find "${TARGET_DIR}" -type f -name install.sh -path '*ow-*/*' | while read -r APP_INSTALL; do
  APP_DIR=$(dirname "${APP_INSTALL}")
  APP_BASE=$(basename "${APP_DIR}")
  APP_NAME=$(echo "${APP_BASE}" | cut -d'-' -f2)

  if [ "${APP_NAME}" = "backend" ]; then
    echo "Skipping backend (already installed)."
    continue
  fi

  DEST_DIR="${APPS_DIR}/ow-${APP_NAME}"
  if [ -d "${DEST_DIR}" ]; then
    echo "OW App '${APP_NAME}' already present – skipping."
    continue
  fi

  echo "Installing OW App '${APP_NAME}' from ${APP_DIR}…"
  chown -R vwadmin:vwadmin "${APP_DIR}"
  chmod +x "${APP_INSTALL}"
  su -l vwadmin -c "cd '${APP_DIR}' && ./install.sh --no-dry"
  echo "Finished installing '${APP_NAME}'."
done
echo "All OW Apps processed."

# ── 10. OW Help symlink ────────────────────────────────────────────
APPS_DIR="/opt/OnlineWeather/apps"
SRC_DIR="${APPS_DIR}/ow-help/help"
LINK_PATH="${APPS_DIR}/help"

echo "Creating symlink for OW Help…"
if [ -d "${SRC_DIR}" ]; then
  ln -sfn "${SRC_DIR}" "${LINK_PATH}"
  chown -h vwadmin:vwadmin "${LINK_PATH}"
  echo "Symlink ${LINK_PATH} → ${SRC_DIR} created."
else
  echo "WARNING: ${SRC_DIR} not found — OW Help may not be installed."
fi

# ── 11. Configure OW Landing page ──────────────────────────────────
LANDING_DIR="/opt/OnlineWeather/apps/ow-landing"
echo "Configuring OW Landing page in ${LANDING_DIR}…"
# Adding empty user config file to start
if [ -d "${LANDING_DIR}" ]; then
  cat > "${LANDING_DIR}/user-ow-landing-config.json" <<'EOF'
{}
EOF
  chown vwadmin:vwadmin "${LANDING_DIR}/user-ow-landing-config.json"
  echo " → Created ${LANDING_DIR}/user-ow-landing-config.json"
else
  echo "WARNING: OW Landing directory ${LANDING_DIR} not found; skipping landing setup."
fi