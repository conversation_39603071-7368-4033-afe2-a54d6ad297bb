#!/usr/bin/env ipython
# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    17.04.2013
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

import IBL.Chart as Chart
import IBL.Core as Core
import IBL.DB as DB
import IBL.Kernel as Kernel
import IBL.Print as Print
import IBL.Utils.DateTime as DateTime

import base64
import datetime

_gs_chartsRoot = "doc:groups/System/meteochart"
_gb_hasJSONExporter = hasattr(Chart, "ExporterJSON")
_g_options = None
if _gb_hasJSONExporter:
    _g_options = Chart.ExporterJSON.Options.ALL


def _getChartDocumentPath(s_dataType):
    path = Core.UniStoragePath("{}/{}/Default".format(_gs_chartsRoot, s_dataType))
    if path.exists():
        return path
    return None


if _gb_hasJSONExporter:

    def _createExporterConfiguration(f_zoom):
        configuration = Chart.ExporterConfiguration()
        configuration.zoom = f_zoom
        configuration.headerHeight = 8
        configuration.headerWidth = 45
        configuration.stationModelZoom = 1.1
        configuration.fontZoom = 1.25
        configuration.cellBaseWidth = 40
        configuration.cellBaseHeight = 10
        return configuration

    def _exportChartToCellPrint(chart, configuration, f_zoom, s_imageFormat):
        s_itemName = "TemporaryProduct"
        exporter = Chart.ExporterToCellPrint(configuration)
        product = DB.Product.Product.create("CellPrintToDataHelpingProduct")
        bmp = Print.CellPrint.createBitmap()
        bmp.setOutputProduct(product)
        bmp.setOutputProductFileNameTemplate(s_itemName)
        bmp.format = s_imageFormat
        f_dpi = 72 * f_zoom
        if f_dpi < 1.1:
            f_dpi = 1.1
        bmp.dpi = f_dpi
        i_width = int(configuration.getChartWidth(chart) * bmp.dpi / 25.4) + 2
        i_height = int(configuration.getChartHeight(chart) * bmp.dpi / 25.4) + 2
        if i_width > 4000 or i_height > 4000:  # some limitation of image output
            f_smallerFactor = 1.0
            if i_width > i_height:
                f_smallerFactor = 4000.0 / i_width
            else:
                f_smallerFactor = 4000.0 / i_height
            bmp.dpi = (bmp.dpi * f_smallerFactor) - 1
            i_width = int(configuration.getChartWidth(chart) * bmp.dpi / 25.4) + 2
            i_height = int(configuration.getChartHeight(chart) * bmp.dpi / 25.4) + 2
        if i_width < 40 or i_height < 40:  # avoid unreasonable small image
            f_enlargeFactor = 1.0
            if i_width < i_height:
                f_enlargeFactor = 40.0 / i_width
            else:
                f_enlargeFactor = 40.0 / i_height
            bmp.dpi = bmp.dpi * f_enlargeFactor
            i_width = int(configuration.getChartWidth(chart) * bmp.dpi / 25.4) + 2
            i_height = int(configuration.getChartHeight(chart) * bmp.dpi / 25.4) + 2
        bmp.width = i_width
        bmp.height = i_height
        bmp.quality = 100
        bmp.initPage(1)
        exporter.exportChartToCellPrint(chart, bmp)
        if product.containsBinaryData(s_itemName):
            imageData = product.getItemData(s_itemName)
        else:
            imageData = product.getItemText(s_itemName)
        s_b64Data = str(base64.b64encode(imageData), "ascii")
        return s_b64Data

    def _exportChart(
        s_format,
        i_tab,
        s_dataType,
        s_location=None,
        s_validity=None,
        f_zoom=None,
        s_imageFormat="PNG",
        i_imageWidth=None,
        i_imageHeight=None,
    ):
        if f_zoom is None:
            f_zoom = 1.0
        if s_imageFormat is None:
            s_imageFormat = "PNG"
        if i_imageWidth is None:
            i_imageWidth = 0
        if i_imageHeight is None:
            i_imageHeight = 0
        docPath = _getChartDocumentPath(s_dataType)
        if docPath is None or (s_format != "image" and s_format != "json"):
            return [0, ""]
        doc = Chart.Document()
        doc.loadFromStorage(docPath)
        chart = doc.getChart(i_tab)
        if s_location is not None:
            chart.setGlobalParameter(Chart.GlobalParameterIndex.globalParamStation, Kernel.mkStation(s_location))
        validity = (
            DB.toTime(s_validity) if s_validity is not None else DateTime.datetime2timestamp(datetime.datetime.utcnow())
        )
        chart.setGlobalParameter(Chart.GlobalParameterIndex.globalParamTime, Kernel.mkTime(validity))
        chart.evaluate()
        configuration = _createExporterConfiguration(f_zoom)
        if s_format == "json":
            exporter = Chart.ExporterJSON(
                configuration, s_format=s_imageFormat, i_imageWidth=i_imageWidth, i_imageHeight=i_imageHeight
            )
            return [i_tab, exporter.chartToDict(chart, _g_options)]
        else:
            return [i_tab, _exportChartToCellPrint(chart, configuration, f_zoom, s_imageFormat)]


def listSpotCharts():
    path = Core.UniStoragePath(_gs_chartsRoot)
    l_types = []
    for dirPath in path.listDirectoryPaths():
        s_type = dirPath.fileName()
        dirPath.append("Default")
        if dirPath.isFile():
            l_types.append(s_type)
    return l_types


def listChartTabs(s_dataType, dummy1=None, dummy2=None):
    docPath = _getChartDocumentPath(s_dataType)
    if docPath is None:
        return []
    doc = Chart.Document()
    doc.loadFromStorage(docPath)
    a_tabs = []
    for i in range(0, doc.getChartCount()):
        a_tabs.append(doc.getChart(i).getName())
    return a_tabs


if _gb_hasJSONExporter:

    def getChartAsImage(i_tab, s_dataType, s_location=None, s_validity=None, f_zoom=None, s_imageFormat=None):
        # Deprecated, use getChartAsImage instead
        return _exportChart("image", i_tab, s_dataType, s_location, s_validity, f_zoom, s_imageFormat=s_imageFormat)

    def getChartAsJSON(
        i_tab,
        s_dataType,
        s_location=None,
        s_validity=None,
        f_zoom=None,
        s_imageFormat=None,
        i_imageWidth=None,
        i_imageHeight=None,
    ):
        return _exportChart(
            "json", i_tab, s_dataType, s_location, s_validity, f_zoom, s_imageFormat, i_imageWidth, i_imageHeight
        )

    def getDocumentAsImage(s_dataType, s_location=None, s_validity=None, f_zoom=None, s_imageFormat=None):
        a_tabs = listChartTabs(s_dataType)
        if not a_tabs:
            return []
        l_charts = []
        for i in range(len(a_tabs)):
            l_charts.append(_exportChart("image", i, s_dataType, s_location, s_validity, f_zoom, s_imageFormat)[1])
        return l_charts

    def getDocumentAsJSON(
        s_dataType,
        s_location=None,
        s_validity=None,
        f_zoom=None,
        s_imageFormat=None,
        i_imageWidth=None,
        i_imageHeight=None,
    ):
        a_tabs = listChartTabs(s_dataType)
        if not a_tabs:
            return []
        l_charts = []
        for i in range(len(a_tabs)):
            l_charts.append(
                _exportChart(
                    "json", i, s_dataType, s_location, s_validity, f_zoom, s_imageFormat, i_imageWidth, i_imageHeight
                )[1]
            )
        return l_charts

    def getChartTabContent(i_tab, s_dataType, s_location=None, s_validity=None, f_zoom=None):
        # Deprecated, use getChartAsImage instead
        return _exportChart("image", i_tab, s_dataType, s_location, s_validity, f_zoom)


else:

    def getChartTabContent(i_tab, s_dataType, s_location, s_validity, f_zoom):
        # keeping here for backward compatibility because the presentations repository is still not versioned
        s_docPath = _getChartDocumentPath(s_dataType)
        if s_docPath is None:
            return [0, ""]
        s_itemName = "TemporaryProduct"
        doc = Chart.Document()
        doc.loadFromStorage(Core.UniStoragePath(s_docPath))
        doc.getChart(i_tab).setGlobalParameter(
            Chart.GlobalParameterIndex.globalParamStation, Kernel.mkStation(s_location)
        )
        doc.getChart(i_tab).setGlobalParameter(
            Chart.GlobalParameterIndex.globalParamTime, Kernel.mkTime(DB.toTime(s_validity))
        )
        doc.evaluate()

        chart = doc.getChart(i_tab)
        exporter = Chart.ExporterToCellPrint()
        exporter.setHeaderHeight(8)
        exporter.setHeaderWidth(45)
        exporter.setStationModelZoom(1.1)
        exporter.setFontZoom(1.25)
        exporter.setCellBaseWidth(40)
        exporter.setCellBaseHeight(10)

        product = DB.Product.Product.create("CellPrintToDataHelpingProduct")

        bmp = Print.CellPrint.createBitmap()
        bmp.setOutputProduct(product)
        bmp.setOutputProductFileNameTemplate(s_itemName)
        bmp.format = "PNG"
        f_dpi = 72 * f_zoom
        if f_dpi < 1.1:
            f_dpi = 1.1
        bmp.dpi = f_dpi
        i_width = int(exporter.getChartWidth(chart) * bmp.dpi / 25.4) + 2
        i_height = int(exporter.getChartHeight(chart) * bmp.dpi / 25.4) + 2

        if i_width > 4000 or i_height > 4000:  # some limitation of image output
            f_smallerFactor = 1.0
            if i_width > i_height:
                f_smallerFactor = 4000.0 / i_width
            else:
                f_smallerFactor = 4000.0 / i_height
            bmp.dpi = (bmp.dpi * f_smallerFactor) - 1
            i_width = int(exporter.getChartWidth(chart) * bmp.dpi / 25.4) + 2
            i_height = int(exporter.getChartHeight(chart) * bmp.dpi / 25.4) + 2
        if i_width < 40 or i_height < 40:  # avoid unreasonable small image
            f_enlargeFactor = 1.0
            if i_width < i_height:
                f_enlargeFactor = 40.0 / i_width
            else:
                f_enlargeFactor = 40.0 / i_height
            bmp.dpi = bmp.dpi * f_enlargeFactor
            i_width = int(exporter.getChartWidth(chart) * bmp.dpi / 25.4) + 2
            i_height = int(exporter.getChartHeight(chart) * bmp.dpi / 25.4) + 2
        bmp.width = i_width
        bmp.height = i_height

        bmp.quality = 100
        bmp.initPage(1)

        exporter.exportChartToCellPrint(chart, bmp)
        if product.containsBinaryData(s_itemName):
            imageData = product.getItemData(s_itemName)
        else:
            imageData = product.getItemText(s_itemName)
        s_b64Data = str(base64.b64encode(imageData), "ascii")
        return [i_tab, s_b64Data]


if __name__ == "__main__":
    import IBL.Widgets as Widgets

    if _gb_hasJSONExporter:
        _g_options = Chart.ExporterJSON.Options.DEBUG
        app = Widgets.Application()
        # info functions test
        print("spot charts:", listSpotCharts())
        print("METAR tabs:", listChartTabs("METAR"))
        # deprecated call test
        i_tab, s_data = getChartTabContent(2, "METAR")
        data = base64.b64decode(s_data)
        with open("metar-chart-deprecated.png", "wb") as fp:
            fp.write(data)
        print("saved metar-chart-deprecated.png")
        # export chart to image test
        i_tab, s_data = getChartAsImage(2, "METAR")
        data = base64.b64decode(s_data)
        with open("metar-chart.png", "wb") as fp:
            fp.write(data)
        print("saved metar-chart.png")
        # export chart to json test
        i_tab, d_chart = getChartAsJSON(2, "METAR")
        i_cells = 0
        for l_row in d_chart["rows"]:
            for cell in l_row:
                if "image" not in cell:
                    continue
                s_file = "metar-cell-{}-{}.{}".format(
                    cell["row-index"], cell["column-index"], cell["image"]["format"].lower()
                )
                data = base64.b64decode(cell["image"]["bitmap"])
                with open(s_file, "wb") as fp:
                    fp.write(data)
                i_cells += 1
        print("saved {} metar tab 2 cell PNG images".format(i_cells))
        # export chart to json test
        i_tab, d_chart = getChartAsJSON(2, "METAR", s_imageFormat="SVG")
        i_cells = 0
        for l_row in d_chart["rows"]:
            for cell in l_row:
                if "image" not in cell:
                    continue
                s_file = "metar-cell-{}-{}.{}".format(
                    cell["row-index"], cell["column-index"], cell["image"]["format"].lower()
                )
                data = base64.b64decode(cell["image"]["bitmap"])
                with open(s_file, "wb") as fp:
                    fp.write(data)
                i_cells += 1
        print("saved {} metar tab 2 cell SVG images".format(i_cells))
        # export document to images test
        l_images = getDocumentAsImage("METAR")
        i_chart = 0
        for s_data in l_images:
            data = base64.b64decode(s_data)
            with open("metar-{}.png".format(i_chart), "wb") as fp:
                fp.write(data)
            print("saved metar-{}.png".format(i_chart))
            i_chart += 1
        # export document to json test
        l_charts = getDocumentAsJSON("METAR")
        i_chart = 0
        for d_chart in l_charts:
            i_cells = 0
            for l_row in d_chart["rows"]:
                for cell in l_row:
                    if "image" not in cell:
                        continue
                    s_file = "metar-cell-{}-{}-{}.{}".format(
                        i_chart, cell["row-index"], cell["column-index"], cell["image"]["format"].lower()
                    )
                    data = base64.b64decode(cell["image"]["bitmap"])
                    with open(s_file, "wb") as fp:
                        fp.write(data)
                    i_cells += 1
            print("saved {} metar tab {} cell images".format(i_cells, i_tab))
            i_chart += 1
        del app
    else:
        print("No tests can be run on this version because of unversioned presentations repository!")