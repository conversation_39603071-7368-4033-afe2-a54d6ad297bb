# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    17.04.2013
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

import html, os, os.path, urllib.parse, sys, time
from xml.sax import saxutils
import IBL
import IBL.Core as C
import IBL.Core.Log as Log
import IBL.Core.UserManager as UM
import IBL.WebService as WS
from IBL.Net import apache

umInstance = UM.UserManager.getInstance()

try:
    umLicense = UM.checkLicense("license.ow.core")
except:
    umLicense = False

_gd_saveFormatsAllowed = None


class DocStoragePath:
    # root directory of all documents
    _s_documentRoot = "doc:global/onlineweather"
    # subdirectories of the root
    _s_global = "global"
    _s_groups = "groups"
    _s_users = "users"
    _d_topDirectories = frozenset((_s_global, _s_groups, _s_users))
    # error message sent when accessing documents of different user
    _s_userPathAccessError = "Access to documents belonging to other users is forbidden."

    def __init__(self, s_path, request=None):
        super().__init__()
        self.__s_pathRequested = s_path
        self.__s_pathResolved, self.__l_components = self.__resolve(s_path)
        if not self.valid:
            Log.clogError(
                "Cannot resolve '%s': resolved as '%s', components %s"
                % (self.__s_pathRequested, self.__s_pathResolved, str(self.__l_components))
            )

    def validate(self, request):
        if not self.valid:
            WS.sendJSONError(request, "Invalid path '%s'." % saxutils.escape(self.__s_pathRequested))
            return False
        return True

    def checkUser(self, s_userName, request=None):
        if self.valid and not self.isUserPath():
            return True
        if self.isUserPath() and len(self.__l_components) > 1 and self.__l_components[1] == s_userName:
            return True
        if request is not None:
            WS.sendJSONError(request, saxutils.escape(DocStoragePath._s_userPathAccessError))
        return False

    def isGlobalPath(self):
        return self.valid and self.__l_components[0] == DocStoragePath._s_global

    def isGroupPath(self):
        return self.valid and self.__l_components[0] == DocStoragePath._s_groups

    def isUserPath(self):
        return self.valid and self.__l_components[0] == DocStoragePath._s_users

    def getDocumentPath(self, s_documentType, request=None, b_mustExist=False):
        try:
            path = DocStoragePath.__getDocumentTypePath(s_documentType, not b_mustExist)
            if path is not None:
                path.append(self.__s_pathResolved)
                if DocStoragePath.__checkIfIsSubpath(path, DocStoragePath._s_documentRoot):
                    if not b_mustExist or path.exists():
                        return path
            if request is not None:
                WS.sendJSONError(request, "Path '%s' doesn't exist." % saxutils.escape(self.__s_pathRequested))
        except (C.StorageException, RuntimeError) as ex:
            if request is not None:
                WS.sendJSONError(request, "Invalid document type '{}'".format(saxutils.escape(s_documentType)))
        return None

    @staticmethod
    def getGlobalsDirs(s_documentType):
        s_ownershipPath = DocStoragePath._s_global
        uniPath = DocStoragePath.__getDocumentTypePathWithOwnership(s_documentType, s_ownershipPath)
        if uniPath.isNull() or not uniPath.exists():
            return []
        filesFull = uniPath.listEntryPaths(True)
        filesRelative = uniPath.listEntryPathsRelative(True)
        filesRelativeWithOwnership = []
        for i in filesRelative:
            filesRelativeWithOwnership.append(C.GenericPath(s_ownershipPath) + i)
        allfiles = list(zip(filesFull, filesRelativeWithOwnership))
        return allfiles

    @staticmethod
    def getGroupsDirs(s_documentType, groups):
        allpath = []
        for group in groups:
            s_ownershipPath = DocStoragePath._s_groups + "/" + group
            uniPath = DocStoragePath.__getDocumentTypePathWithOwnership(s_documentType, s_ownershipPath)
            if not uniPath.isNull() and uniPath.exists():
                filesFull = uniPath.listEntryPaths(True)
                filesRelative = uniPath.listEntryPathsRelative(True)
                filesRelativeWithOwnership = []
                for i in filesRelative:
                    filesRelativeWithOwnership.append(C.GenericPath(s_ownershipPath) + i)
                allpath = allpath + list(zip(filesFull, filesRelativeWithOwnership))
        return allpath

    @staticmethod
    def getUserDirs(s_documentType, user):
        s_ownershipPath = DocStoragePath._s_users + "/" + str(user)
        uniPath = DocStoragePath.__getDocumentTypePathWithOwnership(s_documentType, s_ownershipPath)
        if uniPath.isNull() or not uniPath.exists():
            return []
        filesFull = uniPath.listEntryPaths(True)
        filesRelative = uniPath.listEntryPathsRelative(True)
        filesRelativeWithOwnership = []
        for i in filesRelative:
            filesRelativeWithOwnership.append(C.GenericPath(s_ownershipPath) + i)
        allfiles = list(zip(filesFull, filesRelativeWithOwnership))
        return allfiles

    @staticmethod
    def __getDocumentTypePathWithOwnership(s_documentType, s_ownershipPath):
        path = C.UniStoragePath(DocStoragePath._s_documentRoot + "/" + s_documentType + "/" + s_ownershipPath)
        if not DocStoragePath.__checkIfIsSubpath(path, DocStoragePath._s_documentRoot):
            return C.UniStoragePath()
        return path

    @staticmethod
    def __getDocumentTypePath(s_documentType, b_createDirectory=True):
        path = C.UniStoragePath(DocStoragePath._s_documentRoot + "/" + s_documentType)
        if not DocStoragePath.__checkIfIsSubpath(path, DocStoragePath._s_documentRoot):
            return None
        if b_createDirectory and not path.exists():
            path.createDirectory()
        return path

    @staticmethod
    def __checkIfIsSubpath(subPath, s_parentDir):
        subPath.makeCanonical()
        a_subPath = str(subPath).split("/")
        a_parentDir = s_parentDir.split("/")
        i_commonBits = 0
        while i_commonBits < len(a_subPath) and i_commonBits < len(a_parentDir):
            if a_subPath[i_commonBits] == a_parentDir[i_commonBits]:
                i_commonBits += 1
            else:
                break
        return i_commonBits >= len(a_parentDir)

    def __resolve(self, s_path):
        s_pathResolved = None
        l_components = []
        if s_path is not None:
            s_pathResolved = os.path.normpath(os.path.join("/", s_path))
            if sys.platform == "win32":
                s_pathResolved = s_pathResolved.replace("\\", "/")
            Log.clogDebug(2, "Resolved {} to {}".format(s_path, s_pathResolved))
            if s_pathResolved:
                s_pathResolved = s_pathResolved.lstrip("/")
                l_components = s_pathResolved.split("/")
        else:
            Log.clogError("Cannot resolve null path.")
        return s_pathResolved, l_components

    @property
    def null(self):
        return self.__s_pathResolved is None

    @property
    def valid(self):
        return not self.null and self.__l_components and self.__l_components[0] in DocStoragePath._d_topDirectories

    @property
    def requested(self):
        return self.__s_pathRequested

    @property
    def resolved(self):
        return self.__s_pathResolved


def __str2bool(v):
    return str(v).lower() in ("yes", "true", "t", "1")


def index(req):
    req.headers_out.add("Content-Type", "text/html")
    req.write("Visual Weather DocStorage wrapper for Online Weather.")
    return apache.OK


def version(req, product=True, version=True, buildDate=None):
    req.headers_out.add("Content-Type", "text/plain")
    s = ""
    if __str2bool(product):
        s += C.getSystemProject()
    if __str2bool(version):
        if len(s):
            s += " Version "
        s += C.getSystemVersion()
    if __str2bool(buildDate):
        if len(s):
            s += " Built "
        path = IBL.basedPath("bin/iwebservice")
        f_dt = None
        if os.path.exists(path):
            f_dt = os.path.getmtime(path)
        path = IBL.basedPath("bin/iwebservice.exe")
        if os.path.exists(path):
            f_dt = os.path.getmtime(path)
        if f_dt is not None:
            s += time.strftime("%d.%m.%Y", time.localtime(f_dt))
    return s


def login(req, redirect=None, oldUser=None, referer=None):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    WS.disableResponseCaching(req)
    user, s_userName = _checkUser(req)
    if user is None or (oldUser is not None and req.user == oldUser):
        WS.sendAuthorizationRequired(req)
        return apache.HTTP_UNAUTHORIZED
    if redirect is not None:
        if referer is not None:
            redirect = urllib.parse.urljoin(referer, redirect)
        req.headers_out.add("Location", redirect.translate(None, "\r\n"))
        redirect = html.escape(redirect, True)
        if not redirect.startswith("/"):
            WS.sendJSONError(req, "Redirection allowed to local path only.")
            return apache.HTTP_UNAUTHORIZED
        WS.sendSimpleHTML(
            req, "Logged in", 'You should be redirected to <a href="%s">%s</a> automatically.' % (redirect, redirect)
        )
        return apache.HTTP_SEE_OTHER
    whoami(req)
    return apache.OK


def redirect(req, to):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    if to is not None and not to.startswith("/"):
        WS.sendJSONError(req, "Redirection allowed to local path only.")
        return apache.HTTP_UNAUTHORIZED

    WS.disableResponseCaching(req)
    if "Referer" in req.headers_in:
        u = list(urllib.parse.urlparse(to))
        if u[4]:
            u[4] += "&"
        u[4] += "referer=" + urllib.parse.quote(req.headers_in["Referer"])
        to = urllib.parse.urlunparse(u)
    req.headers_out.add("Location", to.translate(None, "\r\n"))
    to = cgi.escape(to, True)
    WS.sendSimpleHTML(req, "Redirection", 'You should be redirected to <a href="%s">%s</a> automatically.' % (to, to))
    return apache.HTTP_SEE_OTHER


def logoff(req, oldUser=None):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    if oldUser is not None and req.user != oldUser:
        user, s_userName = _checkUser(req)
        if user is not None:
            whoami(req)
            return apache.OK
    WS.sendAuthorizationRequired(req, "Press cancel to log off")
    return apache.HTTP_UNAUTHORIZED


def whoami(req):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    user, s_userName = _checkUser(req)
    if user is None:
        WS.sendJSONSuccess(req, {"account": None, "groups": []})
        return apache.OK

    a_groups = user.getEffectiveGroupMembership(umInstance)
    WS.sendJSONSuccess(req, {"account": req.user, "groups": a_groups})
    return apache.OK


def listAll(req, documentType):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    user, s_userName = _checkUser(req)
    if _isDocumentTypeOk(documentType):
        a_groups = user.getEffectiveGroupMembership(umInstance) if user is not None else []
        WS.sendJSONSuccess(req, _getPathStructure(documentType, req.user if user is not None else None, a_groups))
    else:
        WS.sendJSONError(req, "Unknown document type '%s'." % saxutils.escape(documentType))

    return apache.OK


def save(req, documentType, path, data):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED
    user, s_userName = _checkUser(req)
    if user is None:
        WS.sendAuthorizationRequired(req, "Press cancel to log off")
        return apache.HTTP_UNAUTHORIZED

    path = DocStoragePath(path)
    if not path.validate(req):
        return apache.HTTP_FORBIDDEN

    if not _checkDataFormat(req, data):
        WS.sendJSONError(req, "Unsupported data format.")
        return apache.HTTP_UNSUPPORTED_MEDIA_TYPE
    if _isDocumentTypeOk(documentType):
        if not path.checkUser(s_userName, req):
            return apache.HTTP_FORBIDDEN
        unipath = path.getDocumentPath(documentType, req)
        if unipath is not None:
            _storeDataToStorageFile(data, unipath)
            WS.sendJSONSuccess(req)
    else:
        WS.sendJSONError(req, "Unknown document type '%s'." % saxutils.escape(documentType))

    return apache.OK


def load(req, documentType, path):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    path = DocStoragePath(path)
    if not path.validate(req):
        return apache.HTTP_FORBIDDEN

    user, s_userName = _checkUser(req)
    if not path.isGlobalPath():
        if user is None:
            WS.sendAuthorizationRequired(req, "Press cancel to log off")
            return apache.HTTP_UNAUTHORIZED
    if _isDocumentTypeOk(documentType):
        if not path.checkUser(s_userName, req):
            return apache.HTTP_FORBIDDEN
        unipath = path.getDocumentPath(documentType, req, True)
        if unipath is not None:
            if unipath.isDirectory():
                WS.sendJSONError(req, "Path '%s' is a directory." % saxutils.escape(path.requested))
            else:
                s_data = _loadDataFromStorageFile(unipath)
                WS.sendJSONSuccess(req, {"path": path.resolved, "data": s_data.decode("UTF-8")})
    else:
        WS.sendJSONError(req, "Unknown document type '%s'." % saxutils.escape(documentType))

    return apache.OK


def remove(req, documentType, path):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    path = DocStoragePath(path)
    if not path.validate(req):
        return apache.HTTP_FORBIDDEN

    user, s_userName = _checkUser(req)
    if user is None:
        WS.sendAuthorizationRequired(req, "Press cancel to log off")
        return apache.HTTP_UNAUTHORIZED

    if _isDocumentTypeOk(documentType):
        if not path.checkUser(s_userName, req):
            return apache.HTTP_FORBIDDEN
        unipath = path.getDocumentPath(documentType, req, True)
        if unipath is not None:
            unipath.remove(True)
            WS.sendJSONSuccess(req)
    else:
        WS.sendJSONError("Unknown document type '%s'." % saxutils.escape(documentType))
    return apache.OK


def rename(req, documentType, path_from, path_to):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    path_from = DocStoragePath(path_from)
    if not path_from.validate(req):
        return apache.HTTP_FORBIDDEN
    path_to = DocStoragePath(path_to)
    if not path_to.validate(req):
        return apache.HTTP_FORBIDDEN

    user, s_userName = _checkUser(req)
    if user is None:
        WS.sendAuthorizationRequired(req, "Press cancel to log off")
        return apache.HTTP_UNAUTHORIZED

    if _isDocumentTypeOk(documentType):
        for path in path_from, path_to:
            if not path.checkUser(s_userName, req):
                return apache.HTTP_FORBIDDEN
        unipath_from = path_from.getDocumentPath(documentType, req, True)
        unipath_to = path_to.getDocumentPath(documentType, req)
        if unipath_from is not None:
            if unipath_to is not None and not unipath_to.exists():
                try:
                    unipath_from.rename(unipath_to)
                    WS.sendJSONSuccess(req)
                except:
                    WS.sendJSONError(
                        req,
                        "Attempt to rename '%s' to '%s' failed, exception occurred."
                        % (saxutils.escape(path_from.requested), saxutils.escape(path_to.requested)),
                    )
            else:
                WS.sendJSONError(
                    req, "Target path '%s' already exists, cannot rename into it." % saxutils.escape(path_to.requested)
                )
    else:
        WS.sendJSONError(req, "Unknown document type '%s'." % saxutils.escape(documentType))

    return apache.OK


def createDirectory(req, documentType, path):
    if not umLicense:
        WS.sendJSONError(req, "Missing OW core license.")
        return apache.HTTP_UNAUTHORIZED

    path = DocStoragePath(path)
    if not path.validate(req):
        return apache.HTTP_FORBIDDEN

    user, s_userName = _checkUser(req)
    if user is None:
        WS.sendAuthorizationRequired(req, "Press cancel to log off")
        return apache.HTTP_UNAUTHORIZED

    if _isDocumentTypeOk(documentType):
        if not path.checkUser(s_userName, req):
            return apache.HTTP_FORBIDDEN
        unipath = path.getDocumentPath(documentType, req)
        if unipath is not None and not unipath.exists():
            unipath.createDirectory()
            WS.sendJSONSuccess(req)
        else:
            WS.sendJSONError(
                req, "DocStorage directory or file name '%s' already exists." % saxutils.escape(path.requested)
            )
    else:
        WS.sendJSONError(req, "Unknown document type '%s'." % saxutils.escape(documentType))

    return apache.OK


def _checkUser(req):
    if not req.user:
        return None, None
    try:
        return umInstance.getUserByName(req.user), req.user
    except:
        return None, None


def _isDocumentTypeOk(s_documentType):
    if s_documentType == "test":
        return False
    return True


def _getPathStructure(docType, s_userName, a_groups):
    a_files = DocStoragePath.getGlobalsDirs(docType)
    if s_userName is not None:
        a_files += DocStoragePath.getUserDirs(docType, s_userName)
    a_files += DocStoragePath.getGroupsDirs(docType, a_groups)

    a = []
    for p in a_files:
        if p[0].isFile():
            a.append({"type": "file", "name": str(p[1])})  # str() may be superfluous (2to3)
        elif p[0].isDirectory():
            a.append({"type": "directory", "name": str(p[1])})  # str() may be superfluous (2to3)
    return a


def _checkDataFormat(req, data):
    global _gd_saveFormatsAllowed

    if _gd_saveFormatsAllowed is None:
        _gd_saveFormatsAllowed = set()
        s_accept = req.service_attributes["accept"] if "accept" in req.service_attributes else "application/json"
        l_formats = s_accept.split(";")
        for s_format in l_formats:
            if s_format == "*/*":
                _gd_saveFormatsAllowed.add("json")
                _gd_saveFormatsAllowed.add("xml")
                break
            if s_format == "application/json":
                _gd_saveFormatsAllowed.add("json")
            elif s_format == "application/xml" or s_format == "text/xml":
                _gd_saveFormatsAllowed.add("xml")
            else:
                # invalid format error - log only
                Log.clogError("Unknown format '{}' specified in `accept` attribute.".format(s_format))
    if not _gd_saveFormatsAllowed:
        return True
    b_accept = False
    if "json" in _gd_saveFormatsAllowed:
        import json

        try:
            json.loads(data)
            b_accept = True
        except json.decoder.JSONDecodeError as ex:
            pass
        except Exception as ex:
            Log.clogError("Unexpected exception when trying to detect JSON data: {}".format(str(ex)))
    if not b_accept and "xml" in _gd_saveFormatsAllowed:
        import xml.dom.minidom as dom
        from xml.parsers.expat import ExpatError

        try:
            document = dom.parseString(data)
            b_accept = True
        except ExpatError as ex:
            pass
        except Exception as ex:
            Log.clogError("Unexpected exception when trying to detect json data: {}".format(str(ex)))
    return b_accept


def _loadDataFromStorageFile(uniPath):
    f = C.DomFile(uniPath)
    return f.data()


def _storeDataToStorageFile(s_data, uniPath):
    f = C.DomFile()
    f.setData(s_data.encode("UTF-8"))
    f.storeToStorage(uniPath)