#!/usr/bin/env ipython
# -*- coding: UTF-8 -*-
# APPLICATION: Iwebservice Ria Dbase
#################################################################################################
##
##  Created:    17.04.2013
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

import IBL.Core as Core
import IBL.Geo as Geo
import IBL.DB as DB
import IBL.BCP as BCP
import IBL.Kernel as Kernel
import IBL.WebService.Decorators as wsd
from IBL.Net import apache

import base64
import os
import re
import time

gb_qt6 = False
try:
    import PyQt6.QtCore as QtCore
    import PyQt6.QtGui as QtGui
    gb_qt6 = True
    os.environ["QT_IMAGEIO_MAXALLOC"] = "1024"
except ImportError:
    import PyQt5.QtCore as QtCore
    import PyQt5.QtGui as QtGui

import datetime
import math

from typing import Dict

gs_binaryT1 = ""
try:
    from IBL.DB import Structs

    com = Structs.Communication()
    com.loadFromStorage(Structs.Communication.getGlobalPath())
    gs_binaryT1 = com.t1Binary

except:
    gs_binaryT1 = ""


def listMessages(headingOrList, maxResultMessages=1000, scanBackDays=0, b_fastTestScan=True):
    import IBL.Utils.DateTime as DateTime

    tsReferenceMidnight = time.time() // 86400 * 86400
    if isinstance(headingOrList, (list, tuple)):
        l_headings = headingOrList[:]
    else:
        l_headings = (headingOrList,)
    d_days = set()
    a_days = []
    a = [tsReferenceMidnight - 86400 * i for i in range(0, 31)]
    a.insert(1, tsReferenceMidnight + 86400)
    for s_heading in l_headings:
        s_yy = s_heading.split(" ")[2][0:2]
        if b_fastTestScan and s_yy == "//":
            d_days.add(tsReferenceMidnight)
        else:
            yyRegExp = re.compile(s_yy.replace("/", "."))
            # start searching for day matching YY pattern from tomorrow
            # build test sequence [today, tomorrow, today-1, today-2, ...]
            for tsDayMidnight in a:
                s_yyActual = Core.formatTime(tsDayMidnight, "dd")
                if yyRegExp.match(s_yyActual):
                    if b_fastTestScan:
                        d_days.add(tsDayMidnight)
                    else:
                        a_days.append(tsDayMidnight)
    if b_fastTestScan:
        a_days = sorted(d_days, reverse=True)
    headingList = [DB.CondensedHeader(heading) for heading in l_headings]
    a_result = []
    for tsDayMidnight in a_days:
        _maxResultMessages = maxResultMessages - len(a_result)
        l = DB.findMultiHeaderMessageByDay(headingList, tsDayMidnight, _maxResultMessages + 1, scanBackDays)
        for messageEntry in l[:_maxResultMessages]:
            a_result.append(
                {
                    "id": DB.MessageIdent.toStringKey(messageEntry.message),
                    "heading": messageEntry.heading,
                    "size": messageEntry.size,
                    "time": str(Core.DateTime(messageEntry.time)),
                    "receptionTime": str(Core.DateTime(messageEntry.received)),
                }
            )
        if len(l) > _maxResultMessages:
            a_result.append("More messages available")
            break
    return a_result


def detectDataFormat(s_msg):
    # skip heading
    b_string = isinstance(s_msg, str)
    i_start = s_msg.find("\n" if b_string else b"\n") + 1
    s_heading = s_msg[:i_start].rstrip()
    s_data = s_msg[i_start:]
    if b_string:
        s_data = bytes(s_msg, "utf-8")
    s_sourceFormat = None
    # PNG and JPEG can be returned straight back without recoding
    img = QtGui.QImage.fromData(s_data, "PNG")
    if not img.isNull():
        return (s_heading, "image/png", s_data, "PNG")
    img = QtGui.QImage.fromData(s_data, "JPEG")
    if not img.isNull():
        return (s_heading, "image/jpeg", s_data, "JPEG")
    # T4 and BUFR must be tested explicitly and conversion to PNG is needed
    if s_data.startswith(b"DFAX"):
        s_sourceFormat = "T4"
        img = QtGui.QImage.fromData(s_data, "T4")
    elif s_data.startswith(b"BUFR"):
        s_sourceFormat = "BUFR"
        img = QtGui.QImage.fromData(s_data, "BUFR")
    else:
        # Neither T4 nor BUFR - try automatic format detection
        img = QtGui.QImage.fromData(s_data)
    if not img.isNull():
        buf = QtCore.QBuffer()
        if gb_qt6:
            buf.open(QtCore.QIODevice.OpenModeFlag.WriteOnly)
        else:
            buf.open(QtCore.QIODevice.WriteOnly)
        img.save(buf, "PNG")
        return (s_heading, "image/png", buf.buffer().data(), s_sourceFormat)
    if s_sourceFormat is None:
        s_sourceFormat = s_data[:8]
    return (s_heading, None, s_data, s_sourceFormat)


def getMessage(id, rawData=False, returnBinaryAsURL=False, b_returnSourceFormat=False):
    message = DB.readMessage(DB.MessageIdent.fromStringKey(id))
    if not message:
        return None
    try:
        s_msg = str(bytes(message), encoding="UTF-8")
    except UnicodeDecodeError:
        s_msg = bytes(message)
    s_format = None
    b_binary = False
    if rawData:
        s_heading = s_msg[: s_msg.find("\n") + 1]
    else:
        (s_heading, s_format, s_data, s_sourceFormat) = detectDataFormat(s_msg)
        b_binary = True
    if isinstance(s_heading, bytes):
        s_heading = str(s_heading, "ascii")
    if s_format is None:
        if s_heading[0] in gs_binaryT1:
            s_format = "application/octet-stream"
            b_binary = True
            # for binary data remove heading
        else:
            s_format = "text/plain"
            s_data = s_msg
    if b_binary and returnBinaryAsURL:
        if b_returnSourceFormat:
            return {"format": s_format, "uri": "getBinaryMessage?id=%s" % id, "source-format": s_sourceFormat}
        else:
            return {"format": s_format, "uri": "getBinaryMessage?id=%s" % id}
    b_needsEncoding = False
    if isinstance(s_data, bytes):
        try:
            s_data = str(s_data, "ascii")
        except:
            b_needsEncoding = True
    if not b_needsEncoding:
        s_encoding = "none"
    else:
        s_encoding = "base64"
        if isinstance(s_data, str):
            s_data = bytes(s_data, "utf-8")
        s_data = str(base64.b64encode(s_data), "ascii")
    d = {"format": s_format, "encoding": s_encoding, "data": s_data}
    if b_returnSourceFormat:
        d["source-format"] = s_sourceFormat
    return d


def getMessages(*args):
    l_results = []
    for messageID in args:
        s_msg = DB.readMessage(DB.MessageIdent.fromStringKey(messageID))
        if s_msg.isNull():
            return None
        s_msg = s_msg.toString()
        s_format = None
        b_binary = False
        (s_heading, s_format, s_data, s_sourceFormat) = detectDataFormat(s_msg)
        b_binary = True
        if s_format is None:
            if s_heading[0] in gs_binaryT1:
                s_format = "application/octet-stream"
                b_binary = True
                # for binary data remove heading
            else:
                s_format = "text/plain"
                s_data = s_msg
        try:
            str(s_data, "ascii")
            b_needsEncoding = False
        except:
            b_needsEncoding = True
        if not b_needsEncoding:
            s_encoding = "none"
        else:
            s_encoding = "base64"
            s_data = base64.b64encode(s_data)

        d = {"format": s_format, "data": s_data}
        l_results += (d,)
    return l_results


@wsd.calling_convention("publisher")
def getBinaryMessage(request, id):
    s_msg = DB.readMessage(DB.MessageIdent.fromStringKey(id))
    if s_msg.isNull():
        return None
    s_msg = s_msg.toString()
    (s_heading, s_format, s_data, s_sourceFormat) = detectDataFormat(s_msg)
    if s_format is None:
        s_format = "application/octet-stream"
    request.headers_out.add("Content-Type", s_format)
    request.headers_out.add("Content-Length", str(len(s_data)))
    request.write(s_data)
    return apache.OK


def _listMessageHeadingFilters(l, path):
    for subPath in sorted(path.listDirectoryPaths(), key=lambda p: p.fileName()):
        subl = []
        _listMessageHeadingFilters(subl, subPath)
        l.append({"name": subPath.fileName(), "type": "folder", "children": subl})
    for entry in sorted(path.listFilePaths(), key=lambda p: p.fileName()):
        l.append({"name": entry.fileName(), "type": "item", "uri": str(entry)})
    return l


def listMessageHeadingFilters():
    l = []
    _listMessageHeadingFilters(l, Core.UniStoragePath("doc:global/filters"))
    return l


def getMessageHeadingFilter(path):
    hf = DB.HeadingFilter.loadFromStorage(Core.UniStoragePath(path))
    return {"filters": hf.filters}


def _listPlaceLists(l, path):
    if not path.exists():
        return []
    for subPath in sorted(path.listDirectoryPaths(), key=lambda p: p.fileName()):
        subl = []
        _listMessageHeadingFilters(subl, subPath)
        l.append({"name": subPath.fileName(), "type": "folder", "children": subl})
    for entry in sorted(path.listFilePaths(), key=lambda p: p.fileName()):
        l.append({"name": entry.fileName(), "type": "item", "uri": str(entry)})
    return l


def listPlaceLists():
    l = []
    _listPlaceLists(l, Core.UniStoragePath("doc:global/placelists"))
    return l


def _placeToPlaceInfo(pce):
    i = {"id": pce.id}
    i["name"] = pce.name
    if pce.icao is not None:
        i["icao"] = pce.icao
    return i


def _placeIdToPlaceInfo(s_placeId):
    pc = Geo.PlaceCatalog.getInstance()
    pce = pc.findById(s_placeId)
    if pce is None:
        return None
    return _placeToPlaceInfo(pce)


def getPlaceList(path):
    pl = Geo.PlaceList()
    pl.loadFromStorage(Core.UniStoragePath(path))
    l = []
    for s_placeId in pl.items:
        i = _placeIdToPlaceInfo(s_placeId)
        l.append(i)
    return {"items": l}


def listPlaceIdsMatchingICAO(s_icaoPattern):
    pc = Geo.PlaceCatalog.getInstance()
    l = []
    regexp = re.compile(s_icaoPattern.replace("/", "."))
    filter = Geo.PlaceCatalogSearchFilter(Geo.scff.ICAO)
    for pce in pc.iterByFilter(filter):
        if regexp.match(pce.icao):
            l.append(_placeToPlaceInfo(pce))
    return l


def getPlaceInfo(s_placeId):
    return _placeIdToPlaceInfo(s_placeId)


def listAirportsInCSV():
    pcat = Geo.PlaceCatalog.getInstance()
    filterAirports = Geo.PlaceCatalogSearchFilter(Geo.scff.AIRPORT)
    s_result = "ICAO\tICAO2\tIATA\tName\tlatitude\tlongitude\tARPTId\n"
    i_count = 0
    for place in pcat.iterByFilter(filterAirports):
        loc = place.location
        s_location = "" if loc is None else "%g\t%g" % (loc.la, loc.lo)
        s_result += "%s\t\t%s\t%s\t%s\t\n" % (
            "" if place.icao is None else place.icao,
            "" if place.iata is None else place.iata,
            "" if place.name is None else place.name,
            s_location,
        )
        i_count += 1
    return s_result


def queryBCP(a_placeIds, a_reportTypes, s_queryMode, b_includeNIL=1, s_referenceDateTime=None):
    if s_referenceDateTime is None:
        referenceDateTime = int(time.time())
    else:
        referenceDateTime = DB.toTime(s_referenceDateTime)

    try:
        fncColorizeMETAR = Kernel.Expression(
            "@body @time l'colour-code.uk-raf.calculated' Fclassify_and_colorize_metar$ltl$l"
        )
        fncColorizeTAF = Kernel.Expression(
            "@body @time l'colour-code.uk-raf.calculated' Fclassify_and_colorize_taf$ltl$l"
        )
    except:
        # workaround for older VWs which don't have colour coding
        fncColorizeMETAR = None
        fncColorizeTAF = None

    l = []
    for s_reportType in a_reportTypes:
        a = s_reportType.split("/", 2)
        i_primaryReportType = getattr("DB.mt.MSG_" + a[0])
        i_secondaryReportType = getattr("DB.mt.MSG_" + a[1]) if len(a) > 1 else DB.mt.MSG_NONE

        a = s_queryMode.split("/", 2)
        i_searchType = getattr("BCP.st." + a[1]) if len(a) > 1 else BCP.st.VALID

        searchRule = BCP.SearchRule(referenceDateTime, i_primaryReportType, i_secondaryReportType, i_searchType)
        if a[0] == "AreaCompiler":
            compiler = BCP.AreaCompiler(searchRule)
        else:
            raise Exception("Unsupported BCP compiler '%s'" % a[0])

        for s_placeId in a_placeIds:
            if s_placeId.startswith("icao:"):
                sid = Geo.StationId.fromICAO(s_placeId[5:])
            elif s_placeId.startswith("wmo:"):
                sid = Geo.StationId.fromWMO(int(s_placeId[4:]))
            else:
                continue  # raise Exception("Unsupported PlaceId '%s'" % s_placeId)
            compiler.addStation(sid)
        compiler.update()

        for entry in compiler:
            i = {
                "reportType": s_reportType,
                "stationId": str(entry.station),
                "placeId": entry.station.getCatalogPrimaryKey(),
            }
            if entry.isNil():
                if not b_includeNIL:
                    continue
                i["text"] = "NIL="
            else:
                i["reportType"] = str(entry.bcpElemMessages()[0].msgType)[4:]
                if i["reportType"] == "LONGTAF":
                    i["reportType"] = "TAF"
                s_text = entry.getText()
                i["text"] = s_text
                i["reportTime"] = str(Core.DateTime(entry.getReportTime()))
                if fncColorizeMETAR is not None and entry.bcpElemMessages()[0].msgType in [
                    DB.mt.MSG_METAR,
                    DB.mt.MSG_SPECI,
                ]:
                    fncColorizeMETAR.setArgument("body", Kernel.mkText(s_text))
                    fncColorizeMETAR.setArgument("time", Kernel.mkTime(entry.getReportTime()))
                    i["textHTML"] = fncColorizeMETAR.eval().toText()
                if fncColorizeTAF is not None and entry.bcpElemMessages()[0].msgType in [
                    DB.mt.MSG_TAF,
                    DB.mt.MSG_LONGTAF,
                ]:
                    fncColorizeTAF.setArgument("body", Kernel.mkText(s_text))
                    fncColorizeTAF.setArgument("time", Kernel.mkTime(entry.getReportTime()))
                    i["textHTML"] = fncColorizeTAF.eval().toText()
            s_revisionType = None
            if entry.isAMD():
                s_revisionType = "AMD"
            elif entry.isCOR():
                s_revisionType = "COR"
            elif entry.isRTD():
                s_revisionType = "RTD"
            if s_revisionType is not None:
                i["revisionType"] = s_revisionType
            l.append(i)
    # sort finally according the original order
    return sorted(l, key=lambda i: a_placeIds.index(i["placeId"]))


def _addRevisionType(i, entry):
    s_revisionType = None
    if entry.isAMD():
        s_revisionType = "AMD"
    elif entry.isCOR():
        s_revisionType = "COR"
    elif entry.isRTD():
        s_revisionType = "RTD"
    if s_revisionType is not None:
        i["revisionType"] = s_revisionType


def _convertNewlinesWMOtoNormal(report):
    return report.getText().replace("\r\r\n", "\n")


def _anyValidMsg(warning, i_timeNow):
    for msg in warning.bcpElemMessages():
        if msg.validTo >= i_timeNow:
            return True
    return False


def _queryWarningCompiler(referenceDateTime, a_firCodes, a_countryCodes, i_msgType, i_searchType):
    searchRule = BCP.SearchRule(referenceDateTime, i_msgType, i_searchType)
    compiler = BCP.WarningCompiler()
    compiler.setSearchRule(searchRule)
    if a_firCodes:
        for s_firCode in a_firCodes:
            compiler.addFIRFilter(Geo.StationId(s_firCode))
    if a_countryCodes:
        for s_countryCode in a_countryCodes:
            compiler.addBulletinFilter(s_countryCode)
    compiler.update()
    l_result = []
    for warning in compiler:
        for msg in warning.bcpElemMessages():
            if msg.validTo >= referenceDateTime:
                i = {}
                i["reportTime"] = str(Core.DateTime(warning.getReportTime()))
                i["reportType"] = str(msg.msgType)[4:]
                i["text"] = warning.getText()
                i["stationId"] = str(warning.station)
                i["validFrom"] = str(Core.DateTime(msg.validFrom))
                i["validTo"] = str(Core.DateTime(msg.validTo))
                i["placeId"] = warning.station.getCatalogPrimaryKey()
                _addRevisionType(i, warning)
                l_result.append(i)
    return l_result


def _queryAreaCompiler(referenceDateTime, a_placeIds, i_msgTypePrimary, i_msgTypeSecondary, i_searchType, b_includeNIL):
    try:
        fncColorizeMETAR = Kernel.Expression(
            "@body @time l'colour-code.uk-raf.calculated' Fclassify_and_colorize_metar$ltl$l"
        )
        fncColorizeTAF = Kernel.Expression(
            "@body @time l'colour-code.uk-raf.calculated' Fclassify_and_colorize_taf$ltl$l"
        )
    except:
        # workaroud for VWs which don't have colour coding
        fncColorizeMETAR = None
        fncColorizeTAF = None

    l = []
    searchRule = BCP.SearchRule(referenceDateTime, i_msgTypePrimary, i_msgTypeSecondary, i_searchType)
    compiler = BCP.AreaCompiler(searchRule)

    for s_placeId in a_placeIds:
        if s_placeId.startswith("icao:"):
            sid = Geo.StationId.fromICAO(s_placeId[5:])
        elif s_placeId.startswith("wmo:"):
            sid = Geo.StationId.fromWMO(int(s_placeId[4:]))
        else:
            raise Exception("Unsupported PlaceId '%s'" % s_placeId)
        compiler.addStation(sid)
    compiler.update()

    for entry in compiler:
        i = {
            "reportType": str(i_msgTypePrimary)[4:],
            "stationId": str(entry.station),
            "placeId": entry.station.getCatalogPrimaryKey(),
        }
        if entry.isNil():
            if not b_includeNIL:
                continue
            i["text"] = "NIL="
        else:
            s_text = entry.getText()
            i["text"] = s_text
            i["reportTime"] = str(Core.DateTime(entry.getReportTime()))
            bcpElemMessages = entry.bcpElemMessages()
            i["reportType"] = str(bcpElemMessages[0].msgType)[4:]
            if fncColorizeMETAR is not None and bcpElemMessages[0].msgType in [DB.mt.MSG_METAR, DB.mt.MSG_SPECI]:
                fncColorizeMETAR.setArgument("body", Kernel.mkText(s_text))
                fncColorizeMETAR.setArgument("time", Kernel.mkTime(entry.getReportTime()))
                i["textHTML"] = fncColorizeMETAR.eval().toText()
            if fncColorizeTAF is not None and bcpElemMessages[0].msgType in [DB.mt.MSG_TAF, DB.mt.MSG_LONGTAF]:
                fncColorizeTAF.setArgument("body", Kernel.mkText(s_text))
                fncColorizeTAF.setArgument("time", Kernel.mkTime(entry.getReportTime()))
                i["textHTML"] = fncColorizeTAF.eval().toText()
        if i["reportType"] == "LONGTAF":
            i["reportType"] = "TAF"
        _addRevisionType(i, entry)
        l.append(i)

    # sort finally according the original order
    return sorted(l, key=lambda i: a_placeIds.index(i["placeId"]))


""" Query for valid/latest warnings according to warning types, FIR area codes and country (AA) codes

    warningTypes:
        AIRMET:     AIRMETs in WA messages, query by FIR code
        SIGMET:     Normal SIGMET (not TC/VA)
        VULCANIC:   WV SIGMET ("vulcanic" is an unfortunate typo in VW code)
        CYCLONE:    WC SIGMET
        WARNING_TROPICAL_CYCLONE: WT messages
        GAMET:      FA messages
        VULCANIC_ASH_ADVISORY: FV messages
        TROPICAL_CYCLONE_ADVISORY: FK messages

    s_searchType:
        VALID
        LATEST
"""


def sunriseSunsetPhases(jsonData: Dict):
    """
    Input: JSON
    {
        "locations":
        {
            [
                {
                    "name": "Bratislava",
                    "lat": 48.1486,
                    "lon": 17.1077,lat
                    "altitude": 130,
                    "datetime": "2022-05-04T00:10:25.089Z"
                },
                ...
            ]
        },
        "output_settings":
        {
            "use_refraction_correction": 1,
            "verbosity": 4
        }
    }
    ------
    inupt parameters
    "lat" required, latitude in decimal format
    "lon" required, longitude in decimal format
    "altitude" required, altitude over sea level in meters
    "datetime: required, date of the day to calculate sunset, sunrise values, string,
            time and date in ISO format or string "today"
    "name" location name, is optional
    "output_settings" whole section is optional
    "use_refraction_correction" is optonal, default is 1
    "verbosity" is optional, default is 0
        0 basic output
        1 returns also name of the location
        2 returns other computed parameters
        3,4 full debug and diagnostics output
    """

    l_solarElevationsList = [
        # (phase name, solar elevation)
        ("horizon", 0.0),
        ("civil", -6.0),
        ("nautical", -12.0),
        ("astronomical", -18.0),
    ]

    atmosphericRefraction = 0.8333
    b_useRefractionCorrection: bool = True
    i_verbosity: int = 0
    if "output_settings" in jsonData:
        outputSettings = jsonData["output_settings"]
        if "use_refraction_correction" in outputSettings:
            b_useRefractionCorrection = bool(outputSettings["use_refraction_correction"] == 1)
        i_verbosity = outputSettings.get("verbosity", i_verbosity)

    l_locationsList = jsonData["locations"]
    l_sunriseSunsetList = []

    for location in l_locationsList:
        try:
            s_name = location.get("name", "")
            f_longitude = location["lon"]
            f_latitude = location["lat"]
            s_timestamp = location["datetime"]
            if s_timestamp == "today":
                f_timestamp = time.mktime(datetime.datetime.utcnow().timetuple())
            else:
                f_timestamp = Core.parseTimeISO(s_timestamp)
            f_altitude = location["altitude"]
            d_sunriseSunsetLocation = {}
            if i_verbosity > 0:
                d_sunriseSunsetLocation["name"] = s_name
            if i_verbosity > 2:
                d_sunriseSunsetLocation["lon"] = f_longitude
                d_sunriseSunsetLocation["lat"] = f_latitude
                d_sunriseSunsetLocation["timestamp"] = Core.formatTimeISO(f_timestamp)
                d_sunriseSunsetLocation["altitude"] = f_altitude
            l_phasesDict = {}
            for s_phaseName, f_solarElevation in l_solarElevationsList:
                if f_altitude > 0.0:
                    if b_useRefractionCorrection:
                        # altitude compensation
                        # https://en.wikipedia.org/wiki/Sunrise_equationSolarElevation
                        # https://www.siranah.de/html/sail040o.htm
                        # f_correctedSolarElevation = f_solarElevation - ((2.76*math.sqrt(f_altitude))/60.0) - atmosphericRefraction
                        f_correctedSolarElevation = f_solarElevation - ((1.76 * math.sqrt(f_altitude)) / 60.0)
                    else:
                        # f_correctedSolarElevation = f_solarElevation - ((2.76*math.sqrt(f_altitude))/60.0)
                        f_correctedSolarElevation = f_solarElevation - ((1.92 * math.sqrt(f_altitude)) / 60.0)
                else:
                    if b_useRefractionCorrection:
                        f_correctedSolarElevation = f_solarElevation - atmosphericRefraction
                    else:
                        f_correctedSolarElevation = f_solarElevation
                d_phaseDict = {}
                if i_verbosity > 1:
                    d_phaseDict["solar_elevation"] = f_solarElevation
                    d_phaseDict["corrected_elevation"] = f_correctedSolarElevation
                ssc = Geo.SunriseSunset(Geo.Coord.deg(f_longitude, f_latitude), f_timestamp, f_correctedSolarElevation)
                if i_verbosity > 1:
                    d_phaseDict["transit"] = Core.formatTimeISO(ssc.transit)
                d_phaseDict["sunrise"] = Core.formatTimeISO(ssc.sunrise)
                d_phaseDict["sunset"] = Core.formatTimeISO(ssc.sunset)
                l_phasesDict[s_phaseName] = d_phaseDict
            d_sunriseSunsetLocation["sunrise_sunset"] = l_phasesDict
            l_sunriseSunsetList.append(d_sunriseSunsetLocation)  # phases for one location
        except:
            l_sunriseSunsetList.append(None)
    if i_verbosity > 3:
        result = {
            "use_refraction_correction": b_useRefractionCorrection,
            "verbosity": i_verbosity,
            "ss_locations": l_sunriseSunsetList,
        }
    else:
        result = {"ss_locations": l_sunriseSunsetList}
    return result


class NWPCatalogue:
    """
    Simple class implementing miscellaneous NWP data related catalogue queries. Supports just a single
    query for now (implementing only a simple new feature task), could be extended in the future to
    support more queries.
    """

    def __init__(self):
        super().__init__()

    def queryModels(self, pattern: str = "", exclude: str = ""):
        """
        Query NWP models available in meteorological database.
        @type pattern: str
        @param pattern: String to use as a smart pattern (L{IBL.Utils.SmartPattern}) to select matching models.
            The pattern is used to match model identifiers.
        @type exclude: str
        @param exclude: String to use as a smart pattern (L{IBL.Utils.SmartPattern}) to exclude matching models.
            The pattern is used to match model identifiers.
        @return: List of models found.
        """
        import IBL.Utils.SmartPattern as SmartPattern

        mcat = Geo.ModelCatalogue.getInstance()
        b_favouriteModelSupport = getattr(mcat, "isFavourite", None) is not None
        l_models = list(DB.GridCatalog.getInstance().queryModels())
        if pattern != "":
            l_models = [model for model in l_models if SmartPattern.match(str(model), pattern)]
        if exclude != "":
            l_models = [model for model in l_models if not SmartPattern.match(str(model), exclude)]
        l_results = []
        for model in l_models:
            l_results.append(
                {
                    "id": str(model),
                    "name": mcat.getModelName(model),
                    "favourite": mcat.isFavourite(model) if b_favouriteModelSupport else False,
                    "token": Kernel.mkModelId(str(model)).toString(),
                }
            )
        return l_results


def listNWPModels(pattern: str = "", exclude: str = ""):
    return NWPCatalogue().queryModels(pattern, exclude)


if __name__ == "__main__":
    import json
    import sys

    b_sunriseSunsetTest = True
    b_basicListsTest = True
    b_messageRetrievalTest = True

    if b_sunriseSunsetTest:
        jsonDataString = """{ "locations":
        [
            {
                "name": "Bratislava",
                "lat": 48.1486,
                "lon": 17.1077,
                "altitude": 130,
                "datetime": "2022-05-04T00:10:25.089Z"
            },
            {
                "name": "Bratislava - Sea Level",
                "lat": 48.1486,
                "lon": 17.1077,
                "altitude": 0,
                "datetime": "today"
            }
        ],
        "output_settings":
            {
                "use_refraction_correction": 1,
                "verbosity": 4
            }
        }
        """

        import io

        jsonData = json.load(io.StringIO(jsonDataString))
        d_result = sunriseSunsetPhases(jsonData)
        print("*** sunrise/sunset test ...", "OK" if isinstance(d_result, dict) else "FAILED")

    if b_basicListsTest:
        result = listFIRAreas()
        print("*** FIR areas list test ...", "OK" if isinstance(result, list) else "FAILED")
        result = listCountryCodes()
        print("*** country codes list test ...", "OK" if isinstance(result, dict) else "FAILED")
        result = listAirportsInCSV()
        print("*** airports list test ...", "OK" if isinstance(result, str) else "FAILED")
        result = listPlaceLists()
        print("*** place lists list test ...", "OK" if isinstance(result, list) else "FAILED")
        result = listMessageHeadingFilters()
        print("*** message heading filters list test ...", "OK" if isinstance(result, list) else "FAILED")
        result = listNWPModels("KG*")
        print("*** NWP models list test ...", "OK" if isinstance(result, list) else "FAILED")

    if b_messageRetrievalTest:
        def retrieveSampleMessages(s_type, s_pattern, b_fastTestScan=False):
            l_results = listMessages(s_pattern, 1000000, 1, b_fastTestScan=b_fastTestScan)
            print("found {} {} data messages".format(len(l_results), s_type))
            d_samples = {}
            for d_result in l_results:
                if isinstance(d_result, dict):
                    s_heading = d_result["heading"][:11]
                    if not s_heading in d_samples:
                        d_samples[s_heading] = (d_result["id"], d_result["heading"])
            print("selected {} {} data samples".format(len(d_samples), s_type))
            i_message = 0
            d_formats = {}
            print("*** {} data retrieval test ... ".format(s_type), end="")
            sys.stdout.flush()
            for s_heading in d_samples:
                s_id, s_messageHeading = d_samples[s_heading]
                i_message += 1
                # if i_message % 100 == 0:
                #     print(f"retrieving message {i_message}...")
                d_message = getMessage(s_id, b_returnSourceFormat=True)
                if isinstance(d_message, dict):
                    # print(s_messageHeading)
                    if d_message["source-format"] not in d_formats:
                        d_formats[d_message["source-format"]] = d_message["format"]
            print("OK" if bool(d_formats) else "FAILED")
            # print(d_formats)

        # test of getMessage (binary data - pictorial messages)
        retrieveSampleMessages("pictorial", "P///// //// //////", True)
        # test of getMessage (binary data - NWP grid data)
        # retrieveSampleMessages("grid", "Y///// //// //////", True)