<!--
  Copyright (c) 2022, IBL Software Engineering spol. s r. o., <<EMAIL>>.
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
-->
<webservice xmlns:xi="http://www.w3.org/2001/XInclude">

  <!-- OW Maps, Messages, Routes, Reports -->
  <service path="/ria/db" type="python" module="config/iwebservice/iwebservice-ria-dbase.py" mode="json-rpc" http-cache-control="no-cache" debug="true"/>

  <!-- OW Maps, Routes, Reports -->
  <!-- <service path="/ria/opmetquery" type="opmet-query" http-cache-control="no-cache"/> -->

  <!-- OW Maps -->
  <!-- Change from SpotChart v2 to v1 if you are using Visual Weather 7.x or older -->
  <!-- <service path="/ria/spotchart" type="python" module="config/iwebservice/iwebservice-ria-spotchart-v2.py" mode="json-rpc" http-cache-control="no-cache" debug="true"/> -->
  <!-- <service path="/ria/spotchart" type="python" module="config/iwebservice/iwebservice-ria-spotchart-v1.py" mode="json-rpc" http-cache-control="no-cache" debug="true"/> -->

  <!-- OW Forms -->
  <!-- <service path="/ria/mews" type="python" module="python/IBL/MessageEditor/WebService.py" http-cache-control="no-cache" debug="true"/>
  <service path="/ria/meproducts" type="file" root="tmp/products-mews" http-cache-control="no-cache"/>
  <service path="/ria/ipdsproducts" type="file" root="var/ipds"/>
  <service path="/ria/ipds" type="ipds" http-cache-control="no-cache"/> -->

  <!-- OW Notifications -->
  <!-- <service path="/ria/icons" type="icon"/>
  <service path="/ria/sounds" type="file" root="share/sounds"/>
  <service path="/ria/mainpanel" type="main-panel" http-cache-control="no-cache" responsibility-schedules-db="Airport Responsibilities"/>
  <service path="/ria/notifications" type="notifications" http-cache-control="no-cache"/> -->

  <!-- OW Profiles -->
  <!-- <service path="/ria/ascentnavigator" type="ascent-navigator" http-cache-control="no-cache"/> -->

  <!-- Mandatory for all Online Weather apps -->

  <service path="/ria/lic" type="license-client" http-cache-control="no-cache"/>
  <!--
       This service provides authentication and must be bound to /ria - the root of all OW support service contexts.
       Must be placed at the end of the service list so that it's handled as last resort service context.
  -->
  <service path="/ria" type="python" module="config/iwebservice/iwebservice-ria-docstorage.py" http-cache-control="no-cache" debug="true"/>
</webservice>