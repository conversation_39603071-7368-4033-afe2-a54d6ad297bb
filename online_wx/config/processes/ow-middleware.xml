<?xml version="1.0" encoding="utf-8"?>
<XIBL-STORAGE version="1.1">
  <process-list xmlns:xi="http://www.w3.org/2001/XInclude">
    <process id="ow-middleware"
             name="Online Weather Middleware"
             essential="false"
             start-timeout="120s"
             finish-timeout="60s"
             run="DEBUG=ibl:* PORT=3000 CORS_ORIGIN=http://localhost:3000 OW_MAPS=true OW_IMPACTS=true OW_ROUTES=true node lib/node_modules/@ibl/ow-middleware/main.js">
      <!-- <depends id="notifyserver"/> -->
    </process>
  </process-list>
</XIBL-STORAGE>