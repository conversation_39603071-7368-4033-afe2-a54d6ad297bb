<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:map">
<XIBL-STORAGE version="1.1">
  <version>v3.6.3</version>
  <sequence>1000009</sequence>
  <setup></setup>
  <heading>PZZ/99</heading>
  <numeric-style-units altitude="ft-asl" distance="D_KM" temperature="T_CELS" wind="S_KT" depth="D_M" salinity="N_PROM" current="S_MPS" sound-speed="S_MPS"/>
  <paper_ratio_correction>1</paper_ratio_correction>
  <prjname>WORLD</prjname>
  <associated key="PageSize" value="fixed-area"/>
  <associated key="autorefresh" value="false"/>
  <legend_display>true</legend_display>
  <projection class="igeo.prj.LambdaPhi" lo0="-3.1415926535897931">
    <tr m00="6.2831853071795862" m01="0" m10="0" m11="-3.1415926535897931">
      <z x="-3.1415926535897931" y="1.5707963267948966"/>
    </tr>
  </projection>
  <mapType>geographic</mapType>
  <layers class="maps.layers.Coast" name="Land" legend_display="true" tooltip_display="false" visible="false" time_control="0" unique_id="1000001">
    <settings skin="doc:global/templates/Land/default" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once coast_precision="150" nopreprint="0"/>
      <color bumplevel="30" city-text-change="0" city_prefer="" city_prefonly="0" citycap="1" citydens="15" citymin="15" cityscale="1" cityshape="0" citysize="3" citytext="1" dem-sea-bumplevel="30" dem_enabled="0" enable-cities="0" glatlonformat="2" grid="1" grid-cross-size="0" grid-keep-same-steps="1" grid-min-la-step="0.00029088820866572158" grid-min-lo-step="0.00029088820866572158" grid-min-meridians="0" grid-min-parallels="0" grid-point-size="0" gstepla="0.26179938779914941" gsteplo="0.26179938779914941" gtextauto="1" gtextkeephoriz="0" gtextla="0.26179938779914941" gtextlaofs="0.1308996938995747" gtextlastep="0.26179938779914941" gtextlo="0.26179938779914941" gtextloofs="0.1308996938995747" gtextlostep="0.26179938779914941" gtextpos="1" gtickla="0.26179938779914941" gticklo="0.26179938779914941" height_units="2" lands="1" sign_180_ew="W" sign_e="E" sign_n="N" sign_s="S" sign_w="W" station_dens="15" station_placelist="" station_prefer="" station_prefonly="0" station_shape="0" station_size="3" stations="0" steps="1">
        <cityf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityf>
        <cityp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityp>
        <cityt attr="128" font="0" size="3.5">
          <color r="0" g="0" b="0" a="255"/>
        </cityt>
        <dem-sea-gradient/>
        <demg>
          <_0 value="0" middle="2500">
            <color r="0" g="100" b="0" a="255"/>
          </_0>
          <_1 value="5000" middle="0">
            <color r="100" g="100" b="0" a="255"/>
          </_1>
        </demg>
        <geosets min-inlandarea="0" minarea="0" showriverlakes="1">
          <borderp width="0.088194422" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </borderp>
          <coastf style="1">
            <color r="255" g="241" b="205" a="255"/>
          </coastf>
          <coastp width="0.35277769" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </coastp>
          <islandf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </islandf>
          <islandp width="0.17638884" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </islandp>
          <lakef style="0">
            <color r="255" g="255" b="255" a="255"/>
          </lakef>
          <lakep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lakep>
          <pondf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </pondf>
          <pondp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </pondp>
          <provincep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </provincep>
          <riverp width="0.088194422" style="0">
            <color r="255" g="255" b="255" a="255"/>
          </riverp>
          <terwaterp width="0.098777756" style="3">
            <color r="0" g="0" b="0" a="255"/>
          </terwaterp>
          <undeff style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undeff>
          <undefp width="0.098777756" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undefp>
        </geosets>
        <gridp width="0.17638884" style="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridp>
        <gridt attr="160" font="0" size="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridt>
        <gridtickp width="0.17638884" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gridtickp>
        <seaf style="1">
          <color r="212" g="229" b="225" a="255"/>
        </seaf>
        <seap width="0.35277769" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </seap>
        <stationf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationf>
        <stationp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationp>
        <stationt attr="128" font="0" size="3.2">
          <color r="0" g="0" b="0" a="255"/>
        </stationt>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.Any" name="D-Value_10Kft" legend_display="false" tooltip_display="true" visible="true" time_control="1" unique_id="1000003">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,500853,1000700,3000001,0[3,9] v10000[3,5] F-$v**v**$v00 v100[3,5] F/$v**v**$v00" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Height/Lines 2dkm" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="1" alternative-threshold="0" basiclevel="v800[3,5]" cut-above="0" cut-below="0" enable-alternative-pen="true" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="1" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="4" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="V" hilo1="N" hilo_density="0" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="1" number_enabled="1" numberscnt="1" rotatesigns="1" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.70555556" style="1">
            <color r="0" g="0" b="255" a="255"/>
          </alternative-linep>
          <boxf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583334" style="0">
            <color r="0" g="0" b="140" a="0"/>
          </boxp>
          <fillcolors>
            <_0 value="-10" middle="-9.5">
              <color r="0" g="0" b="0" a="255"/>
            </_0>
            <_1 value="-9" middle="-8.5">
              <color r="255" g="0" b="0" a="255"/>
            </_1>
            <_2 value="-8" middle="-7.5">
              <color r="153" g="51" b="0" a="255"/>
            </_2>
            <_3 value="-7" middle="-6.5">
              <color r="51" g="51" b="0" a="255"/>
            </_3>
            <_4 value="-6" middle="-5.5">
              <color r="0" g="51" b="0" a="255"/>
            </_4>
            <_5 value="-5" middle="-4.5">
              <color r="0" g="51" b="102" a="255"/>
            </_5>
            <_6 value="-4" middle="-3.5">
              <color r="0" g="0" b="128" a="255"/>
            </_6>
            <_7 value="-3" middle="-2.5">
              <color r="51" g="51" b="153" a="255"/>
            </_7>
            <_8 value="-2" middle="-1.5">
              <color r="51" g="51" b="51" a="255"/>
            </_8>
            <_9 value="-1" middle="-0.5">
              <color r="128" g="0" b="0" a="255"/>
            </_9>
            <_10 value="0" middle="0.5">
              <color r="0" g="128" b="0" a="255"/>
            </_10>
            <_11 value="1" middle="1.5">
              <color r="0" g="128" b="128" a="255"/>
            </_11>
            <_12 value="2" middle="2.5">
              <color r="51" g="204" b="204" a="255"/>
            </_12>
            <_13 value="3" middle="3.5">
              <color r="51" g="102" b="255" a="255"/>
            </_13>
            <_14 value="4" middle="4.5">
              <color r="51" g="153" b="102" a="255"/>
            </_14>
            <_15 value="5" middle="5.5">
              <color r="51" g="102" b="255" a="255"/>
            </_15>
            <_16 value="6" middle="6.5">
              <color r="128" g="0" b="128" a="255"/>
            </_16>
            <_17 value="7" middle="7.5">
              <color r="0" g="255" b="0" a="255"/>
            </_17>
            <_18 value="8" middle="8.5">
              <color r="255" g="255" b="0" a="255"/>
            </_18>
            <_19 value="9" middle="9.5">
              <color r="0" g="128" b="0" a="255"/>
            </_19>
            <_20 value="10" middle="2.5">
              <color r="255" g="153" b="0" a="255"/>
            </_20>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="5">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="1" size="5.5999999">
            <color r="0" g="0" b="0" a="191"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="0">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.70555556" style="1">
            <color r="255" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="0" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="161" font="3" size="5">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="D-Value_15Kft" legend_display="false" tooltip_display="true" visible="true" time_control="0" unique_id="1000009">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,500853,1000600,3000001,0[3,9] v15000[3,5] F-$v**v**$v00 v100[3,5] F/$v**v**$v00" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Height/Lines 2dkm" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="1" alternative-threshold="0" basiclevel="v800[3,5]" cut-above="0" cut-below="0" enable-alternative-pen="true" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="1" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="4" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="V" hilo1="N" hilo_density="0" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="1" number_enabled="1" numberscnt="1" rotatesigns="1" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.70555556" style="1">
            <color r="0" g="0" b="255" a="255"/>
          </alternative-linep>
          <boxf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583" style="0">
            <color r="0" g="0" b="140" a="0"/>
          </boxp>
          <fillcolors>
            <_0 value="-10" middle="-9.5">
              <color r="0" g="0" b="0" a="255"/>
            </_0>
            <_1 value="-9" middle="-8.5">
              <color r="255" g="0" b="0" a="255"/>
            </_1>
            <_2 value="-8" middle="-7.5">
              <color r="153" g="51" b="0" a="255"/>
            </_2>
            <_3 value="-7" middle="-6.5">
              <color r="51" g="51" b="0" a="255"/>
            </_3>
            <_4 value="-6" middle="-5.5">
              <color r="0" g="51" b="0" a="255"/>
            </_4>
            <_5 value="-5" middle="-4.5">
              <color r="0" g="51" b="102" a="255"/>
            </_5>
            <_6 value="-4" middle="-3.5">
              <color r="0" g="0" b="128" a="255"/>
            </_6>
            <_7 value="-3" middle="-2.5">
              <color r="51" g="51" b="153" a="255"/>
            </_7>
            <_8 value="-2" middle="-1.5">
              <color r="51" g="51" b="51" a="255"/>
            </_8>
            <_9 value="-1" middle="-0.5">
              <color r="128" g="0" b="0" a="255"/>
            </_9>
            <_10 value="0" middle="0.5">
              <color r="0" g="128" b="0" a="255"/>
            </_10>
            <_11 value="1" middle="1.5">
              <color r="0" g="128" b="128" a="255"/>
            </_11>
            <_12 value="2" middle="2.5">
              <color r="51" g="204" b="204" a="255"/>
            </_12>
            <_13 value="3" middle="3.5">
              <color r="51" g="102" b="255" a="255"/>
            </_13>
            <_14 value="4" middle="4.5">
              <color r="51" g="153" b="102" a="255"/>
            </_14>
            <_15 value="5" middle="5.5">
              <color r="51" g="102" b="255" a="255"/>
            </_15>
            <_16 value="6" middle="6.5">
              <color r="128" g="0" b="128" a="255"/>
            </_16>
            <_17 value="7" middle="7.5">
              <color r="0" g="255" b="0" a="255"/>
            </_17>
            <_18 value="8" middle="8.5">
              <color r="255" g="255" b="0" a="255"/>
            </_18>
            <_19 value="9" middle="9.5">
              <color r="0" g="128" b="0" a="255"/>
            </_19>
            <_20 value="10" middle="2.5">
              <color r="255" g="153" b="0" a="255"/>
            </_20>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="5">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="1" size="5.5999999">
            <color r="0" g="0" b="0" a="191"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="0">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.70555556" style="1">
            <color r="255" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="0" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="161" font="3" size="5">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="D-Value_20Kft" legend_display="false" tooltip_display="true" visible="true" time_control="0" unique_id="1000007">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,500853,1000500,3000001,0[3,9] v20000[3,5] F-$v**v**$v00 v100[3,5] F/$v**v**$v00" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Height/Lines 2dkm" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="1" alternative-threshold="0" basiclevel="v800[3,5]" cut-above="0" cut-below="0" enable-alternative-pen="true" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="1" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="4" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="V" hilo1="N" hilo_density="0" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="1" number_enabled="1" numberscnt="1" rotatesigns="1" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.70555556" style="1">
            <color r="0" g="0" b="255" a="255"/>
          </alternative-linep>
          <boxf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583" style="0">
            <color r="0" g="0" b="140" a="0"/>
          </boxp>
          <fillcolors>
            <_0 value="-10" middle="-9.5">
              <color r="0" g="0" b="0" a="255"/>
            </_0>
            <_1 value="-9" middle="-8.5">
              <color r="255" g="0" b="0" a="255"/>
            </_1>
            <_2 value="-8" middle="-7.5">
              <color r="153" g="51" b="0" a="255"/>
            </_2>
            <_3 value="-7" middle="-6.5">
              <color r="51" g="51" b="0" a="255"/>
            </_3>
            <_4 value="-6" middle="-5.5">
              <color r="0" g="51" b="0" a="255"/>
            </_4>
            <_5 value="-5" middle="-4.5">
              <color r="0" g="51" b="102" a="255"/>
            </_5>
            <_6 value="-4" middle="-3.5">
              <color r="0" g="0" b="128" a="255"/>
            </_6>
            <_7 value="-3" middle="-2.5">
              <color r="51" g="51" b="153" a="255"/>
            </_7>
            <_8 value="-2" middle="-1.5">
              <color r="51" g="51" b="51" a="255"/>
            </_8>
            <_9 value="-1" middle="-0.5">
              <color r="128" g="0" b="0" a="255"/>
            </_9>
            <_10 value="0" middle="0.5">
              <color r="0" g="128" b="0" a="255"/>
            </_10>
            <_11 value="1" middle="1.5">
              <color r="0" g="128" b="128" a="255"/>
            </_11>
            <_12 value="2" middle="2.5">
              <color r="51" g="204" b="204" a="255"/>
            </_12>
            <_13 value="3" middle="3.5">
              <color r="51" g="102" b="255" a="255"/>
            </_13>
            <_14 value="4" middle="4.5">
              <color r="51" g="153" b="102" a="255"/>
            </_14>
            <_15 value="5" middle="5.5">
              <color r="51" g="102" b="255" a="255"/>
            </_15>
            <_16 value="6" middle="6.5">
              <color r="128" g="0" b="128" a="255"/>
            </_16>
            <_17 value="7" middle="7.5">
              <color r="0" g="255" b="0" a="255"/>
            </_17>
            <_18 value="8" middle="8.5">
              <color r="255" g="255" b="0" a="255"/>
            </_18>
            <_19 value="9" middle="9.5">
              <color r="0" g="128" b="0" a="255"/>
            </_19>
            <_20 value="10" middle="2.5">
              <color r="255" g="153" b="0" a="255"/>
            </_20>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="5">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="1" size="5.5999999">
            <color r="0" g="0" b="0" a="191"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="0">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.70555556" style="1">
            <color r="255" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="0" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="161" font="3" size="5">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <common-data>
    <list key="center">
      <value class="token_combo">
        <value value="m&quot;sd:NOAA-GFS-PGRB2-0p25-RAW&quot;"/>
      </value>
    </list>
    <list key="dataset">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="level">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="run">
      <value class="token_combo">
        <value value="t2016,4,29,6,0,0"/>
      </value>
    </list>
    <list key="time-axis">
      <value class="time_axis_data" flags="1" mode="CLIP_RANGE"/>
    </list>
  </common-data>
  <legends>
    <layers/>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="true">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="gradient" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="true">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="auto" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="true">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="auto" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <proto>
      <Topic name="Topic" expand_policy="6" search_policy="3" enabled="true" text="">
        <hint x="0" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </Topic>
    </proto>
    <custom class="legend_text" name="10Kft" expand_policy="3" search_policy="3" enabled="true" priority="1" text="&lt;big&gt;GFS D-Values (in hundreds of feet)&lt;/big&gt;&#10;Pressure Level: 700 mb  - Standard Height: 10 Kft&#10;&lt;span style=&quot;color:#ff0000;&quot;&gt;Positive Values&lt;/span&gt;    &lt;span style=&quot;color:#0055ff;&quot;&gt;Negative Values&lt;/span&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.35277778" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
    <custom class="legend_text" name="15Kft" expand_policy="3" search_policy="3" enabled="true" priority="1" text="&lt;big&gt;GFS D-Values (in hundreds of feet)&lt;/big&gt;&#10;Pressure Level: 600 mb  - Standard Height: 15 Kft&#10;&lt;span style=&quot;color:#ff0000;&quot;&gt;Positive Values&lt;/span&gt;    &lt;span style=&quot;color:#0055ff;&quot;&gt;Negative Values&lt;/span&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.35277778" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
    <custom class="legend_text" name="20Kft" expand_policy="3" search_policy="3" enabled="true" priority="1" text="&lt;big&gt;GFS D-Values (in hundreds of feet)&lt;/big&gt;&#10;Pressure Level: 500 mb  - Standard Height: 20 Kft&#10;&lt;span style=&quot;color:#ff0000;&quot;&gt;Positive Values&lt;/span&gt;    &lt;span style=&quot;color:#0055ff;&quot;&gt;Negative Values&lt;/span&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.35277778" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
  </legends>
</XIBL-STORAGE>
