<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:map">
<XIBL-STORAGE version="1.1">
  <version>v3.6.3</version>
  <sequence>1000020</sequence>
  <setup></setup>
  <heading>PZZ/99</heading>
  <numeric-style-units altitude="ft-asl" distance="D_KM" temperature="T_CELS" wind="S_KT" depth="D_M" salinity="N_PROM" current="S_MPS" sound-speed="S_MPS"/>
  <paper_ratio_correction>1</paper_ratio_correction>
  <prjname>WORLD</prjname>
  <associated key="PageSize" value="fixed-area"/>
  <associated key="autorefresh" value="false"/>
  <legend_display>true</legend_display>
  <projection class="igeo.prj.LambdaPhi" lo0="-3.1415926535897931">
    <tr m00="6.2831853071795862" m01="0" m10="0" m11="-3.1415926535897931">
      <z x="-3.1415926535897931" y="1.5707963267948966"/>
    </tr>
  </projection>
  <mapType>geographic</mapType>
  <layers class="maps.layers.Coast" name="land" legend_display="true" tooltip_display="false" visible="false" time_control="0" unique_id="1000001">
    <settings skin="doc:global/templates/Land/default" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once coast_precision="150" nopreprint="0"/>
      <color bumplevel="30" city-text-change="0" city_prefer="" city_prefonly="0" citycap="1" citydens="15" citymin="15" cityscale="1" cityshape="0" citysize="3" citytext="1" dem-sea-bumplevel="30" dem_enabled="0" enable-cities="0" glatlonformat="2" grid="1" grid-cross-size="0" grid-keep-same-steps="1" grid-min-la-step="0.00029088820866572158" grid-min-lo-step="0.00029088820866572158" grid-min-meridians="0" grid-min-parallels="0" grid-point-size="0" gstepla="0.26179938779914941" gsteplo="0.26179938779914941" gtextauto="1" gtextkeephoriz="0" gtextla="0.26179938779914941" gtextlaofs="0.1308996938995747" gtextlastep="0.26179938779914941" gtextlo="0.26179938779914941" gtextloofs="0.1308996938995747" gtextlostep="0.26179938779914941" gtextpos="1" gtickla="0.26179938779914941" gticklo="0.26179938779914941" height_units="2" lands="1" sign_180_ew="W" sign_e="E" sign_n="N" sign_s="S" sign_w="W" station_dens="15" station_placelist="" station_prefer="" station_prefonly="0" station_shape="0" station_size="3" stations="0" steps="1">
        <cityf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityf>
        <cityp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityp>
        <cityt attr="128" font="0" size="3.5">
          <color r="0" g="0" b="0" a="255"/>
        </cityt>
        <dem-sea-gradient/>
        <demg>
          <_0 value="0" middle="2500">
            <color r="0" g="100" b="0" a="255"/>
          </_0>
          <_1 value="5000" middle="0">
            <color r="100" g="100" b="0" a="255"/>
          </_1>
        </demg>
        <geosets min-inlandarea="0" minarea="0" showriverlakes="1">
          <borderp width="0.088194422" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </borderp>
          <coastf style="1">
            <color r="255" g="241" b="205" a="255"/>
          </coastf>
          <coastp width="0.35277769" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </coastp>
          <islandf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </islandf>
          <islandp width="0.17638884" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </islandp>
          <lakef style="0">
            <color r="255" g="255" b="255" a="255"/>
          </lakef>
          <lakep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lakep>
          <pondf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </pondf>
          <pondp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </pondp>
          <provincep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </provincep>
          <riverp width="0.088194422" style="0">
            <color r="255" g="255" b="255" a="255"/>
          </riverp>
          <terwaterp width="0.098777756" style="3">
            <color r="0" g="0" b="0" a="255"/>
          </terwaterp>
          <undeff style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undeff>
          <undefp width="0.098777756" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undefp>
        </geosets>
        <gridp width="0.17638884" style="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridp>
        <gridt attr="160" font="0" size="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridt>
        <gridtickp width="0.17638884" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gridtickp>
        <seaf style="1">
          <color r="212" g="229" b="225" a="255"/>
        </seaf>
        <seap width="0.35277769" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </seap>
        <stationf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationf>
        <stationp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationp>
        <stationt attr="128" font="0" size="3.2">
          <color r="0" g="0" b="0" a="255"/>
        </stationt>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.Any" name="Relative Humidity" legend_display="true" tooltip_display="true" visible="true" time_control="1" unique_id="1000012">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,1000257,1000500,0,0[1,1]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[1,1]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="[50,100,5]" trace-mode="0"/>
        <color boldcnt="5" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="5" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="" hilo1="" hilo_density="10" hilo_digits="0" hilo_enabled="1" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="2" number_enabled="0" numberscnt="1" rotatesigns="0" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-linep>
          <boxf style="1">
            <color r="0" g="0" b="0" a="0"/>
          </boxf>
          <boxp width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </boxp>
          <fillcolors>
            <_0 value="50" middle="55">
              <color r="79" g="224" b="118" a="255"/>
            </_0>
            <_1 value="60" middle="65">
              <color r="69" g="195" b="102" a="255"/>
            </_1>
            <_2 value="70" middle="75">
              <color r="56" g="158" b="83" a="255"/>
            </_2>
            <_3 value="80" middle="85">
              <color r="49" g="138" b="73" a="255"/>
            </_3>
            <_4 value="90" middle="0">
              <color r="36" g="100" b="53" a="255"/>
            </_4>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="1">
            <color r="0" g="0" b="0" a="0"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </hilo-box-pen>
          <hilot attr="161" font="0" size="5.5999999">
            <color r="0" g="0" b="0" a="255"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="1">
            <color r="0" g="0" b="0" a="0"/>
          </isof>
          <lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </linep>
          <locol r="0" g="0" b="0" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="Absolute Vorticity" legend_display="true" tooltip_display="true" visible="true" time_control="0" unique_id="1000013">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="Gsd:NOAA-GFS-PGRB2-0p25-RAW,3,0,750391,1000500,3000001,0[0,0]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Vorticity/Absolute (Lines)" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[0,0]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="1" scale="100000" steps="-40 -36 -32 -28 -24 -20 -16 -12 12 16 20 24 28 32 36 40" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="0" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="5" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="Warm" hilo1="Cold" hilo_density="10" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;10^-5/s" number_density="0" number_enabled="1" numberscnt="1" rotatesigns="0" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-linep>
          <boxf style="1">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583334" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </boxp>
          <fillcolors>
            <_0 value="-32" middle="-29.999886932373048">
              <color r="25" g="25" b="75" a="215"/>
            </_0>
            <_1 value="-28" middle="-26.000000152587891">
              <color r="25" g="27" b="107" a="200"/>
            </_1>
            <_2 value="-24" middle="-22">
              <color r="40" g="52" b="132" a="195"/>
            </_2>
            <_3 value="-20" middle="-19.366881256103515">
              <color r="76" g="105" b="166" a="190"/>
            </_3>
            <_4 value="-16" middle="-13.999993896484375">
              <color r="64" g="129" b="194" a="185"/>
            </_4>
            <_5 value="-12" middle="8.3390693664550781">
              <color r="119" g="162" b="255" a="0"/>
            </_5>
            <_6 value="12" middle="13.99999755859375">
              <color r="255" g="124" b="112" a="180"/>
            </_6>
            <_7 value="16" middle="18">
              <color r="198" g="78" b="78" a="185"/>
            </_7>
            <_8 value="20" middle="22">
              <color r="144" g="67" b="61" a="190"/>
            </_8>
            <_9 value="24" middle="26">
              <color r="109" g="30" b="30" a="195"/>
            </_9>
            <_10 value="28" middle="29.999998931884765">
              <color r="94" g="25" b="25" a="200"/>
            </_10>
            <_11 value="32" middle="33.061225204467775">
              <color r="63" g="14" b="14" a="205"/>
            </_11>
            <_12 value="36" middle="38.000001220703126">
              <color r="204" g="41" b="244" a="210"/>
            </_12>
            <_13 value="40" middle="-40">
              <color r="129" g="0" b="194" a="215"/>
            </_13>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="1">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="3" size="4">
            <color r="255" g="0" b="0" a="191"/>
          </hilot>
          <hilovalt attr="160" font="3" size="3">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="1">
            <color r="0" g="0" b="0" a="0"/>
          </isof>
          <lineboldp width="0.17638889" style="2">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors>
            <_0 value="-41" middle="-40">
              <color r="0" g="0" b="0" a="0"/>
            </_0>
            <_1 value="-40" middle="-36.666666666666664">
              <color r="0" g="0" b="139" a="0"/>
            </_1>
            <_2 value="-33.333333333333336" middle="-30">
              <color r="19" g="19" b="169" a="0"/>
            </_2>
            <_3 value="-26.666666666666668" middle="-23.333333333333332">
              <color r="69" g="69" b="189" a="0"/>
            </_3>
            <_4 value="-20" middle="-16.666666666666671">
              <color r="109" g="109" b="209" a="0"/>
            </_4>
            <_5 value="-13.333333333333336" middle="-10.333333333333336">
              <color r="149" g="149" b="229" a="0"/>
            </_5>
            <_6 value="-7" middle="-6.6666666666666643">
              <color r="189" g="189" b="249" a="0"/>
            </_6>
            <_7 value="-6.6666666666666643" middle="-3.3333333333333357">
              <color r="255" g="255" b="255" a="0"/>
            </_7>
            <_8 value="0" middle="3.3333333333333357">
              <color r="255" g="255" b="255" a="0"/>
            </_8>
            <_9 value="6.6666666666666714" middle="10">
              <color r="255" g="189" b="189" a="0"/>
            </_9>
            <_10 value="13.333333333333336" middle="16.666666666666664">
              <color r="229" g="149" b="149" a="0"/>
            </_10>
            <_11 value="20" middle="23.333333333333336">
              <color r="209" g="109" b="109" a="0"/>
            </_11>
            <_12 value="26.666666666666671" middle="30">
              <color r="189" g="69" b="69" a="0"/>
            </_12>
            <_13 value="33.333333333333329" middle="36.666666666666671">
              <color r="169" g="19" b="19" a="0"/>
            </_13>
            <_14 value="40" middle="0">
              <color r="129" g="0" b="0" a="0"/>
            </_14>
          </linecolors>
          <linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="255" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="161" font="0" size="4">
            <color r="0" g="0" b="0" a="191"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="Geopotential Height" legend_display="true" tooltip_display="true" visible="true" time_control="0" unique_id="1000006">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,1000773,1000500,0,0[3,9]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Height/Lines 50dkm" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[3,14]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="6" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="false" hilo-extrema-km-size="0" hilo-extrema-min-size="5" hilo-extrema-rel-size="4.8999999999999995" hilo-placement="2" hilo0="H" hilo1="L" hilo_density="1" hilo_digits="0" hilo_enabled="1" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="1" number_enabled="1" numberscnt="1" rotatesigns="1" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-linep>
          <boxf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583334" style="0">
            <color r="0" g="0" b="140" a="0"/>
          </boxp>
          <fillcolors/>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="2">
            <color r="255" g="0" b="0" a="0"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="1" size="5.5999999">
            <color r="0" g="0" b="255" a="255"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="255" a="255"/>
          </hilovalt>
          <isof style="0">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.70555556" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </linep>
          <locol r="255" g="0" b="0" a="255"/>
          <lovalcol r="255" g="0" b="0" a="255"/>
          <numberst attr="161" font="3" size="3">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="Wind" legend_display="true" tooltip_display="true" visible="true" time_control="0" unique_id="1000014">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,500800,4000000,3000001,0[4,0]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.WindRender">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once auto-scale="0" barb-colour-cooperate="-1" barb-colour-cooperate-unit="v0[0,0]" barb-colour-style="1" cooperate="-1" decluttering-warning="false" density="70" fixed-steps="" mode="2" size="10" speedlimit="v0[4,0]"/>
        <color calm-circle-size="0.14999999999999999">
          <barb-gradient/>
          <barbp width="0.35277769" style="1">
            <color r="104" g="104" b="104" a="255"/>
          </barbp>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="Temperature" legend_display="true" tooltip_display="true" visible="true" time_control="0" unique_id="1000018">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,1000000,4000000,3000001,0[2,1]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Temperature/Solid 300hPa" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[2,0]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="1" scale="1" steps="[-100, 100, 5] #0" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="1" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="5" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="W" hilo1="C" hilo_density="10" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="2" number_enabled="1" numberscnt="1" rotatesigns="0" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </alternative-linep>
          <boxf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583334" style="0">
            <color r="255" g="0" b="0" a="255"/>
          </boxp>
          <fillcolors>
            <_0 value="-70" middle="-69.610963821411133">
              <color r="255" g="255" b="255" a="255"/>
            </_0>
            <_1 value="-68" middle="-66.99999988079071">
              <color r="153" g="153" b="177" a="255"/>
            </_1>
            <_2 value="-66" middle="-65">
              <color r="108" g="106" b="150" a="255"/>
            </_2>
            <_3 value="-64" middle="-63">
              <color r="30" g="51" b="127" a="255"/>
            </_3>
            <_4 value="-62" middle="-61">
              <color r="0" g="51" b="190" a="255"/>
            </_4>
            <_5 value="-60" middle="-59">
              <color r="0" g="0" b="255" a="255"/>
            </_5>
            <_6 value="-58" middle="-57">
              <color r="0" g="126" b="255" a="255"/>
            </_6>
            <_7 value="-56" middle="-55">
              <color r="0" g="190" b="255" a="255"/>
            </_7>
            <_8 value="-54" middle="-53">
              <color r="0" g="255" b="255" a="255"/>
            </_8>
            <_9 value="-52" middle="-51">
              <color r="0" g="242" b="189" a="255"/>
            </_9>
            <_10 value="-50" middle="-49">
              <color r="24" g="215" b="140" a="255"/>
            </_10>
            <_11 value="-48" middle="-47">
              <color r="0" g="204" b="0" a="255"/>
            </_11>
            <_12 value="-46" middle="-45">
              <color r="0" g="255" b="0" a="255"/>
            </_12>
            <_13 value="-44" middle="-43">
              <color r="204" g="255" b="0" a="255"/>
            </_13>
            <_14 value="-42" middle="-41">
              <color r="255" g="255" b="0" a="255"/>
            </_14>
            <_15 value="-40" middle="-39">
              <color r="237" g="237" b="126" a="255"/>
            </_15>
            <_16 value="-38" middle="-37">
              <color r="228" g="204" b="102" a="255"/>
            </_16>
            <_17 value="-36" middle="-35">
              <color r="255" g="190" b="0" a="255"/>
            </_17>
            <_18 value="-34" middle="-33">
              <color r="255" g="140" b="0" a="255"/>
            </_18>
            <_19 value="-32" middle="-31">
              <color r="255" g="0" b="0" a="255"/>
            </_19>
            <_20 value="-30" middle="-29.000000059604645">
              <color r="237" g="75" b="126" a="255"/>
            </_20>
            <_21 value="-28" middle="-24">
              <color r="217" g="12" b="120" a="255"/>
            </_21>
            <_22 value="-20" middle="0">
              <color r="85" g="0" b="127" a="255"/>
            </_22>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="1">
            <color r="0" g="0" b="0" a="0"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </hilo-box-pen>
          <hilot attr="161" font="1" size="6">
            <color r="255" g="0" b="0" a="255"/>
          </hilot>
          <hilovalt attr="160" font="1" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="0">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.35277778" style="2">
            <color r="0" g="0" b="255" a="255"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.35277778" style="2">
            <color r="255" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="255" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="160" font="0" size="4.1999998">
            <color r="255" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <layers class="maps.layers.Any" name="Absolute Vorticity Contours" legend_display="true" tooltip_display="true" visible="true" time_control="0" unique_id="1000020">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="Gsd:NOAA-GFS-PGRB2-0p25-RAW,3,0,750391,1000500,3000001,0[0,0]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin="doc:global/templates/IsoRender/Vorticity/Absolute (Lines)" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[0,0]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="1" scale="100000" steps="-40 -36 -32 -28 -24 -20 -16 -12 12 16 20 24 28 32 36 40" trace-mode="0"/>
        <color boldcnt="0" border_labels="0" box_enabled="0" clip_out_invalid="1" clip_out_undefined="1" extended-label-formatting="&lt;value show-units=&quot;false&quot;/&gt;" extended-point-info-formatting="&lt;value/&gt;" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="5" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="Warm" hilo1="Cold" hilo_density="10" hilo_digits="0" hilo_enabled="0" label-format="&lt;value hidden=&quot;false&quot; digits=&quot;0&quot;/&gt;" number_density="1" number_enabled="1" numberscnt="1" rotatesigns="0" use-extended-formatting="false">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-linep>
          <boxf style="1">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.010583334" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </boxp>
          <fillcolors/>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="1">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="3" size="4">
            <color r="255" g="0" b="0" a="191"/>
          </hilot>
          <hilovalt attr="160" font="3" size="3">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="1">
            <color r="0" g="0" b="0" a="0"/>
          </isof>
          <lineboldp width="0.17638889" style="2">
            <color r="0" g="0" b="0" a="255"/>
          </lineboldp>
          <linecolors>
            <_0 value="-41" middle="-40">
              <color r="0" g="0" b="0" a="0"/>
            </_0>
            <_1 value="-40" middle="-36.666666666666664">
              <color r="0" g="0" b="139" a="255"/>
            </_1>
            <_2 value="-33.333333333333336" middle="-30">
              <color r="19" g="19" b="169" a="255"/>
            </_2>
            <_3 value="-26.666666666666668" middle="-23.333333333333332">
              <color r="69" g="69" b="189" a="255"/>
            </_3>
            <_4 value="-20" middle="-16.666666666666671">
              <color r="109" g="109" b="209" a="255"/>
            </_4>
            <_5 value="-13.333333333333336" middle="-10.333333333333336">
              <color r="149" g="149" b="229" a="255"/>
            </_5>
            <_6 value="-7" middle="-6.6666666666666643">
              <color r="189" g="189" b="249" a="255"/>
            </_6>
            <_7 value="-6.6666666666666643" middle="-3.3333333333333357">
              <color r="255" g="255" b="255" a="255"/>
            </_7>
            <_8 value="0" middle="3.3333333333333357">
              <color r="255" g="255" b="255" a="255"/>
            </_8>
            <_9 value="6.6666666666666714" middle="10">
              <color r="255" g="189" b="189" a="255"/>
            </_9>
            <_10 value="13.333333333333336" middle="16.666666666666664">
              <color r="229" g="149" b="149" a="255"/>
            </_10>
            <_11 value="20" middle="23.333333333333336">
              <color r="209" g="109" b="109" a="255"/>
            </_11>
            <_12 value="26.666666666666671" middle="30">
              <color r="189" g="69" b="69" a="255"/>
            </_12>
            <_13 value="33.333333333333329" middle="36.666666666666671">
              <color r="169" g="19" b="19" a="255"/>
            </_13>
            <_14 value="40" middle="0">
              <color r="129" g="0" b="0" a="255"/>
            </_14>
          </linecolors>
          <linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </linep>
          <locol r="0" g="0" b="255" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="161" font="0" size="3">
            <color r="0" g="0" b="0" a="191"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <common-data>
    <list key="center">
      <value class="token_combo">
        <value value="m&quot;sd:NOAA-GFS-PGRB2-0p25-RAW&quot;"/>
      </value>
    </list>
    <list key="dataset">
      <value class="token_combo">
        <value value="v-1[0,0]"/>
      </value>
    </list>
    <list key="level">
      <value class="token_combo">
        <value value="v500[6,0]"/>
      </value>
    </list>
    <list key="run">
      <value class="token_combo">
        <value value="t2015,9,9,0,0,0"/>
      </value>
    </list>
    <list key="time-axis">
      <value class="time_axis_data" flags="1" mode="CLIP_RANGE"/>
    </list>
  </common-data>
  <legends>
    <layers/>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="3" search_policy="9" enabled="true" priority="-1" type="gradient" revert="false">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="255" g="255" b="255" a="255"/>
        </fnt>
        <gradient-pen-box width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="3" search_policy="9" enabled="true" priority="-1" type="gradient" revert="false">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="255" g="255" b="255" a="255"/>
        </fnt>
        <gradient-pen-box width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="auto" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="auto" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors name="colors" expand_policy="1" search_policy="3" enabled="true" priority="-1" type="auto" revert="false">
        <hint x="1" y="1"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
        <gradient-pen-box width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gradient-pen-box>
        <primary-units type="0" sub="0"/>
        <secondary-units type="0" sub="0"/>
      </colors>
    </layers>
    <proto>
      <Topic name="Topic" expand_policy="3" search_policy="9" enabled="true" text="&lt;big&gt;GFS 500mb Absolute Vorticity (10&lt;sup&gt;-5&lt;/sup&gt; s&lt;sup&gt;-1&lt;/sup&gt;): Colorfilled&lt;/big&gt;">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="255" g="255" b="255" a="255"/>
        </fnt>
      </Topic>
    </proto>
    <custom class="legend_text" name="Relative Humidity" expand_policy="3" search_policy="9" enabled="true" priority="1" text="&lt;big&gt;GFS 500mb Relative Humidity (%): Colorfilled&lt;/big&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.1" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
    <custom class="legend_text" name="Other" expand_policy="3" search_policy="9" enabled="true" priority="1" text="&lt;big&gt;GFS 500mb Geopotential Height (dm): Black lines &amp; Temperature (°C): &lt;span style=&quot;color:#ff0000;&quot;&gt;Red lines&lt;/span&gt;&lt;/big&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.1" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
    <custom class="legend_text" name="Topic1" expand_policy="3" search_policy="9" enabled="true" priority="1" text="&lt;big&gt;GFS 500mb Absolute Vorticity (10&lt;sup&gt;-5&lt;/sup&gt; s&lt;sup&gt;-1&lt;/sup&gt;) -- &lt;span style=&quot;color:#2b65ec;&quot;&gt;blue&lt;/span&gt; &amp; &lt;span style=&quot;color:#f75d59;&quot;&gt;pink&lt;/span&gt; lines&lt;/big&gt;">
      <hint x="0" y="1"/>
      <boxp width="0.1" style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxp>
      <boxf style="1">
        <color r="0" g="0" b="0" a="255"/>
      </boxf>
      <fnt attr="112" font="0" size="4">
        <color r="255" g="255" b="255" a="255"/>
      </fnt>
    </custom>
  </legends>
</XIBL-STORAGE>
