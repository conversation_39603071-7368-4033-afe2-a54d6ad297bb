<?xml version="1.0" encoding="UTF-8"?>
<XIBL-STORAGE version="1.1">
  <version>v3.4.4</version>
  <sequence>1000004</sequence>
  <setup></setup>
  <heading>PZZ/99</heading>
  <numeric-style-units altitude="m-asl" distance="D_KM" temperature="T_CELS" wind="S_MPS"/>
  <paper_ratio_correction>1</paper_ratio_correction>
  <prjname></prjname>
  <associated key="PageSize" value="fixed-area"/>
  <associated key="autorefresh" value="false"/>
  <legend_display>true</legend_display>
  <projection class="igeo.prj.LambdaPhi" lo0="-3.1415926535897931">
    <tr m00="6.2831853071795862" m01="0" m10="0" m11="-3.1415926535897931">
      <z x="-3.1415926535897931" y="1.5707963267948966"/>
    </tr>
  </projection>
  <mapType>geographic</mapType>
  <layers class="maps.layers.Coast" name="land" legend_display="true" tooltip_display="false" visible="false" time_control="0" unique_id="1000001">
    <settings skin="doc:global/templates/Land/default" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once coast_precision="150" nopreprint="0"/>
      <color bumplevel="30" city-text-change="0" city_prefer="" city_prefonly="0" citycap="1" citydens="15" citymin="15" cityscale="1" cityshape="0" citysize="3" citytext="1" dem_enabled="0" enable-cities="0" glatlonformat="2" grid="1" grid-cross-size="0" grid-point-size="0" gstepla="0.26179938779914941" gsteplo="0.26179938779914941" gtextauto="1" gtextkeephoriz="0" gtextla="0.26179938779914941" gtextlaofs="0.1308996938995747" gtextlastep="0.26179938779914941" gtextlo="0.26179938779914941" gtextloofs="0.1308996938995747" gtextlostep="0.26179938779914941" gtextpos="1" gtickla="0.26179938779914941" gticklo="0.26179938779914941" height_units="2" lands="1" sign_e="E" sign_n="N" sign_s="S" sign_w="W" station_dens="15" station_placelist="" station_prefer="" station_prefonly="0" station_shape="0" station_size="3" stations="0" steps="1">
        <cityf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityf>
        <cityp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityp>
        <cityt attr="128" font="0" size="3.5">
          <color r="0" g="0" b="0" a="255"/>
        </cityt>
        <demg>
          <_0 value="0" middle="2500">
            <color r="0" g="100" b="0" a="255"/>
          </_0>
          <_1 value="5000" middle="0">
            <color r="100" g="100" b="0" a="255"/>
          </_1>
        </demg>
        <geosets min-inlandarea="0" minarea="0" showriverlakes="1">
          <borderp width="0.088194422" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </borderp>
          <coastf style="1">
            <color r="255" g="241" b="205" a="255"/>
          </coastf>
          <coastp width="0.35277769" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </coastp>
          <islandf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </islandf>
          <islandp width="0.17638884" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </islandp>
          <lakef style="0">
            <color r="255" g="255" b="255" a="255"/>
          </lakef>
          <lakep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lakep>
          <pondf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </pondf>
          <pondp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </pondp>
          <provincep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </provincep>
          <riverp width="0.088194422" style="0">
            <color r="255" g="255" b="255" a="255"/>
          </riverp>
          <terwaterp width="0.098777756" style="3">
            <color r="0" g="0" b="0" a="255"/>
          </terwaterp>
          <undeff style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undeff>
          <undefp width="0.098777756" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undefp>
        </geosets>
        <gridp width="0.17638884" style="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridp>
        <gridt attr="160" font="0" size="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridt>
        <gridtickp width="0.17638884" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gridtickp>
        <seaf style="1">
          <color r="212" g="229" b="225" a="255"/>
        </seaf>
        <seap width="0.35277769" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </seap>
        <stationf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationf>
        <stationp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationp>
        <stationt attr="128" font="0" size="3.2">
          <color r="0" g="0" b="0" a="255"/>
        </stationt>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.Any" name="Max Wind Contour " legend_display="true" tooltip_display="true" visible="true" time_control="1" unique_id="1000002" class_name="">
    <source class="maps.layers.GridSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-modifications="1" check-full-coverage="false" frame-mode="3" kinking-strength="0" level-filter="" lower-bound="false" lower-bound-value="v0[0,0]" parameter-filter="" plain-decoding="1" post-filtering="0" post-filtering-width="2" post-subsample="1" post-subsample-filtering="1" run-filter="" run-mode="0" subsample-filtering="0" upper-bound="false" upper-bound-value="v0[0,0]"/>
        <color kfunc="G----,3,0,500800,4000000,3000001,0[4,0]" precx="0" precy="0"/>
      </settings>
    </source>
    <render class="maps.layers.IsoRender" controlledSkin="false">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once allow-oversampling="4" alternative-threshold="0" basiclevel="v0[4,2]" cut-above="0" cut-below="0" enable-alternative-pen="false" enable-cut-above="false" enable-cut-below="false" enable-kinking="0" enable-limit-bottom="false" enable-limit-top="false" enable-missing-substitution="false" enable-suppress-artefacts="true" enable-threshold-highs="false" enable-threshold-lows="false" gainmode="0" limit-bottom="0" limit-highs="0" limit-lows="0" limit-top="0" missing-substitution="-32768" number-color-gradient="0" scale="1" steps="[70,310,20]" trace-mode="0"/>
        <color boldcnt="5" border_labels="0" box_enabled="0" clip_out_invalid="1" clip_out_undefined="1" display-label-value="true" hilo-box-enabled="false" hilo-clip-labels="true" hilo-extrema-km-size="150" hilo-extrema-min-size="5" hilo-extrema-rel-size="9.9999999999999982" hilo-placement="2" hilo0="" hilo1="" hilo_density="0" hilo_digits="0" hilo_enabled="0" number_density="1" number_digits="0" number_enabled="1" numbers="" numberscnt="1" rotatesigns="1">
          <alternative-lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-lineboldp>
          <alternative-linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </alternative-linep>
          <boxf style="1">
            <color r="255" g="255" b="255" a="255"/>
          </boxf>
          <boxp width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </boxp>
          <fillcolors>
            <_0 value="70" middle="80">
              <color r="242" g="203" b="255" a="255"/>
            </_0>
            <_1 value="90" middle="100">
              <color r="232" g="155" b="232" a="255"/>
            </_1>
            <_2 value="110" middle="120">
              <color r="202" g="79" b="255" a="255"/>
            </_2>
            <_3 value="130" middle="130">
              <color r="130" g="85" b="130" a="255"/>
            </_3>
            <_4 value="150" middle="150">
              <color r="235" g="147" b="24" a="255"/>
            </_4>
            <_5 value="170" middle="170">
              <color r="202" g="101" b="50" a="255"/>
            </_5>
            <_6 value="190" middle="190">
              <color r="165" g="78" b="87" a="255"/>
            </_6>
            <_7 value="210" middle="220.00014572143556">
              <color r="145" g="37" b="37" a="255"/>
            </_7>
            <_8 value="230" middle="240">
              <color r="93" g="20" b="17" a="255"/>
            </_8>
            <_9 value="250" middle="260.00004653930665">
              <color r="119" g="149" b="255" a="255"/>
            </_9>
            <_10 value="270" middle="279.99999904632568">
              <color r="97" g="97" b="145" a="255"/>
            </_10>
            <_11 value="290" middle="145">
              <color r="68" g="26" b="131" a="255"/>
            </_11>
          </fillcolors>
          <hilo-arrow-low-color r="0" g="0" b="0" a="255"/>
          <hilo-arrow-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-arrow-pen>
          <hilo-box-fill style="1">
            <color r="255" g="255" b="255" a="255"/>
          </hilo-box-fill>
          <hilo-box-pen width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </hilo-box-pen>
          <hilot attr="161" font="0" size="5.5999999">
            <color r="0" g="0" b="0" a="255"/>
          </hilot>
          <hilovalt attr="160" font="0" size="4.1999998">
            <color r="0" g="0" b="0" a="255"/>
          </hilovalt>
          <isof style="1">
            <color r="0" g="0" b="0" a="255"/>
          </isof>
          <lineboldp width="0.70555556" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </lineboldp>
          <linecolors/>
          <linep width="0.35277778" style="1">
            <color r="0" g="0" b="0" a="0"/>
          </linep>
          <locol r="0" g="0" b="0" a="255"/>
          <lovalcol r="0" g="0" b="0" a="255"/>
          <numberst attr="160" font="0" size="3">
            <color r="0" g="0" b="0" a="255"/>
          </numberst>
        </color>
      </settings>
    </render>
  </layers>
  <common-data>
    <list key="center">
      <value class="token_combo">
        <value value="m&quot;sd:NOAA-GFS-PGRB2-0p25-RAW&quot;"/>
      </value>
    </list>
    <list key="dataset">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="level">
      <value class="token_combo">
        <value value="v300[6,0]"/>
      </value>
    </list>
    <list key="run">
      <value class="token_combo">
        <value value="t2014,2,21,12,0,0"/>
      </value>
    </list>
    <list key="time-axis">
      <value class="time_axis_data" flags="1" mode="CLIP_RANGE"/>
    </list>
  </common-data>
  <legends>
    <layers/>
    <layers>
      <source name="source" expand_policy="5" search_policy="4" enabled="false">
        <hint x="1" y="0"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
      <colors type="gradient" revert="false" name="colors" expand_policy="3" search_policy="9" enabled="true" priority="-1">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxf>
        <fnt attr="0" font="0" size="4">
          <color r="255" g="255" b="255" a="255"/>
        </fnt>
      </colors>
    </layers>
    <proto>
      <Topic name="Topic" expand_policy="3" search_policy="9" enabled="true" text="&lt;big&gt;GFS Winds GTE to 70 (kts)&lt;/big&gt;">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="255" g="255" b="255" a="255"/>
        </fnt>
      </Topic>
    </proto>
  </legends>
</XIBL-STORAGE>
