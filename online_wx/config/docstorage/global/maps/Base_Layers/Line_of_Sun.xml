<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:map">
<XIBL-STORAGE version="1.1">
  <version>v4.0.3</version>
  <setup></setup>
  <heading>PZZ/99</heading>
  <numeric-style-units altitude="ft-asl" distance="D_KM" temperature="T_CELS" wind="S_KT" depth="D_M" salinity="N_PROM" current="S_MPS" sound-speed="S_MPS"/>
  <paper_ratio_correction>1</paper_ratio_correction>
  <prjname></prjname>
  <associated key="PageSize" value="fixed-area"/>
  <associated key="PropertyWidgets.DiscreteToolbar.DensityRatio" value="2.0"/>
  <associated key="autorefresh" value="yes"/>
  <legend_display>true</legend_display>
  <projection class="igeo.prj.LambdaPhi" lo0="-3.1415926535897931">
    <tr m00="6.2831853071795862" m01="0" m10="0" m11="-3.1415926535897931">
      <z x="-3.1415926535897931" y="1.5707963267948966"/>
    </tr>
  </projection>
  <mapType>geographic</mapType>
  <layers class="maps.layers.Coast" name="land" legend_display="true" tooltip_display="false" latest_frame_in_animation="false" visible="false" time_control="0" unique_id="1000001">
    <settings skin="doc:global/templates/Land/default" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once city-pos-if-occupied="" city-pos-if_cutbymapedge="" city-preferred-pos="" city-preferred-pos-override="" coast_precision="150" nopreprint="0" station-pos-if-occupied="" station-pos-if_cutbymapedge="" station-preferred-pos="" station-preferred-pos-override=""/>
      <color bumplevel="30" city-text-change="0" city_prefer="" city_prefonly="0" citycap="1" citydens="15" citymin="15" cityscale="1" cityshape="0" citysize="3" citystyle="0" citytext="1" dem-sea-bumplevel="30" dem_enabled="0" enable-cities="0" enable-stations="0" glatlonformat="2" grid="1" grid-cross-size="0" grid-keep-same-steps="1" grid-min-la-step="0.00029088820866572158" grid-min-lo-step="0.00029088820866572158" grid-min-meridians="0" grid-min-parallels="0" grid-point-size="0" gstepla="0.26179938779914941" gsteplo="0.26179938779914941" gtextauto="1" gtextkeephoriz="0" gtextla="0.26179938779914941" gtextlaofs="0.1308996938995747" gtextlastep="0.26179938779914941" gtextlo="0.26179938779914941" gtextloofs="0.1308996938995747" gtextlostep="0.26179938779914941" gtextpos="1" gtickla="0.26179938779914941" gticklo="0.26179938779914941" height_units="2" lands="1" sign_180_ew="W" sign_e="E" sign_n="N" sign_s="S" sign_w="W" station_dens="15" station_placelist="" station_prefer="" station_prefonly="0" station_shape="0" station_size="3" stations="1" steps="1">
        <cityf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityf>
        <cityfill style="0">
          <color r="255" g="255" b="255" a="255"/>
        </cityfill>
        <cityoutline width="0.35277778" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </cityoutline>
        <cityp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityp>
        <cityt attr="128" font="0" size="3.5">
          <color r="0" g="0" b="0" a="255"/>
        </cityt>
        <dem-sea-gradient/>
        <demg>
          <_0 value="0" middle="2500">
            <color r="0" g="100" b="0" a="255"/>
          </_0>
          <_1 value="5000" middle="0">
            <color r="100" g="100" b="0" a="255"/>
          </_1>
        </demg>
        <geosets min-inlandarea="0" minarea="0" showriverlakes="1">
          <borderp width="0.088194422" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </borderp>
          <coastf style="1">
            <color r="255" g="241" b="205" a="255"/>
          </coastf>
          <coastp width="0.35277769" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </coastp>
          <islandf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </islandf>
          <islandp width="0.17638884" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </islandp>
          <lakef style="0">
            <color r="255" g="255" b="255" a="255"/>
          </lakef>
          <lakep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </lakep>
          <pondf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </pondf>
          <pondp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </pondp>
          <provincep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </provincep>
          <riverp width="0.088194422" style="0">
            <color r="255" g="255" b="255" a="255"/>
          </riverp>
          <terwaterp width="0.098777756" style="3">
            <color r="0" g="0" b="0" a="255"/>
          </terwaterp>
          <undeff style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undeff>
          <undefp width="0.098777756" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undefp>
        </geosets>
        <gridp width="0.17638884" style="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridp>
        <gridt attr="160" font="0" size="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridt>
        <gridtickp width="0.17638884" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gridtickp>
        <seaf style="1">
          <color r="212" g="229" b="225" a="255"/>
        </seaf>
        <seap width="0.35277769" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </seap>
        <stationf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationf>
        <stationfill style="0">
          <color r="255" g="255" b="255" a="255"/>
        </stationfill>
        <stationoutline width="0.35277778" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </stationoutline>
        <stationp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationp>
        <stationt attr="128" font="0" size="3.2">
          <color r="0" g="0" b="0" a="255"/>
        </stationt>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.LineOfSun" name="Line of Sun - Ideal" legend_display="true" tooltip_display="true" latest_frame_in_animation="false" visible="true" time_control="0" unique_id="1000006">
    <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once/>
      <color below-horizont-angle="0">
        <dark-fill style="1">
          <color r="3" g="3" b="3" a="32"/>
        </dark-fill>
        <light-fill style="0">
          <color r="255" g="255" b="127" a="64"/>
        </light-fill>
        <twilight-pen width="5.644443" style="0">
          <color r="182" g="182" b="182" a="64"/>
        </twilight-pen>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.LineOfSun" name="Line of Sun - Civil" legend_display="true" tooltip_display="true" latest_frame_in_animation="false" visible="true" time_control="0" unique_id="1000009">
    <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once/>
      <color below-horizont-angle="6">
        <dark-fill style="1">
          <color r="0" g="0" b="0" a="48"/>
        </dark-fill>
        <light-fill style="0">
          <color r="255" g="255" b="127" a="64"/>
        </light-fill>
        <twilight-pen width="0.35277778" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </twilight-pen>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.LineOfSun" name="Line of Sun - Nautical" legend_display="true" tooltip_display="true" latest_frame_in_animation="false" visible="true" time_control="0" unique_id="1000010">
    <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once/>
      <color below-horizont-angle="12">
        <dark-fill style="1">
          <color r="0" g="0" b="0" a="64"/>
        </dark-fill>
        <light-fill style="0">
          <color r="255" g="255" b="127" a="64"/>
        </light-fill>
        <twilight-pen width="0.35277778" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </twilight-pen>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.LineOfSun" name="Line of Sun - Astronomical" legend_display="true" tooltip_display="true" latest_frame_in_animation="false" visible="false" time_control="0" unique_id="1000011">
    <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once/>
      <color below-horizont-angle="18">
        <dark-fill style="1">
          <color r="0" g="0" b="0" a="96"/>
        </dark-fill>
        <light-fill style="0">
          <color r="255" g="255" b="127" a="64"/>
        </light-fill>
        <twilight-pen width="0.35277778" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </twilight-pen>
      </color>
    </settings>
  </layers>
  <layers class="maps.layers.Any" name="Time Axis" legend_display="true" tooltip_display="true" latest_frame_in_animation="false" visible="true" time_control="1" unique_id="1386271909">
    <source class="maps.layers.CustomTimeSource">
      <settings skin_modified="true" autoConvertGray="true" autoConvertBW="true">
        <once frame-end-kfunc="" frame-mode="0" frame-start-kfunc="Ftime$$t v1[5,2] Froundtime_down$tv50$t v24[5,2] F-$tv50$t" frame-step-kfunc="v30[5,1]" max-frames="97"/>
        <color/>
      </settings>
    </source>
    <render class="maps.layers.NullRender">
      <settings autoConvertGray="true" autoConvertBW="true">
        <once/>
        <color/>
      </settings>
    </render>
  </layers>
  <common-data>
    <list key="center">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="dataset">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="level">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="run">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="time-axis">
      <value class="time_axis_data" range-back-limit="PT48H" flags="0" mode="CLIP_RANGE"/>
    </list>
  </common-data>
  <legends>
    <layers/>
    <layers/>
    <layers/>
    <layers/>
    <layers/>
    <layers>
      <source name="Source" expand_policy="5" search_policy="4" enabled="true">
        <hint x="1" y="0"/>
        <boxp width="0.35277778" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </source>
    </layers>
    <proto>
      <Topic name="Topic" expand_policy="6" search_policy="3" enabled="true" text="">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </Topic>
    </proto>
  </legends>
</XIBL-STORAGE>
