<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  24.08.2016
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:supercachesettings">
<XIBL-STORAGE version="1.1">
  <!-- Maximum size of Super Cache on disk, e.g. 50GB. -->
  <maximum-size>100GB</maximum-size>

  <!-- House-keeping makes sure that used size is not over this ratio (expressed in percents).
       This is called optimal size of cache. Unless this level is reached, eviction is based solely
       on retention policies.

       This forms kind of safety margin making sure that regular operation would not hit cache full condition.
       Cache full during regular operation implies "lag" in user experience as eviction is complex
       and time consuming process.

  -->
  <house-keeping-ratio>95</house-keeping-ratio>

  <!-- Super Cache status report frequency. Reported status can be viewed in supercachemanager log. -->
  <report-frequency>PT5M</report-frequency>

  <!-- Frequency of TOC synchronisation. Applies only in shared mode. -->
  <sync-frequency>PT5S</sync-frequency>

  <!-- House-keeping frequency, -->
  <check-frequency>PT5M</check-frequency>

  <!-- Default options if ISUPERCACHE is not set or set to "default"

       Available options so far: planar, derived, processed, point, netcdf, small-grid, transforms, reports, debug

       See documentation for details [http://wiki.iblsoft.com/display/VWDOC/Super+Cache]
  -->
  <default-options>planar|derived|processed|transforms|reports</default-options>

  <!-- Eviction strategy:

        modification-time      Use modification time for marking last access, accessed items are touched on the file system.
                               This option is not compatible with NFS as it breaks its caching. Suggested option if cache
                               is not big enough to hold all items using solely retention policies.

        access-time            Use access-time for marking last access. On Linux this requires relatime to be set to reasonable
                               values in mount options.

        creation-time          Using creation time only. Similar to access-time without relatime set. This option is suggested
                               if you are sure eviction would happen solely on retention policies (i.e. used size would not
                               exceed house-keeping-ratio threshold)

        default                Use default option for system.
  -->
  <eviction-strategy>modification-time</eviction-strategy>

  <!-- Retention policies.

       Default retention applies to all items not recognised by explicit retention rules.

       Explicit retention can be specified on paths matching certain patterns
       by adding one or more elements like follows:

          <retention key="*/image/*" value="PT12H"/>

       The above retention rule limits life-time of image products to 12 hours.
  -->
  <default-retention>PT24H</default-retention>

  <!-- Automatic subscriptions

       Lists subscriptions for automatic pre-caching whenever certain type of report comes in.

       See the following examples:

          <subscription class-type="IBL.Plugins.SuperCache.PreCacheKernelGrid" pattern="GRIB:KGFX,1000000,2"/>
          <subscription class-type="IBL.Plugins.SuperCache.PreCacheAscent" pattern="GridRun:KGFX"/>

       For GRIB1 use the value subtracted with 500000.
       E.g. for Temperature 628130 use the number 128130 (628130 - 500000).

          <subscription class-type="IBL.Plugins.SuperCache.PreCacheKernelGrid" pattern="GRIB:ECMA,128130,100??00"/>
          <subscription class-type="IBL.Plugins.SuperCache.PreCacheKernelGrid" pattern="GRIB:ECMA,128130,2"/>

       Fine tuning grid tiles subscriptions
       To pre-cache pre-cache NECP-GFS temperature data at 2 metres including tile levels 1 and 2
       over the entire Europe and level 3 over Slovakia
          <subscription class-type="IBL.Plugins.SuperCache.PreCacheKernelGrid" pattern="GRIB:KGFX,1000000,2">
            <grid-tiles area-path="doc:global/areas/Europe" tile-levels="1,2"/>
            <grid-tiles area-path="doc:global/areas/Slovakia" tile-levels="3"/>
          </subscription>

       To find out which parameters are available and the ID numbers that should be used in the specification, use the following command:
          queryindex -k | grep <model-id>

  -->
  <!-- Second level cache

        Keep 100 items in memory (default is 50):
          <max-online-items-count>100</max-online-items-count>

        Limit total size of items to 128 MB (default is 256 MB):
          <max-online-items-total-size>128MB</max-online-items-total-size>

        Perform 2nd level cache housekeeping every 5 minutes (default is 2 minutes):
          <online-prune-frequency>PT5M</online-prune-frequency>

        During housekeeping remove items unused for 10 minutes (default is 5 minutes):
          <online-item-prune-age>PT10M</online-item-prune-age>

        To log items removed but still referenced (i.e. not released actually, default is false):
          <online-log-referenced-items>true</online-log-referenced-items>
  -->

  <!-- Synchronization

       Super Cache allows to share cached results between systems. This might be useful in certain cases like if you are
       building distributed system working on large models. Super Cache currently supports two synchronisation models:
       1) Running concurrently on the same large cache space (shared mode)
       2) Running independently however accessing common "archive"

       The following types of synchronization are available:

       1) No synchronization

          <synchronization>standalone</synchronization>

       2) Shared mode - supercache directory must be mapped to same shared disk with quick access time and ideally coherent
          view. Optinally shared-sync-delay element may be used to specify how often to merge TOCs from cooperators.
          The suggested value is around 5 times of sync-frequency.

          <synchronization>shared</synchronization>
          <shared-sync-delay>PT30S</shared-sync-delay>

       3) File Archive - using shared directory as an archive.

          <synchronization>file-archive</synchronization>
          <file-archive-path>/mnt/myarchive#nfs</file-archive-path>

          <online-archive-id>my-file-archive</online-archive-id>

          Element file-archive-path defines archive path optionally suffixed with hash mark (#) following options separated by ampersand mark (&).
          The following options are available:

              not-coherent       Archive is not coherent, i.e. file created by instance A may not be available at instance B for certain
                                 period of time.

              coherent           Archive is coherent.

              no-clone           Archive does not support clonning (hard links).

              clone              Archive supports hard links.

              nfs                Indicates archive is on NFS. Shorthand for not-coherent&no-clone.

       4) ElastiCache - using Memcached as backend for archive. Would be likely removed in future. Discovery feature is
          so far not implemented, thus all nodes must be listed in configuration (before SERVER shall be double dash).

          <synchronization>elasticache</synchronization>
          <elasticache-config>- -SERVER=bdd-cache.yuboip.0001.euw1.cache.amazonaws.com:11211 - -SERVER=bdd-cache.yuboip.0002.euw1.cache.amazonaws.com:11211</elasticache-config>
          <online-archive-id>my-elasticache-archive</online-archive-id>

       5) Cloud Storage - using cloud storage as backend for archive

          <synchronization>cloud-storage</synchronization>
          <cloud-storage-settings>s3:REGION:BUCKET:ACCESS-KEY:SECRET-KEY</cloud-storage-settings>
          <cloud-storage-expiration>P1D</cloud-storage-expiration>
          <archive-pattern>*grid_series*</archive-pattern>

          <online-archive-id>my-s3-archive</online-archive-id>

       6) Super Cache Archive - prototype of parallel archive shared on all nodes of distributed system giving good performance however currently lacking resilience.

          <synchronization>super-cache-archive</synchronization>
          <archive-server-authority>ec2-52-210-150-153.eu-west-1.compute.amazonaws.com</archive-server-authority>
          <archive-client-authority>*************</archive-client-authority>
          <archive-path>/mnt/ssd2/archive</archive-path>
          <archive-block-size>8388608</archive-block-size>
          <archive-redundancy>2</archive-redundancy>

          <online-archive-id>my-parallel-archive</online-archive-id>

          Element archive-server-authority specifies "root" of the distributed super cache archive. Root is managing receipts, knows what kind of data is stored on which hosts.
          Currently is single point of failure.

          Element archive-client-authority shall provide address of current system in distributed architecture (effectively this is ip of system, but must be ip that is accessible
          from all other systems).

          Element archive-path specifies where the content shall be stored. Multiple paths may be specified if you want to spread archive over several drives.

          Element archive-block-size is optional and specifies maximum block size in segmentation. Items larger than this size are split into blocks which are then spread over
          distributed archive. This enables to parallelize access to larger items.

          Element archive-redundancy is optional and specifies number of copies of the same block in distributed archive. This is can decrease the risk that archived item
          would become lost in case some of clients are broken.

       When using archive based synchronization (either file-archive, elasticache, s3 or super-cache-archive) the archive can be labeled using online-archive-id element.
       When two systems have the same online-archive-id, certain tasks may be optimised (e.g. RAWWCS retrieval may be optimised using archive as data exchange platform),
       this it is suggested to label archives consistently across systems.
  -->
  <synchronization>standalone</synchronization>

</XIBL-STORAGE>