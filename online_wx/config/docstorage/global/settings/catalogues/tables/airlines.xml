<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  14.03.2014
  Authors: <AUTHORS>

  Copyright (C) 2014, IBL Software Engineering spol. s r. o., <<EMAIL>>.
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <field id="IATA" type="string"/>
    <field id="ICAO" type="string"/>
    <field id="CallSign" type="string"/>
    <field id="Name" type="string"/>
    <field id="URL" type="string"/>
    <field id="Email" type="string"/>
    <field id="Phone" type="string"/>
    <field id="Fax" type="string"/>
    <field id="Person" type="string"/>
    <field id="Address" type="string"/>
    <field id="Country" type="string"/>
    <field id="Bill<PERSON>erson" type="string"/>
    <field id="BillPhone" type="string"/>
    <field id="BillFax" type="string"/>
    <field id="BillAddress" type="string"/>
    <field id="BillCountry" type="string"/>
  </specification>
  <index id="idxPKey">
    <key>
      <item field="IATA"/>
      <item field="ICAO"/>
    </key>
  </index>
  <index id="idxIATA">
    <key>
      <item field="IATA"/>
    </key>
  </index>
  <index id="idxICAO">
    <key>
      <item field="ICAO"/>
    </key>
  </index>
  <load>
    <primary-key>
      <item field="IATA"/>
      <item field="ICAO"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/airlines.csv"/>
  </load>
</XIBL-STORAGE>
