<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  06.10.2010
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <field id="param-id" type="string"/>
    <field id="site-id" type="string"/>
    <field id="type" type="enum">
      <value short-name="NONE"/>
      <value value="0" short-name="BELOW"/>
      <value value="1" short-name="ABOVE"/>
      <value value="2" short-name="INSIDE"/>
      <value value="3" short-name="OUTSIDE"/>
      <value value="4" short-name="BELOW_WITH_WARNING"/>
      <value value="5" short-name="ABOVE_WITH_WARNING"/>
      <value value="6" short-name="TRUE"/>
      <value value="7" short-name="FALSE"/>
    </field>
    <field id="limit-1" type="number"/>
    <field id="limit-2" type="number"/>
    <field id="units" type="enum">
      <value value="0" short-name="N_NONE"/>
      <value value="1000" short-name="N_NUM"/>
      <value value="1001" short-name="N_PERC"/>
      <value value="1002" short-name="N_PROM"/>
      <value value="1003" short-name="N_PPM"/>
      <value value="1004" short-name="N_FRAC"/>
      <value value="2000" short-name="T_CELS"/>
      <value value="2001" short-name="T_KELV"/>
      <value value="2002" short-name="T_FAREN"/>
      <value value="3000" short-name="D_MM"/>
      <value value="3001" short-name="D_CM"/>
      <value value="3002" short-name="D_M"/>
      <value value="3003" short-name="D_KM"/>
      <value value="3004" short-name="D_INCH"/>
      <value value="3005" short-name="D_FEET"/>
      <value value="3006" short-name="D_MILE"/>
      <value value="3007" short-name="D_FL"/>
      <value value="3008" short-name="D_AT"/>
      <value value="3009" short-name="D_GPM"/>
      <value value="3010" short-name="D_USNM"/>
      <value value="3011" short-name="D_UKNM"/>
      <value value="3012" short-name="D_SMILE"/>
      <value value="3013" short-name="D_DM"/>
      <value value="3014" short-name="D_DKM"/>
      <value value="4000" short-name="S_MPS"/>
      <value value="4001" short-name="S_KMPH"/>
      <value value="4002" short-name="S_KT"/>
      <value value="4003" short-name="S_MPH"/>
      <value value="4004" short-name="S_CMS"/>
      <value value="4005" short-name="S_MMH"/>
      <value value="4006" short-name="S_KMD"/>
      <value value="4007" short-name="S_MMS"/>
      <value value="5000" short-name="T_SEC"/>
      <value value="5001" short-name="T_MIN"/>
      <value value="5002" short-name="T_HOUR"/>
      <value value="5003" short-name="T_DAY"/>
      <value value="5004" short-name="T_YEARS"/>
      <value value="6000" short-name="P_HPA"/>
      <value value="6001" short-name="P_MMHG"/>
      <value value="6002" short-name="P_MB"/>
      <value value="6003" short-name="P_PA"/>
      <value value="6004" short-name="P_INCHHG32"/>
      <value value="6005" short-name="P_INCHHG60"/>
      <value value="6006" short-name="P_DPA"/>
      <value value="7000" short-name="C_OCTAS"/>
      <value value="7001" short-name="C_DECAS"/>
      <value value="7002" short-name="C_FLIGHT"/>
      <value value="7003" short-name="C_PERC"/>
      <value value="7004" short-name="C_FRAC"/>
      <value value="8000" short-name="P_KG_M2"/>
      <value value="8001" short-name="P_KG_CM2"/>
      <value value="8002" short-name="P_KG_M2_H"/>
      <value value="8003" short-name="P_KG_CM2_H"/>
      <value value="8004" short-name="P_KG_M2_S"/>
      <value value="8005" short-name="P_M_M2"/>
      <value value="8006" short-name="P_M_M2_H"/>
      <value value="8007" short-name="P_MM"/>
      <value value="8008" short-name="P_MM_H"/>
      <value value="9000" short-name="R_USVH"/>
      <value value="9001" short-name="R_MSVH"/>
      <value value="9002" short-name="R_SVH"/>
      <value value="9003" short-name="R_BQ"/>
      <value value="9004" short-name="R_JCM2"/>
      <value value="9005" short-name="R_JM2"/>
      <value value="9006" short-name="R_KJM2"/>
      <value value="10000" short-name="D_DEG"/>
      <value value="10001" short-name="D_RAD"/>
    </field>
  </specification>

  <!-- Define the table indices -->
  <index id="index">
    <key>
      <item field="param-id"/>
      <item field="site-id"/>
    </key>
  </index>

  <!-- Define the table loaders -->
  <load>
    <primary-key>
      <item field="param-id"/>
      <item field="site-id"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/limitcatalog.csv"/>
  </load>
</XIBL-STORAGE>
