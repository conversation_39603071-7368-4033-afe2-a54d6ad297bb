<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  14.05.2012
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <!-- Internal id -->
    <field id="name" type="string"/>
    <!-- ISO 3166 A2 code -->
    <field id="description" type="string"/>
  </specification>

  <!-- Define the table loaders -->
  <load>
    <primary-key>
      <item field="name"/>
    </primary-key>
    <reader class="idb.UniStorageTableIO" path="doc:global/settings/catalogues/accessrights" data-type="accessrightslist" record-name="access-right"/>
  </load>
</XIBL-STORAGE>
