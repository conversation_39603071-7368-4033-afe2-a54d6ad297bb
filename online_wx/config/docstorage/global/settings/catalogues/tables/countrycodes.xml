<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  28.01.2011
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <field id="id" type="string"/>
    <field id="iso" type="string"/>
    <field id="fips" type="string"/>
    <field id="wmo" type="string"/>
    <field id="icao" type="string"/>
    <field id="parent" type="string"/>
    <field id="name" type="string"/>
    <field id="type" type="enum">
      <value value="0" short-name="CATEGORY"/>
      <value value="1" short-name="REGION"/>
      <value value="2" short-name="COUNTRY"/>
      <value value="3" short-name="TERRITORY"/>
    </field>
  </specification>

  <!-- Define the table indices -->
  <index id="id">
    <key>
      <item field="id"/>
    </key>
  </index>
  <index id="iso">
    <key>
      <item field="iso"/>
    </key>
  </index>
  <index id="fips">
    <key>
      <item field="fips"/>
    </key>
  </index>
  <index id="wmo">
    <key>
      <item field="wmo"/>
    </key>
  </index>
  <index id="name">
    <key>
      <item field="name" ignore-case="true"/>
    </key>
  </index>

  <!-- Define the table loaders -->
  <load>
    <primary-key>
      <item field="id"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/countrycodes.csv"/>
  </load>
</XIBL-STORAGE>
