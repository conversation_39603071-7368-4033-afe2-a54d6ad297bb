<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  09.02.2011
  Authors: <AUTHORS>

  Copyright (C) 2011-2019, IBL Software Engineering spol. s r. o., <<EMAIL>>.
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <field id="id" type="string" full-name="Id"/>
    <field id="wmo" type="int" full-name="WMO"/>
    <field id="icao" type="string" full-name="ICAO"/>
    <field id="iata" type="string" full-name="IATA"/>
    <field id="wigos" type="string" full-name="WIGOS"/>
    <field id="name" type="string" full-name="Name"/>
    <field id="custom-name" type="string" full-name="Custom name"/>
    <field id="elevation" type="number" full-name="Elevation"/>
    <field id="pressure-sensor-height" type="number" full-name="Pressure sensor height"/>
    <field id="latitude" type="number" full-name="Latitude"/>
    <field id="longitude" type="number" full-name="Longitude"/>
    <field id="priority" type="int" full-name="Priority"/>
    <field id="country-code" type="string" full-name="Country code"/>
    <field id="time-zone" type="string" full-name="Time zone"/>
    <field id="state-region" type="string" full-name="State/Region"/>
    <field id="runway-direction-1" type="number" full-name="Runway direction 1"/>
    <field id="runway-direction-2" type="number" full-name="Runway direction 2"/>
    <field id="minimum-sector-altitude" type="number" full-name="Minimum sector altitude (MSA)"/>
    <field id="group" type="string" full-name="Group"/>
    <field id="flags" type="flags" full-name="Flags">
      <value value="0" short-name="UpperAir"/>
      <value value="1" short-name="Modified"/>
      <value value="2" short-name="KeyPoint"/>
      <value value="3" short-name="Automatic"/>
      <value value="4" short-name="AutoObserv"/>
      <value value="5" short-name="Military"/>
      <value value="6" short-name="Deleted"/>
      <value value="7" short-name="Station"/>
      <value value="8" short-name="Airport"/>
      <value value="9" short-name="Waypoint"/>
      <value value="10" short-name="NavAid"/>
      <value value="11" short-name="LimitOfForecast"/>
    </field>
    <field id="type" type="enum" full-name="Type">
      <value value="0" short-name="STATION" full-name="Meteorological station (unrecognised type)"/>
      <value value="1" short-name="STNS" full-name="Meteorological station providing synoptic data (SYNOP)"/>
      <value value="2" short-name="STNA" full-name="Meteorological station providing aviation data (METAR, TAF)"/>
      <value value="3" short-name="STNU" full-name="Meteorological station providing upper air data (TEMP)"/>
      <value value="51" short-name="TCAC" full-name="Tropical Cyclone Advisory Centre"/>
      <value value="52" short-name="VAAC" full-name="Volcanic Ash Advisory Centre"/>
      <value value="99" short-name="OTHER" full-name="Other"/>
      <value value="100" short-name="ABN" full-name="Aerodrome Beacon/Aeronautical Ground Light Symbol (VTC)"/>
      <value value="101" short-name="AD" full-name="Aerodrome"/>
      <value value="102" short-name="ALA" full-name="Aircraft Landing Area"/>
      <value value="103" short-name="BST" full-name="Broadcast Station ERC low symbol"/>
      <value value="104" short-name="DME" full-name="Distance Measuring Equipment (Aust)"/>
      <value value="105" short-name="GAP" full-name="VTC Large Open Arrow (GAAP Approach Point)"/>
      <value value="106" short-name="GNA" full-name="GPS Non-procision Approach point"/>
      <value value="107" short-name="GP" full-name="Glide Path"/>
      <value value="108" short-name="HBN" full-name="Hazard Beacon"/>
      <value value="109" short-name="HLS" full-name="Helicopter Landing Site"/>
      <value value="110" short-name="ILS" full-name="Instrument Landing System"/>
      <value value="111" short-name="IM" full-name="Inner Marker"/>
      <value value="112" short-name="LDM" full-name="Land Mark/Chart or Cultural Feature"/>
      <value value="113" short-name="LLZ" full-name="Localiser"/>
      <value value="114" short-name="LOC" full-name="Locator"/>
      <value value="115" short-name="MAL" full-name="VTC Marine Light Symbol"/>
      <value value="116" short-name="MM" full-name="Middle Marker"/>
      <value value="117" short-name="NDB" full-name="Non Directional Beacon"/>
      <value value="118" short-name="OM" full-name="Outer Marker"/>
      <value value="119" short-name="SAL" full-name="VTC Large Solid Arrow (Approved Operations for ARR/DEP Only)"/>
      <value value="120" short-name="TAC" full-name="TACAN"/>
      <value value="121" short-name="TSP" full-name="VTC Solid Box (Tracking Point)"/>
      <value value="122" short-name="VFA" full-name="VTC Diamond Half Solid/Half Open (Approach Point)"/>
      <value value="123" short-name="VFC" full-name="VTC Open Box (Check Point)"/>
      <value value="124" short-name="VFM" full-name="VTC Solid Triangle (Compulsory Reporting Point)"/>
      <value value="125" short-name="VFR" full-name="VTC Open Triangle (Enroute Reporting Point)"/>
      <value value="126" short-name="VOR" full-name="VOR"/>
      <value value="127" short-name="WPT" full-name="IFR Waypoint"/>
      <value value="128" short-name="VORDME" full-name="VOR and Distance Measuring Equipment"/>
      <value value="129" short-name="VORTAC" full-name="VOR and TACAN"/>
      <value value="130" short-name="NDBDME" full-name="Non Directional Beacon and Distance Measuring Equipment"/>
      <value value="1000" short-name="VOLCANO" full-name="Volcanoes"/>
      <value value="1001" short-name="CITY" full-name="Cities"/>
      <value value="1002" short-name="CAPITALCITY" full-name="Capital Cities"/>
      <value value="1003" short-name="WAVEBUOY" full-name="Wave Buoys"/>
      <value value="1004" short-name="WAVEBUOYV" full-name="Wave Buoys (virtual)"/>
      <value value="2000" short-name="FIR" full-name="FIR Area"/>
      <value value="2001" short-name="COUNTRY" full-name="Countries"/>
      <value value="2002" short-name="LOF" full-name="LOF Area"/>
      <value value="2003" short-name="FIR-OLD" full-name="Old FIR Area"/>
      <value value="2004" short-name="UIR" full-name="UIR Area"/>
      <value value="2005" short-name="CTA" full-name="CTA Area"/>
      <value value="990" short-name="DPOINT" full-name="Point (dynamic)"/>
      <value value="991" short-name="DPOLY" full-name="Polygon (dynamic)"/>
      <value value="992" short-name="DSTATION" full-name="Station (dynamic)"/>
      <value value="3000" short-name="USR1" full-name="User 1"/>
      <value value="3001" short-name="USR2" full-name="User 2"/>
      <value value="3002" short-name="USR3" full-name="User 3"/>
      <value value="3003" short-name="USR4" full-name="User 4"/>
      <value value="3004" short-name="USR5" full-name="User 5"/>
    </field>
    <field id="source" type="flags" full-name="Source">
      <value short-name="STCAT" full-name="IBL Place Catalogue" flags-character="S"/>
      <value short-name="AWCAT" full-name="IBL Aviation Catalogue" flags-character="A"/>
      <value value="15" short-name="CUSTOM" full-name="Customer Specific" flags-character="Z"/>
    </field>
    <field id="icao-region" type="string" full-name="ICAO region"/>
    <field id="polygon" type="string" full-name="Polygon"/>
    <field id="polyline" type="string" full-name="Polyline"/>
    <field id="population" type="int" full-name="Population"/>
    <field id="custom-id" type="string" full-name="Custom Id"/>
    <field id="GML-id" type="string" full-name="GML Id"/>
    <field id="organisation" type="string" full-name="Organisation"/>
  </specification>
  <index id="id">
    <key>
      <item field="id"/>
    </key>
  </index>
  <index id="wmo">
    <key>
      <item field="wmo"/>
    </key>
  </index>
  <index id="icao">
    <key>
      <item field="icao"/>
    </key>
  </index>
  <index id="iata">
    <key>
      <item field="iata"/>
    </key>
  </index>
  <index id="wigos">
    <key>
      <item field="wigos"/>
    </key>
  </index>
  <index id="name">
    <key>
      <item field="name" ignore-case="true"/>
    </key>
  </index>
  <index id="type">
    <key>
      <item field="type"/>
    </key>
  </index>
  <index id="country-code">
    <key>
      <item field="country-code"/>
    </key>
  </index>
  <index id="custom-id">
    <key>
      <item field="custom-id"/>
    </key>
  </index>
  <index id="GML-id">
    <key>
      <item field="GML-id"/>
    </key>
  </index>
  <load>
    <primary-key>
      <item field="id"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/stcat.csv"/>
  </load>
  <load>
    <primary-key>
      <item field="id"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/cities.csv"/>
  </load>
  <load>
    <primary-key>
      <item field="id"/>
    </primary-key>
    <reader class="idb.UniStorageTableIO" optional="true" path="doc:runtime/PlaceCatalog/DynamicPlaceTable" data-type="stationcatalogue.entrylist" record-name="station">
      <conversion field="elevation" name="elev"/>
      <conversion field="latitude" name="lat"/>
      <conversion field="longitude" name="lon"/>
    </reader>
  </load>

  <!--
   The local place catalogue changes should be always loaded last because these are manual
  changes done by users which have the highest priority and should override any place entry with the same ID.
  -->
  <load>
    <primary-key>
      <item field="id"/>
    </primary-key>
    <reader class="idb.UniStorageTableIO" optional="true" path="doc:global/settings/catalogues/stations/local" data-type="stationcatalogue.entrylist" record-name="station">
      <conversion field="elevation" name="elev"/>
      <conversion field="latitude" name="lat" scale="57.295779513100001"/>
      <conversion field="longitude" name="lon" scale="57.295779513100001"/>
      <conversion field="runway-direction-1" name="runway-direction1"/>
      <conversion field="runway-direction-2" name="runway-direction2"/>
      <conversion field="minimum-sector-altitude" name="minimum-sector-altitude"/>
      <conversion field="flags" type="int"/>
    </reader>
  </load>
</XIBL-STORAGE>
