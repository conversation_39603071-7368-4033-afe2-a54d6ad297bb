<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  23.01.2019
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <field id="placement-set-id" type="string"/>
    <field id="place-id" type="string"/>
    <field id="placement" type="string"/>
  </specification>

  <!-- Define the table indices -->
  <index id="placement-set-id">
    <key>
      <item field="placement-set-id"/>
    </key>
  </index>
  <index id="place-id">
    <key>
      <item field="place-id"/>
    </key>
  </index>
  <index id="placement">
    <key>
      <item field="placement"/>
    </key>
  </index>

  <!-- Define the table loaders -->
  <load>
    <reader class="idb.CSVTableIO" path="config/labelplacementoverrides.csv"/>
  </load>
</XIBL-STORAGE>
