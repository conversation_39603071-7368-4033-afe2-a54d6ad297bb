import argparse
import boto3
import botocore
import gzip
import os
from datetime import datetime, timedelta, UTC
import shutil


# Define static variables
MODEL_CONFIG = {
    'GFS': {
        'bucket': 'noaa-gfs-bdp-pds',
        'pattern': ['gfs.t{run:02d}z.pgrb2.0p25.f{forecast_hour:03d}']
    },
    'GEFS': {
        'bucket': 'noaa-gefs-pds',
        'pattern': ['gep{member:02d}.t{run:02d}z.pgrb2a.0p50.f{forecast_hour:03d}'],
        'control_pattern': 'gec00.t{run:02d}z.pgrb2a.0p50.f{forecast_hour:03d}',
        'members': list(range(1, 31))  # gep01 through gep30
    },
    'GOES': {
        'bucket': 'noaa-goes{bird}',
        'pattern': ['OR_ABI-L1b-RadF-M3C{channel:02d}_G{bird}_s{timestamp}.nc'],
        'channels': [1, 2, 3]  # Default channels to pull for GOES
    },
    'MRMS': {
        'bucket': 'noaa-mrms-pds',
        'pattern': ['MRMS_MergedReflectivityQC_00.50_{timestamp}.grib2.gz'],
        'products': ['MergedReflectivityQCComposite_00.50']
    }
}

INGEST_BUCKET = 'dev-shredder-input'

def copy_file(s3_client, src_bucket, src_key, dest_bucket, dest_key):
    with open('/tmp/tempfile', 'wb') as f:
        s3_client.download_fileobj(src_bucket, src_key, f)
    s3_client.upload_file('/tmp/tempfile', dest_bucket, dest_key)
    os.remove('/tmp/tempfile')

def fetch_gfs(s3_client, run, forecast_hours):
    today = datetime.now(UTC).strftime('%Y%m%d')
    config = MODEL_CONFIG['GFS']
    for fh in range(forecast_hours):
        filename = f"gfs.{today}/{run:02d}/atmos/" + config['pattern'][0].format(run=run, forecast_hour=fh)
        try:
            copy_file(s3_client, config['bucket'], filename, INGEST_BUCKET, filename.split('/')[-1])
        except botocore.exceptions.ClientError:
            print(f"File not found: {filename}")

def list_goes_images(s3_client, bucket, prefix, channels, hours):
    response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix, Delimiter='/')
    if 'CommonPrefixes' not in response:
        return []
    
    available_hours = sorted([p['Prefix'].split('/')[-2] for p in response['CommonPrefixes']], reverse=True)
    selected_hours = available_hours[:hours]
    
    files = []
    for hour in selected_hours:
        hour_prefix = f"{prefix}{hour}/"
        hour_files = s3_client.list_objects_v2(Bucket=bucket, Prefix=hour_prefix)
        if 'Contents' not in hour_files:
            continue
        for obj in hour_files['Contents']:
            key = obj['Key']
            for channel in channels:
                if f"C{channel:02d}_G" in key:
                    files.append(key)
                    break
    return files

def fetch_goes(s3_client, bird, hours):
    config = MODEL_CONFIG['GOES']
    goes_bucket = config['bucket'].format(bird=bird)
    julian_day = datetime.now(UTC).strftime('%j')
    prefix = f"ABI-L1b-RadF/{datetime.now(UTC).year}/{julian_day}/"
    
    files = list_goes_images(s3_client, goes_bucket, prefix, config['channels'], hours)
    for file in files:
        copy_file(s3_client, goes_bucket, file, INGEST_BUCKET, file.split('/')[-1])

def get_mrms_date_prefix(hours_back=0):
    """Get the date prefix for MRMS files, accounting for UTC rollover"""
    target_time = datetime.now(UTC) - timedelta(hours=hours_back)
    return target_time.strftime('%Y%m%d')

def list_mrms_files_for_date(s3_client, region, product, date_str):
    """List all MRMS files for a specific date and product"""
    config = MODEL_CONFIG['MRMS']
    prefix = f"{region}/{product}/{date_str}/"
    
    try:
        response = s3_client.list_objects_v2(Bucket=config['bucket'], Prefix=prefix)
        if 'Contents' not in response:
            return []
        return [obj['Key'] for obj in response['Contents'] if obj['Key'].endswith('.grib2.gz')]
    except botocore.exceptions.ClientError:
        return []

def extract_mrms_valid_time(filename):
    """Extract valid time from MRMS filename"""
    # Extract timestamp from filename like MRMS_MergedReflectivityQCComposite_00.50_20250807-165639.grib2.gz
    import re
    match = re.search(r'_(\d{8})-(\d{6})\.grib2\.gz$', filename)
    if match:
        date_str, time_str = match.groups()
        return datetime.strptime(f"{date_str}{time_str}", '%Y%m%d%H%M%S').replace(tzinfo=UTC)
    return None

def filter_mrms_by_time_range(files, hours):
    """Filter MRMS files to only include those within the requested time range"""
    end_time = datetime.now(UTC)
    start_time = end_time - timedelta(hours=hours)
    
    valid_files = []
    for file in files:
        valid_time = extract_mrms_valid_time(file)
        if valid_time and start_time <= valid_time <= end_time:
            valid_files.append(file)
    
    return sorted(valid_files, key=lambda f: extract_mrms_valid_time(f))

def should_check_previous_day(hours):
    """Determine if we need to check previous day's data based on current time and requested hours"""
    now = datetime.now(UTC)
    current_hour = now.hour
    
    # If we're within 1 hour of 00Z, check previous day for safety
    if current_hour < 1:
        return True
    
    # If requested hours would go back beyond 00Z today, check previous day
    hours_since_midnight = current_hour + (now.minute / 60.0)
    if hours > hours_since_midnight:
        return True
    
    return False

def fetch_mrms(s3_client, hours, region='CONUS', product='MergedReflectivityQCComposite_00.50'):
    """Fetch MRMS data for the specified time range"""
    config = MODEL_CONFIG['MRMS']
    
    # Get today's files
    all_files = []
    today_date = get_mrms_date_prefix(0)
    files = list_mrms_files_for_date(s3_client, region, product, today_date)
    all_files.extend(files)
    
    # Only check yesterday if we need to
    if should_check_previous_day(hours):
        yesterday_date = get_mrms_date_prefix(24)
        yesterday_files = list_mrms_files_for_date(s3_client, region, product, yesterday_date)
        all_files.extend(yesterday_files)
        print(f"Checking both {yesterday_date} and {today_date} for MRMS data")
    else:
        print(f"Checking only {today_date} for MRMS data")
    
    # Filter files by time range
    target_files = filter_mrms_by_time_range(all_files, hours)
    
    print(f"Found {len(target_files)} MRMS files for last {hours} hours")
    
    for file_key in target_files:
        filename = file_key.split('/')[-1]
        local_path = f"/tmp/{filename}"
        
        # Download and decompress
        try:
            with open(local_path, 'wb') as f:
                s3_client.download_fileobj(config['bucket'], file_key, f)
            
            with gzip.open(local_path, 'rb') as f_in:
                with open(local_path[:-3], 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            s3_client.upload_file(local_path[:-3], INGEST_BUCKET, filename[:-3])
            print(f"Processed: {filename}")
            
        except Exception as e:
            print(f"Error processing {filename}: {e}")
        finally:
            # Cleanup
            for temp_file in [local_path, local_path[:-3]]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)

def generate_timestamps(start_time, end_time):
    timestamps = []
    current_time = start_time
    while current_time <= end_time:
        timestamps.append(current_time.strftime('%Y%j%H%M%S'))
        current_time += timedelta(minutes=10)
    return timestamps

def fetch_gefs(s3_client, run, forecast_hours):
    """Fetch GEFS ensemble data for specified run and forecast hours

    Args:
        forecast_hours: Number of forecast time steps to fetch (not hours)
                       GEFS data comes in 3-hour increments: f000, f003, f006, etc.
                       So forecast_hours=2 will fetch f000 and f003
    """
    today = datetime.now(UTC).strftime('%Y%m%d')
    config = MODEL_CONFIG['GEFS']

    total_files = 0
    for i in range(forecast_hours):
        # GEFS data comes in 3-hour increments: f000, f003, f006, f009, etc.
        fh = i * 3

        # Download control member (gec00)
        control_filename = f"gefs.{today}/{run:02d}/atmos/pgrb2ap5/" + config['control_pattern'].format(run=run, forecast_hour=fh)
        try:
            copy_file(s3_client, config['bucket'], control_filename, INGEST_BUCKET, control_filename.split('/')[-1])
            total_files += 1
            print(f"Downloaded control: {control_filename.split('/')[-1]}")
        except botocore.exceptions.ClientError:
            print(f"Control file not found: {control_filename}")

        # Download ensemble members (gep01-gep30)
        for member in config['members']:
            member_filename = f"gefs.{today}/{run:02d}/atmos/pgrb2ap5/" + config['pattern'][0].format(member=member, run=run, forecast_hour=fh)
            try:
                copy_file(s3_client, config['bucket'], member_filename, INGEST_BUCKET, member_filename.split('/')[-1])
                total_files += 1
            except botocore.exceptions.ClientError:
                print(f"Member file not found: {member_filename}")

    print(f"Downloaded {total_files} GEFS files total")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Fetch public data from NOAA AWS buckets.')
    parser.add_argument('-m', '--model', required=True, help='Model to fetch data for (GFS, GEFS, GOES, MRMS)')
    parser.add_argument('-r', '--run', type=int, help='Model run to fetch (for GFS)')
    parser.add_argument('-f', '--forecast_hours', type=int, help='Number of forecast hours to fetch (for GFS)')
    parser.add_argument('-t', '--hours', type=int, help='Number of hours of data to fetch (for GOES, MRMS)')
    parser.add_argument('--mrms-region', type=str, default='CONUS', 
                       choices=['ALASKA', 'ANC', 'CARIB', 'CONUS', 'GUAM', 'HAWAII'],
                       help='MRMS region to fetch data for')
    parser.add_argument('--mrms-product', type=str, default='MergedReflectivityQCComposite_00.50',
                       help='MRMS product to fetch')
    parser.add_argument('--bird', type=int, help='GOES satellite number (16, 17, 18)')
    parser.add_argument('--profile', help='AWS profile to use')

    args = parser.parse_args()

    session = boto3.Session(profile_name=args.profile) if args.profile else boto3.Session()
    s3_client = session.client('s3')

    if args.model == 'GFS':
        fetch_gfs(s3_client, args.run, args.forecast_hours)
    elif args.model == 'GEFS':
        fetch_gefs(s3_client, args.run, args.forecast_hours)
    elif args.model == 'GOES':
        fetch_goes(s3_client, args.bird, args.hours)
    elif args.model == 'MRMS':
        fetch_mrms(s3_client, args.hours, args.mrms_region, args.mrms_product)
    else:
        print(f"Unknown model: {args.model}")
