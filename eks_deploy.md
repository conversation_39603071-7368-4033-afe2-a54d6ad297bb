# Setting things up on a fresh box
eksctl create cluster 
    --name open-wx-sandbox 
    --region us-east-2  
    --nodegroup-name open-wx-nodes   
    --node-type t3.large   
    --nodes 2   
    --nodes-min 2   
    --nodes-max 3     
    --managed

```
aws iam attach-role-policy --role-name eks-open-wx-node --policy-arn arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy
aws iam attach-role-policy --role-name eks-open-wx-node --policy-arn arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly
aws iam attach-role-policy --role-name eks-open-wx-node --policy-arn arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
```

```
aws eks describe-cluster --name open-wx-sandbox --query "cluster.identity.oidc.issuer" --output text

https://oidc.eks.us-east-2.amazonaws.com/id/4A8D4DF4FC4BF255B71F8BAD6C737CAC
```

```
eksctl create iamserviceaccount \
  --cluster open-wx-sandbox \
  --namespace open-weather \
  --name open-weather-sa \
  --role-arn arn:aws:iam::************:role/OpenWeatherK8S \
  --approve
```