---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-varnish
  namespace: {{ .Values.namespace }}
spec:
  host: varnish.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-proxy
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-proxy.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-wms-gfs
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-wms-gfs.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      consistentHash:
        httpQueryParameterName: "LAYER"
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-edr-gfs
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-edr-gfs.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-jupyter
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-jupyter.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
