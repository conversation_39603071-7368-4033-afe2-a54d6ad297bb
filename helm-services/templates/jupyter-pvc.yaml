# {{- if and .Values.jupyter.enabled .Values.jupyter.persistence.enabled }}
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: jupyter-pvc
#   namespace: {{ .Values.namespace }}
#   labels:
#     app: open-weather-services
#     role: "JUPYTER"
# spec:
#   accessModes:
#     - ReadWriteOnce
#   resources:
#     requests:
#       storage: {{ .Values.jupyter.persistence.size }}
#   {{- if .Values.jupyter.persistence.storageClass }}
#   storageClassName: {{ .Values.jupyter.persistence.storageClass }}
#   {{- end }}
# {{- end }}
