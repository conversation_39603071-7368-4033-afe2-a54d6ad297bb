{{- if .Values.varnish.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: varnish-config
  namespace: {{ .Values.namespace }}
data:
  default.vcl: |
    vcl 4.1;

    backend proxy {
        .host = "open-weather-proxy.{{ .Values.namespace }}.svc.cluster.local";
        .port = "80";
    }

    sub vcl_recv {
        set req.backend_hint = proxy;
        
        # Allow purging from localhost
        if (req.method == "PURGE") {
            if (!client.ip ~ purge) {
                return (synth(405, "Not allowed."));
            }
            return (purge);
        }
        
        # Normalize query parameters for better caching
        if (req.url ~ "\?") {
            set req.url = std.querysort(req.url);
        }
    }

    sub vcl_backend_response {
        # Cache for 5 minutes by default
        if (beresp.ttl <= 0s) {
            set beresp.ttl = 300s;
        }
        
        # Cache WMS GetMap requests for longer
        if (bereq.url ~ "(?i)request=getmap") {
            set beresp.ttl = 3600s;  # 1 hour
        }
        
        # Don't cache error responses
        if (beresp.status >= 400) {
            set beresp.ttl = 0s;
        }
    }

    sub vcl_deliver {
        # Add cache status header
        if (obj.hits > 0) {
            set resp.http.X-Cache = "HIT";
        } else {
            set resp.http.X-Cache = "MISS";
        }
    }

    # Define ACL for purge requests
    acl purge {
        "localhost";
        "127.0.0.1";
        "::1";
    }
{{- end }}
