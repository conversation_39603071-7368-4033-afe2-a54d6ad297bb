{{- if .Values.ingress.public.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: open-weather-virtualservice
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "*"
  gateways:
    - open-weather-gateway
  http:
    - match:
        - uri:
            prefix: "/ogc/WMS"
      route:
        - destination:
            host: varnish.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 80
      corsPolicy:
        allowOrigin:
          - "*"
        allowMethods:
          - GET
          - POST
          - OPTIONS
        allowHeaders:
          - "*"
    - match:
        - uri:
            prefix: "/ogc/edr"
      route:
        - destination:
            host: open-weather-edr-gfs.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 8006
      corsPolicy:
        allowOrigin:
          - "*"
        allowMethods:
          - GET
          - POST
          - OPTIONS
        allowHeaders:
          - "*"
    - match:
        - uri:
            prefix: "/jupyter"
      route:
        - destination:
            host: open-weather-jupyter.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 8888

{{- end }}

{{- if .Values.ingress.internal.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: open-weather-internal-virtualservice
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "*"
  gateways:
    - open-weather-internal-gateway
  http:
    - match:
        - uri:
            prefix: "/"
      route:
        - destination:
            host: open-weather-jupyter.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 8888
{{- end }}
