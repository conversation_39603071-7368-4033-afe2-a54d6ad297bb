{{- range .Values.replicasets }}
{{- if or (eq .INSTANCE_ROLE "WMS_GIS") (eq .INSTANCE_ROLE "WMS_GFS") (eq .INSTANCE_ROLE "EDR_GFS") (eq .INSTANCE_ROLE "PROXY") }}
apiVersion: v1
kind: Service
metadata:
  name: open-weather-{{ .name }}
  namespace: {{ $.Values.namespace }}
  labels:
    app: open-weather-services
    role: {{ .INSTANCE_ROLE | quote }}
spec:
  selector:
    app: open-weather-services
    role: {{ .INSTANCE_ROLE | quote }}
  ports:
    - protocol: TCP
      port: {{ if or (eq .INSTANCE_ROLE "WMS_GIS") (eq .INSTANCE_ROLE "WMS_GFS") }}8008{{ else if eq .INSTANCE_ROLE "EDR_GFS" }}8006{{ else }}80{{ end }}
      targetPort: {{ if or (eq .INSTANCE_ROLE "WMS_GIS") (eq .INSTANCE_ROLE "WMS_GFS") }}8008{{ else if eq .INSTANCE_ROLE "EDR_GFS" }}8006{{ else }}8000{{ end }}
---
{{- end }}
{{- end }}

{{- if .Values.jupyter.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: open-weather-jupyter
  namespace: {{ .Values.namespace }}
  labels:
    app: open-weather-services
    role: "JUPYTER"
spec:
  selector:
    app: open-weather-services
    role: "JUPYTER"
  ports:
    - protocol: TCP
      port: 8888
      targetPort: 8888
      name: jupyter
{{- end }}
