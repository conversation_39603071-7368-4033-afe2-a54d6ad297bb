{{- if .Values.ingress.public.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: open-weather-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
{{- end }}

{{- if .Values.ingress.internal.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: open-weather-internal-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: internal-ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
{{- end }}
