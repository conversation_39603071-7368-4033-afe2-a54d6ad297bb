{{- if .Values.ingress.public.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: open-weather-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
{{- end }}

{{- if .Values.ingress.internal.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: open-weather-internal-ingressgateway
  namespace: {{ .Values.namespace }}
  labels:
    app: istio-ingressgateway
    istio: internal-ingressgateway
spec:
  type: NodePort
  selector:
    app: istio-ingressgateway
    istio: ingressgateway
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http2
      nodePort: 30888
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: open-weather-internal-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: internal-ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
{{- end }}
