{{- range .Values.replicasets }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-weather-{{ .name }}
  namespace: {{ $.Values.namespace }}
  labels:
    app: open-weather-services
    role: {{ .INSTANCE_ROLE | quote }}
spec:
  replicas: {{ .replicas }}
  selector:
    matchLabels:
      app: open-weather-services
      role: {{ .INSTANCE_ROLE | quote }}
  template:
    metadata:
      labels:
        app: open-weather-services
        role: {{ .INSTANCE_ROLE | quote }}
    spec:
      imagePullSecrets:
        - name: ecr-creds  # Use the Kubernetes secret for AWS ECR authentication

      containers:
        - name: open-weather-{{ .name }}
          image: 125100372861.dkr.ecr.us-east-2.amazonaws.com/{{ .image }}:latest
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: aws-credentials
          env:
            # Global environment variables
            {{- range $key, $value := $.Values.globalEnvs }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}

            # Set INSTANCE_ROLE for this deployment
            - name: INSTANCE_ROLE
              value: {{ .INSTANCE_ROLE | quote }}

          resources:
            {{- toYaml .resources | nindent 12 }}
---
{{- end }}

{{- if .Values.jupyter.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-weather-jupyter
  namespace: {{ .Values.namespace }}
  labels:
    app: open-weather-services
    role: "JUPYTER"
spec:
  replicas: {{ .Values.jupyter.replicas }}
  selector:
    matchLabels:
      app: open-weather-services
      role: "JUPYTER"
  template:
    metadata:
      labels:
        app: open-weather-services
        role: "JUPYTER"
    spec:
      imagePullSecrets:
        - name: ecr-creds

      containers:
        - name: jupyter
          image: {{ .Values.jupyter.image }}:latest
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: aws-credentials
          env:
            # Shared environment variables for Jupyter
            - name: IELASTICSEARCHSTORAGE
              value: {{ .Values.globalEnvs.IELASTICSEARCHSTORAGE | quote }}
            - name: ICONTENTREPOSITORY
              value: {{ .Values.globalEnvs.ICONTENTREPOSITORY | quote }}
            - name: AWS_REGION
              value: {{ .Values.globalEnvs.AWS_REGION | quote }}
            - name: INSTANCE_ROLE
              value: "JUPYTER"

          ports:
            - containerPort: 8888
              name: jupyter

          {{- if .Values.jupyter.persistence.enabled }}
          volumeMounts:
            - name: jupyter-storage
              mountPath: /home/<USER>/work
          {{- end }}

          resources:
            {{- toYaml .Values.jupyter.resources | nindent 12 }}

      {{- if .Values.jupyter.persistence.enabled }}
      volumes:
        - name: jupyter-storage
          persistentVolumeClaim:
            claimName: jupyter-pvc
      {{- end }}
{{- end }}
