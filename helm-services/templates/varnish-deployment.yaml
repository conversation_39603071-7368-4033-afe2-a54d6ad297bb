{{- if .Values.varnish.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: varnish
  namespace: {{ .Values.namespace }}
  labels:
    app: open-weather-services
    role: "VARNISH"
spec:
  replicas: {{ .Values.varnish.replicas }}
  selector:
    matchLabels:
      app: open-weather-services
      role: "VARNISH"
  template:
    metadata:
      labels:
        app: open-weather-services
        role: "VARNISH"
    spec:
      containers:
        - name: varnish
          image: {{ .Values.varnish.image }}
          args:
            - "-F"
            - "-f"
            - "/etc/varnish/default.vcl"
            - "-s"
            - "malloc,{{ .Values.varnish.cacheSize }}"
          ports:
            - containerPort: 80
              name: http
          volumeMounts:
            - name: varnish-config
              mountPath: /etc/varnish
          resources:
            {{- toYaml .Values.varnish.resources | nindent 12 }}
      volumes:
        - name: varnish-config
          configMap:
            name: varnish-config
{{- end }}
