# Default values for open weather services
namespace: open-weather-services

# Shared environment variables (applied to all service pods)
globalEnvs:
  # Elasticsearch and content repository
  IELASTICSEARCHSTORAGE: "https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=objectstorage&aws-signature-v4"
  ICONTENTREPOSITORY: "es:https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=ged&aws-signature-v4"
  AWS_REGION: "us-east-2"
  ELASTIC_SEARCH_STORAGE_INDEX: "objectstorage"
  CONTENT_REPOSITORY_INDEX: "ged"
  ICLOUDPLATFORM: "aws"
  LOG_LEVEL: "info"

  # OGC-specific environment variables
  HOSTNAME: "https://ogc.shyftwx.com"
  WMS_WORKER_THREADS: "2"
  WMS_GIS_URL: "http://open-weather-wms-gis.open-weather-services.svc.cluster.local:8008"
  WMS_GFS_URL: "http://open-weather-wms-gfs.open-weather-services.svc.cluster.local:8008"

# Define OGC services
replicasets:
  # OGC WMS services
  - name: "wms-gis"
    image: "open-weather-rocky8-services"
    INSTANCE_ROLE: "WMS_GIS"
    port: 8008
    targetPort: 8008
    replicas: 1
    resources:
      limits:
        cpu: "3000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  - name: "wms-gfs"
    image: "open-weather-rocky8-services"
    INSTANCE_ROLE: "WMS_GFS"
    port: 8008
    targetPort: 8008
    replicas: 1
    resources:
      limits:
        cpu: "5000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  # EDR service
  - name: "edr-gfs"
    image: "open-weather-rocky8-services"
    INSTANCE_ROLE: "EDR_GFS"
    port: 8006
    targetPort: 8006
    replicas: 1
    resources:
      limits:
        cpu: "5000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  # OGC Proxy service (handles routing)
  - name: "proxy"
    image: "open-weather-rocky8-proxy"
    INSTANCE_ROLE: "PROXY"
    replicas: 1
    port: 80
    targetPort: 8000
    resources:
      limits:
        cpu: "250m"
        memory: "256Mi"
      requests:
        cpu: "100m"
        memory: "128Mi"

# Jupyter configuration
jupyter:
  enabled: true
  image: "************.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-jupyter"
  replicas: 1
  port: 8888
  targetPort: 8888
  resources:
    limits:
      cpu: "1000m"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  persistence:
    enabled: true
    size: "2Gi"
    storageClass: ""  # Use default storage class

# Varnish configuration
varnish:
  enabled: true
  replicas: 1
  image: "varnish:latest"
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "250m"
      memory: "512Mi"
  cacheSize: "512M"

# Ingress configuration
ingress:
  public:
    enabled: true
    className: "istio"
  internal:
    enabled: false
    className: "istio"
