<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:mathkernel.gridprocessor.CloudStorageSettings">
<XIBL-STORAGE version="1.1">
  <cloud-storage-settings version="3.0.0" backend="${GED_BUCKET}" key-format="plain-fs">
    <aggregation aggregated-tiles="21" data-format="grib" aggregation-format="object-directory">
      <selection>
        <model>NOAAGFSH</model>
        <level>1000hPa</level>
        <level>975hPa</level>
        <level>950hPa</level>
        <level>925hPa</level>
        <level>900hPa</level>
        <level>850hPa</level>
        <level>800hPa</level>
        <level>750hPa</level>
        <level>700hPa</level>
        <level>650hPa</level>
        <level>600hPa</level>
        <level>550hPa</level>
        <level>500hPa</level>
        <level>450hPa</level>
        <level>400hPa</level>
        <level>350hPa</level>
        <level>300hPa</level>
        <level>250hPa</level>
        <level>200hPa</level>
        <level>150hPa</level>
        <level>100hPa</level>
        <level>70hPa</level>
        <level>50hPa</level>
      </selection>
    </aggregation>
    <aggregation aggregated-tiles="21" data-format="grib" aggregation-format="object-directory">
      <selection>
        <model>NOAAGFSH</model>
        <level>40hPa</level>
        <level>30hPa</level>
        <level>20hPa</level>
        <level>15hPa</level>
        <level>10hPa</level>
        <level>7hPa</level>
        <level>5hPa</level>
        <level>3hPa</level>
        <level>2hPa</level>
        <level>1hPa</level>
        <level>0.70hPa</level>
        <level>0.40hPa</level>
        <level>0.20hPa</level>
        <level>0.10hPa</level>
        <level>0.07hPa</level>
        <level>0.04hPa</level>
        <level>0.02hPa</level>
        <level>0.01hPa</level>
      </selection>
    </aggregation>
    <aggregation aggregated-tiles="21" data-format="grib" aggregation-format="object-directory">
      <selection>
        <model>NOAAGFSH</model>
      </selection>
    </aggregation>
  </cloud-storage-settings>
</XIBL-STORAGE>
