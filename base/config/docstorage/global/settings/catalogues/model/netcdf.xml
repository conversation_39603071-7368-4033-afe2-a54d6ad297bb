<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  29.06.2012
  Authors: <AUTHORS>

  Copyright (C) 2012-2021, IBL Software Engineering spol. s r. o., <<EMAIL>>.
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:netcdfcatalog">
<XIBL-STORAGE version="1.1">
  <model filename="cami*" model-id="CAMI"/>
  <model filename="ECMWF_ERA-40*" model-id="ECMF"/>
  <model source="Australian Bureau of Meteorology" model-id="ACCA"/>
  <model institution="NCAR*" title="model output prepared for IPCC AR4" model-id="IPCC"/>
  <model title="IPSL*" model-id="IPSL"/>
  <model title="Global Ocean from UK Met Office Global FOAM 1/4 degree" model-id="FGloEGRR"/>
  <model title="HadGEM2-ES model output*" model-id="TASEGRR"/>
  <model title="IBL Scattered grid test" model-id="IBLSGTst"/>
  <model filename="YCDF00_CAMI*" model-id="CAMI"/>
  <model filename="YCDF00_ECMW*" model-id="ECMF"/>
  <model filename="YCDF00_IPCC*" model-id="IPCC"/>
  <model filename="YCDF00_CMSK*" model-id="CMSK"/>
  <model filename="YCDF99_TEST*" model-id="MAMO"/>
  <model filename="YCDF00_NZAW*" model-id="NZAW"/>
  <model filename="YCDF99_MSSI*" model-id="MSSI"/>
  <model filename="YCDF01_RHUM*" model-id="RHUM"/>
  <model filename="YCDF01_TOPO*" model-id="TOPO"/>
  <model filename="YCDF10_APME*" model-id="APME"/>
  <model title="OceanMAPS*" model-id="OCEAN"/>
  <model filename="umglaa*" model-id="GlobEGRR"/>
  <model filename="ume4aa*" model-id="Eur4EGRR"/>
  <model filename="YCDF99_CONU*" model-id="CONUS"/>
  <model filename="YCDF00_EGRR*" model-id="CHEMEGRR"/>
  <model filename="YCDF98_NORW*" model-id="NORWAY"/>
  <model filename="YCDF01_HARM*" model-id="HARM"/>
  <model filename="YCDF99_WAFC*" model-id="WAFC"/>
  <model filename="YCDF01_KKCI*" model-id="NCWFKKCI"/>
  <model filename="YCDF99_HIMA*" model-id="HIMA"/>
  <model filename="IDE002*" model-id="HIMA"/>
  <model filename="YCDF99_NMMB*" model-id="BSCD"/>
  <model filename="YCDF00_KAWC*" model-id="KAWC"/>
  <model filename="YCDF15_AGFE*" model-id="ADFD"/>
  <model filename="YCDF15_TEST*" model-id="TEST"/>
  <model title="AGLS*" model-id="AGLS"/>
  <model filename="OR_ABI-L1b-RadF-*_G16_*" model-id="ABIG16FD"/>
  <model filename="OR_ABI-L1b-RadF-*_G17_*" model-id="ABIG17FD"/>
  <model filename="/S_NWC_.*_MSG4_.*[0-9]{6}T[0-9]{6}Z\.nc/" model-id="NWCSEur"/>
  <model filename="S-OSI_-FRA_*-GLB*" model-id="OSISGSST"/>
  <model filename="S-OSI_-NOR_*-OSSTIST*3A*" model-id="OSISNHLM"/>
  <model filename="S-OSI_-NOR_*-OSSTIST*3B*" model-id="OSISNHLN"/>
  <model filename="S-OSI_-FRA_-MSG*" model-id="OSISMSG"/>
  <model filename="S-OSI_-FRA_*-GOES*" model-id="OSISGOES"/>
  <model filename="S-OSI_-FRA_*-DLI*" model-id="OSISMDLI"/>
  <model filename="S-OSI_-DMI_*-MULT*_SH_CONC*" model-id="OSISICDs"/>
  <model filename="S-OSI_-DMI_*-MULT*_NH_CONC*" model-id="OSISICDn"/>
  <model filename="S-OSI_-DMI_*-AMSR*_SH_CONC*" model-id="OSISICGs"/>
  <model filename="S-OSI_-DMI_*-AMSR*_NH_CONC*" model-id="OSISICGn"/>
  <model filename="S-OSI_-NOR_*_SH_EDGE*" model-id="OSISSIEs"/>
  <model filename="S-OSI_-NOR_*_NH_EDGE*" model-id="OSISSIEn"/>
  <model filename="S-OSI_-NOR_*_SH_TYPE*" model-id="OSISSITs"/>
  <model filename="S-OSI_-NOR_*_NH_TYPE*" model-id="OSISSITn"/>
  <model filename="S-OSI_-DMI_*_SH_EMIS*" model-id="OSISSIMs"/>
  <model filename="S-OSI_-DMI_*_NH_EMIS*" model-id="OSISSIMn"/>
  <model filename="YCDF*_WRFD_*" model-id="WRFARW-D"/>
  <model filename="YCDF*_WRFE_*" model-id="WRFARW-E"/>
  <model filename="YCDF*_WRFC_*" model-id="WRF-Chem"/>
  <model filename="FWI_*" model-id="ECMF"/>
  <model-scheme model-id="CAMI" scheme="CAMI"/>
  <model-scheme model-id="ECMF" scheme="ECMWF"/>
  <model-scheme model-id="IPCC" scheme="NCAR"/>
  <model-scheme model-id="IPSL" scheme="IPSL"/>
  <model-scheme model-id="RHUM" scheme="NCM"/>
  <model-scheme model-id="TOPO" scheme="ROSETOPO"/>
  <model-scheme model-id="ACCA" scheme="au.bom.access"/>
  <model-scheme model-id="APME" scheme="au.bom.pme"/>
  <model-scheme model-id="ADFD" scheme="au.bom.adfd"/>
  <model-scheme model-id="CMSK" scheme="COSMO"/>
  <model-scheme model-id="FGloEGRR" scheme="STANDARD"/>
  <model-scheme model-id="TASEGRR" scheme="STANDARD"/>
  <model-scheme model-id="MAMO" scheme="MAMO"/>
  <model-scheme model-id="CONUS" scheme="CONUS"/>
  <model-scheme model-id="NZAW" scheme="NZAW-DISPERSION"/>
  <model-scheme model-id="CHEMEGRR" scheme="CHEMEGRR"/>
  <model-scheme model-id="NORWAY" scheme="STANDARD"/>
  <model-scheme model-id="OCEAN" scheme="STANDARD"/>
  <model-scheme model-id="AGLS" scheme="AGLS"/>
  <model-scheme model-id="GlobEGRR" scheme="UM"/>
  <model-scheme model-id="Eur4EGRR" scheme="UM"/>
  <model-scheme model-id="MSSI" scheme="nz.metservice.satellites"/>
  <model-scheme model-id="HIMA" scheme="Himawari"/>
  <model-scheme model-id="HARM" scheme="HARM"/>
  <model-scheme model-id="WAFC" scheme="WAFC"/>
  <model-scheme model-id="NCWFKKCI" scheme="NCWFKKCI"/>
  <model-scheme model-id="BSCD" scheme="BSCD"/>
  <model-scheme model-id="KAWC" scheme="KAWCS"/>
  <model-scheme model-id="TEST" scheme="TEST"/>
  <model-scheme model-id="ABIG16FD" scheme="GOES-ABI-L1b"/>
  <model-scheme model-id="ABIG17FD" scheme="GOES-ABI-L1b"/>
  <model-scheme model-id="NWCSEur" scheme="NWCSAF"/>
  <model-scheme model-id="OSISGSST" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISNHLM" scheme="OSI-SAF-flipped"/>
  <model-scheme model-id="OSISNHLN" scheme="OSI-SAF-flipped"/>
  <model-scheme model-id="OSISMSG" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISGOES" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISMDLI" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISICDs" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISICDn" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISICGs" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISICGn" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSIEs" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSIEn" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSITs" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSITn" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSIMs" scheme="OSI-SAF"/>
  <model-scheme model-id="OSISSIMn" scheme="OSI-SAF"/>
  <model-scheme model-id="WRF-Chem" scheme="WRF-Chem"/>
  <model-scheme model-id="WRFARW-D" scheme="WRFARW"/>
  <model-scheme model-id="WRFARW-E" scheme="WRFARW"/>
  <model-scheme model-id="IBLSGTst" scheme="ibl.sg.test"/>
  <scheme id="CAMI">
    <hints>
      <vertical-coordinate name="lev" hybrid-level-offset="0.5"/>
    </hints>
    <parameter name="U" parameter="500033"/>
    <parameter name="V" parameter="500034"/>
    <parameter name="T" parameter="500011"/>
    <parameter name="Q" parameter="500051"/>
    <parameter name="PS" parameter="500001" level="gnd-surf"/>
    <parameter name="PHIS" parameter="500006" level="gnd-surf"/>
    <parameter name="SGH" parameter="500009" level="gnd-surf"/>
    <parameter name="LANDM" parameter="500081" level="gnd-surf"/>
    <parameter name="PBLH" parameter="1000786" level="gnd-surf"/>
    <parameter name="CLOUD" parameter="500072"/>
    <parameter name="TS" parameter="500011" level="gnd-surf"/>
    <parameter name="TSICE" parameter="1655880" level="gnd-surf"/>
    <parameter name="TS1" parameter="1131074" level="10 cm"/>
    <parameter name="TS2" parameter="1131074" level="20 cm"/>
    <parameter name="TS3" parameter="1131074" level="30 cm"/>
    <parameter name="TS4" parameter="1131074" level="40 cm"/>
    <parameter name="SNOWHICE" parameter="500065" level="gnd-surf"/>
    <parameter name="TBOT" parameter="500011" level="2m"/>
    <parameter name="ICEFRAC" parameter="1001557" level="gnd-surf"/>
    <parameter name="SICTHK" parameter="1655873" level="0mbs"/>
  </scheme>
  <scheme id="CHEMEGRR">
    <parameter name="field01" parameter="1005171" level="2m"/>
  </scheme>
  <scheme id="CONUS">
    <parameter name="rainfall_rate" parameter="500061" level="gnd-surf"/>
  </scheme>
  <scheme id="au.bom.access">
    <parameter name="abl_ht" parameter="1000786" level="gnd-surf"/>
    <parameter name="accum_conv_prcp" parameter="500063" level="gnd-surf" accumulated="true"/>
    <parameter name="accum_prcp" parameter="500061" level="gnd-surf" accumulated="true"/>
    <parameter name="air_temp" parameter="500011"/>
    <parameter name="cape" parameter="1001798" level="gnd-surf"/>
    <parameter name="ciwc" parameter="1000340"/>
    <parameter name="clwc" parameter="1000339"/>
    <parameter name="conv_cldbse_pres" parameter="500001" level="conv-cld-bottom"/>
    <parameter name="conv_cldtop_pres" parameter="500001" level="conv-cld-top"/>
    <parameter name="dewpt" parameter="500017"/>
    <parameter name="dewpt20t" parameter="500017" level="20m"/>
    <parameter name="dewpt_scrn" parameter="500017" level="2m"/>
    <parameter name="dmape" parameter="750076" level="gnd-surf"/>
    <parameter name="fog_fraction" parameter="729210" level="gnd-surf"/>
    <parameter name="geop_ht" parameter="500007"/>
    <parameter name="hi_cld" parameter="500075" level="gnd-surf"/>
    <parameter name="li500" parameter="732010" level="gnd-surf"/>
    <parameter name="low_cld" parameter="500073" level="gnd-surf"/>
    <parameter name="merid_wnd" parameter="500034"/>
    <parameter name="mid_cld" parameter="500074" level="gnd-surf"/>
    <parameter name="mslp" parameter="500002" level="mean-sea"/>
    <parameter name="precwtr" parameter="500054" level="atmosphere"/>
    <parameter name="pressure" parameter="500001"/>
    <parameter name="relhum" parameter="500052"/>
    <parameter name="sfc_pres" parameter="500001" level="gnd-surf"/>
    <parameter name="spec_hum" parameter="500051"/>
    <parameter name="temp_scrn" parameter="500011" level="2m"/>
    <parameter name="tmax_scrn" parameter="500015" level="2m"/>
    <parameter name="tmin_scrn" parameter="500016" level="2m"/>
    <parameter name="ttl_cld" parameter="500071" level="gnd-surf"/>
    <parameter name="u10" parameter="500033" level="10m" vector-components-uv="true"/>
    <parameter name="v10" parameter="500034" level="10m" vector-components-uv="true"/>
    <parameter name="u50r" parameter="500033" level="50m"/>
    <parameter name="v50r" parameter="500034" level="50m"/>
    <parameter name="vertical_wnd" parameter="500040"/>
    <parameter name="vis_prob" parameter="500020" level="gnd-surf" dataset="prob:x&lt;5000"/>
    <parameter name="visibility" parameter="500020" level="gnd-surf"/>
    <parameter name="wndgust10m" parameter="1000534" level="10m"/>
    <parameter name="zonal_wnd" parameter="500033"/>
  </scheme>
  <scheme id="ECMWF">
    <parameter name="tcw" parameter="500054" level="atmosphere"/>
    <parameter name="tcwv" parameter="1000320" level="atmosphere"/>
    <parameter name="lsp" parameter="500062" level="gnd-surf" dataset="stat:accumulation/PT6H"/>
    <parameter name="cp" parameter="500063" level="gnd-surf" dataset="stat:accumulation/PT6H"/>
    <parameter name="msl" parameter="500002" level="mean-sea"/>
    <parameter name="blh" parameter="1000786" level="gnd-surf"/>
    <parameter name="tcc" parameter="500071" level="gnd-surf"/>
    <parameter name="p10u" parameter="500033" level="10m"/>
    <parameter name="p10v" parameter="500034" level="10m"/>
    <parameter name="p2t" parameter="500011" level="2m"/>
    <parameter name="p2d" parameter="500017" level="2m"/>
    <parameter name="e" parameter="1000262" level="gnd-surf"/>
    <parameter name="lcc" parameter="500073" level="gnd-surf"/>
    <parameter name="mcc" parameter="500074" level="gnd-surf"/>
    <parameter name="hcc" parameter="500075" level="gnd-surf"/>
    <parameter name="tco3" parameter="1003584" level="atmosphere"/>
    <parameter name="tp" parameter="500061" level="gnd-surf" dataset="stat:accumulation/PT6H"/>
    <!-- ECMWF Fire danger forecast (Copernicus) -->
    <parameter name="fwi" parameter="1132101" level="gnd-surf"/>
    <parameter name="ffmc" parameter="1132102" level="gnd-surf"/>
    <parameter name="dmc" parameter="1132103" level="gnd-surf"/>
    <parameter name="dc" parameter="1132104" level="gnd-surf"/>
    <parameter name="isi" parameter="1132105" level="gnd-surf"/>
    <parameter name="bui" parameter="1132106" level="gnd-surf"/>
    <parameter name="dsr" parameter="1132107" level="gnd-surf"/>
    <parameter name="danger_risk" parameter="1132288" level="gnd-surf"/>
    <hints>
      <attribute-override key="fwi:units">
        <value value="1"/>
      </attribute-override>
      <attribute-override key="isi:units">
        <value value="1"/>
      </attribute-override>
      <attribute-override key="bui:units">
        <value value="1"/>
      </attribute-override>
      <attribute-override key="dsr:units">
        <value value="1"/>
      </attribute-override>
      <attribute-override key="danger_risk:units">
        <value value="1"/>
      </attribute-override>
    </hints>
  </scheme>
  <scheme id="STANDARD">
    <parameter standard-name="convective_snowfall_flux" parameter="1000016" level="gnd-surf"/>
    <parameter standard-name="geopotential_height" parameter="1000773"/>
    <parameter standard-name="surface_air_pressure" parameter="500001" level="gnd-surf"/>
    <parameter standard-name="air_pressure" parameter="500001"/>
    <parameter standard-name="air_pressure_at_sea_level" parameter="500002" level="mean-sea"/>
    <parameter standard-name="air_temperature" parameter="500011"/>
    <parameter standard-name="surface_temperature" parameter="500011" level="gnd-surf"/>
    <parameter standard-name="dew_point_temperature" parameter="500017"/>
    <parameter standard-name="relative_humidity" parameter="500052"/>
    <parameter standard-name="specific_humidity" parameter="1000256"/>
    <parameter standard-name="x_wind" parameter="500033" vector-components-uv="true"/>
    <parameter standard-name="grid_eastward_wind" parameter="500033" vector-components-uv="true"/>
    <parameter standard-name="y_wind" parameter="500034" vector-components-uv="true"/>
    <parameter standard-name="grid_northward_wind" parameter="500034" vector-components-uv="true"/>
    <parameter standard-name="geopotential" parameter="500006"/>
    <parameter standard-name="lagrangian_tendency_of_air_pressure" parameter="500039"/>
    <parameter standard-name="omega" parameter="500039"/>
    <parameter standard-name="vertical_air_velocity_expressed_as_tendency_of_pressure" parameter="500039"/>
    <parameter standard-name="upward_air_velocity" parameter="500040"/>
    <parameter standard-name="lwe_thickness_of_surface_snow_amount" parameter="1000316"/>
    <parameter standard-name="surface_specific_humidity" parameter="500051" level="gnd-surf"/>
    <parameter standard-name="wind_speed_of_gust" parameter="1000534"/>
    <parameter standard-name="precipitation_amount" parameter="1000264" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="snowfall_amount" parameter="1000306" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="stratiform_rainfall_amount" parameter="1000303" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="large_scale_rainfall_amount" parameter="1000303" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="stratiform_snowfall_amount" parameter="1000271" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="large_scale_snowfall_amount" parameter="1000271" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="convective_rainfall_amount" parameter="1000304" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="convective_snowfall_amount" parameter="1000270" level="gnd-surf" accumulated="true"/>
    <parameter standard-name="precipitation_rate" parameter="1000263" level="gnd-surf"/>
    <parameter standard-name="stratiform_rainfall_rate" parameter="1000333" level="gnd-surf"/>
    <parameter standard-name="large_scale_rainfall_rate" parameter="1000333" level="gnd-surf"/>
    <parameter standard-name="stratiform_snowfall_rate" parameter="1000312" level="gnd-surf"/>
    <parameter standard-name="large_scale_snowfall_rate" parameter="1000312" level="gnd-surf"/>
    <parameter standard-name="convective_rainfall_rate" parameter="1000332" level="gnd-surf"/>
    <parameter standard-name="convective_snowfall_rate" parameter="1000311" level="gnd-surf"/>
    <parameter standard-name="sea_water_potential_temperature" parameter="1656128"/>
    <parameter standard-name="sea_water_salinity" parameter="500088"/>
    <parameter standard-name="sea_water_x_velocity" parameter="1655618"/>
    <parameter standard-name="sea_water_y_velocity" parameter="1655619"/>
    <parameter standard-name="sea_surface_height" parameter="1655363"/>
    <parameter standard-name="low_type_cloud_area_fraction" parameter="1001539" level="gnd-surf"/>
    <parameter standard-name="medium_type_cloud_area_fraction" parameter="1001540" level="gnd-surf"/>
    <parameter standard-name="high_type_cloud_area_fraction" parameter="1001541" level="gnd-surf"/>
    <parameter standard-name="soil_temperature" parameter="1131074"/>
  </scheme>
  <scheme id="COSMO">
    <parameter name="TMIN_2M" parameter="1000005"/>
    <parameter name="TMAX_2M" parameter="1000004"/>
    <parameter name="CLCT" parameter="500072" level="gnd-surf"/>
    <parameter name="CLCL" parameter="500073" level="gnd-surf"/>
    <parameter name="CLCM" parameter="500074" level="gnd-surf"/>
    <parameter name="CLCH" parameter="500075" level="gnd-surf"/>
    <inherits>STANDARD</inherits>
  </scheme>
  <scheme id="UM">
    <parameter long-name="precipitation_rate" parameter="1000263" level="gnd-surf"/>
    <parameter long-name="stratiform_rainfall_rate" parameter="1000303" level="gnd-surf"/>
    <parameter long-name="large_scale_rainfall_rate" parameter="1000303" level="gnd-surf"/>
    <parameter long-name="stratiform_snowfall_rate" parameter="1000312" level="gnd-surf"/>
    <parameter long-name="large_scale_snowfall_rate" parameter="1000312" level="gnd-surf"/>
    <parameter long-name="convective_rainfall_rate" parameter="1000332" level="gnd-surf"/>
    <parameter long-name="convective_snowfall_rate" parameter="1000311" level="gnd-surf"/>
    <parameter long-name="cloud_base_altitude_assuming_only_consider_cloud_area_fraction_greater_than_2p5_oktas" parameter="651252" level="gnd-surf"/>
    <parameter long-name="cloud_base_altitude_assuming_only_consider_cloud_area_fraction_greater_than_4p5_oktas" parameter="651252" level="gnd-surf"/>
    <parameter long-name="cloud_area_fraction_assuming_random_overlap" parameter="1001537" level="gnd-surf"/>
    <parameter long-name="cloud_area_fraction_assuming_maximum_random_overlap" parameter="1001537" level="gnd-surf"/>
    <parameter long-name="cloud_area_fraction_assuming_only_consider_surface_to_1000_feet_asl" parameter="1064017" level="gnd-surf"/>
    <parameter long-name="flash_number" parameter="1263426" level="atmosphere"/>
    <inherits>STANDARD</inherits>
  </scheme>
  <scheme id="MAMO">
    <parameter name="l3m_data" parameter="1000000" level="gnd-surf"/>
  </scheme>
  <scheme id="NCAR">
    <parameter name="pr" parameter="1000263" level="gnd-surf"/>
    <parameter name="tas" parameter="1000000" level="gnd-surf"/>
    <parameter name="ua" parameter="500033"/>
  </scheme>
  <scheme id="IPSL">
    <parameter standard-name="sea_surface_temperature" parameter="1000000" level="gnd-surf"/>
  </scheme>
  <scheme id="NCM">
    <parameter name="rhum" parameter="500052"/>
  </scheme>
  <scheme id="ROSETOPO">
    <parameter name="ROSE" parameter="500007" level="gnd-surf"/>
  </scheme>
  <scheme id="au.bom.pme">
    <hints>
      <variable-role key="forc_hrs" value="forecast-coordinate"/>
    </hints>
    <parameter name="hr24_prcp" parameter="500061" level="gnd-surf" dataset="stat:accumulation/PT24H"/>
    <parameter name="hr24_prcp_prob100mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;100"/>
    <parameter name="hr24_prcp_prob50mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;50"/>
    <parameter name="hr24_prcp_prob25mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;25"/>
    <parameter name="hr24_prcp_prob15mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;15"/>
    <parameter name="hr24_prcp_prob10mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;10"/>
    <parameter name="hr24_prcp_prob5mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;5"/>
    <parameter name="hr24_prcp_prob1mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;1"/>
    <parameter name="hr24_prcp_probPT2mm" parameter="500061" level="gnd-surf" dataset="prob:x&lt;0.2"/>
  </scheme>
  <scheme id="au.bom.adfd">
    <parameter name="T_SFC" parameter="500011" level="gnd-surf"/>
    <parameter name="Td_SFC" parameter="500017" level="gnd-surf"/>
    <parameter name="MaxT_SFC" parameter="500015" level="gnd-surf"/>
    <parameter name="MinT_SFC" parameter="500016" level="gnd-surf"/>
    <parameter name="RH_SFC" parameter="500052" level="gnd-surf"/>
    <parameter name="Precip_SFC" parameter="500061" level="gnd-surf"/>
    <parameter name="DailyPrecip_SFC" parameter="500061" level="gnd-surf"/>
    <parameter name="KBDI_SFC" parameter="692191" level="gnd-surf"/>
    <parameter name="GFDI_SFC" parameter="692192" level="gnd-surf"/>
    <parameter name="FFDI_SFC" parameter="692193" level="gnd-surf"/>
    <parameter name="CHaines_SFC" parameter="692194" level="gnd-surf"/>
    <parameter name="DF_SFC" parameter="692195" level="gnd-surf"/>
    <parameter name="Curing_SFC" parameter="692196" level="gnd-surf"/>
    <parameter name="GrassFuelLoad_SFC" parameter="692197" level="gnd-surf"/>
    <parameter name="Wind_Dir_SFC" parameter="20" level="gnd-surf"/>
    <parameter name="Wind_Mag_SFC" parameter="21" level="gnd-surf"/>
    <parameter name="WindGust_SFC" parameter="500180" level="gnd-surf"/>
  </scheme>
  <scheme id="nz.metservice.satellites">
    <hints>
      <dimension-role key="line_svissr_ir1" value="axis-y"/>
      <dimension-role key="line_svissr_ir2" value="axis-y"/>
      <dimension-role key="line_svissr_ir3" value="axis-y"/>
      <dimension-role key="line_svissr_ir4" value="axis-y"/>
      <dimension-role key="line_svissr_vis" value="axis-y"/>
      <dimension-role key="sample_svissr_ir1" value="axis-x"/>
      <dimension-role key="sample_svissr_ir2" value="axis-x"/>
      <dimension-role key="sample_svissr_ir3" value="axis-x"/>
      <dimension-role key="sample_svissr_ir4" value="axis-x"/>
      <dimension-role key="sample_svissr_vis" value="axis-x"/>
    </hints>
    <parameter name="avhrr_ch1" parameter="1001028" level="0.60um" dataset="satellite:223/590"/>
    <parameter name="avhrr_ch2" parameter="1001028" level="0.80um" dataset="satellite:223/590"/>
    <parameter name="avhrr_ch3" parameter="1001028" level="1.60um" dataset="satellite:223/590"/>
    <parameter name="avhrr_ch4" parameter="1001028" level="11.00um" dataset="satellite:223/590"/>
    <parameter name="avhrr_ch5" parameter="1001028" level="12.00um" dataset="satellite:223/590"/>
    <parameter name="svissr_ir1" parameter="1001028" level="11.00um" dataset="satellite:171/296"/>
    <parameter name="svissr_ir2" parameter="1001028" level="12.00um" dataset="satellite:171/296"/>
    <parameter name="svissr_ir3" parameter="1001028" level="7.00um" dataset="satellite:171/296"/>
    <parameter name="svissr_ir4" parameter="1001028" level="4.00um" dataset="satellite:171/296"/>
    <parameter name="svissr_vis" parameter="1004865" level="0.90um" dataset="satellite:171/296"/>
    <parameter name="svissr_vis_fine" parameter="1004865" level="0.90um" dataset="satellite:171/489"/>
  </scheme>
  <scheme id="Himawari">
    <hints collection-data-type="observation"/>
    <parameter name="tbb" parameter="500118" level="gnd-surf"/>
    <parameter name="channel_0001_brf" parameter="1196609" level="0.47um"/>
    <parameter name="channel_0002_brf" parameter="1196609" level="0.51um"/>
    <parameter name="channel_0003_brf" parameter="1196609" level="0.64um"/>
    <parameter name="channel_0004_brf" parameter="1196609" level="0.86um"/>
    <parameter name="channel_0007_brightness_temperature" parameter="1196610" level="3.90um"/>
    <parameter name="channel_0008_brightness_temperature" parameter="1196610" level="6.20um"/>
    <parameter name="channel_0009_brightness_temperature" parameter="1196610" level="6.90um"/>
    <parameter name="channel_0010_brightness_temperature" parameter="1196610" level="7.30um"/>
    <parameter name="channel_0011_brightness_temperature" parameter="1196610" level="8.60um"/>
    <parameter name="channel_0013_brightness_temperature" parameter="1196610" level="10.40um"/>
    <parameter name="channel_0015_brightness_temperature" parameter="1196610" level="12.40um"/>
  </scheme>
  <scheme id="uk.metoffice.foam"/>
  <scheme id="NZAW-DISPERSION">
    <parameter name="p006" parameter="1005171" dataset="area:006"/>
    <parameter name="p020" parameter="1005171" dataset="area:020"/>
    <parameter name="p065" parameter="1005171" dataset="area:065"/>
    <parameter name="p200" parameter="1005171" dataset="area:200"/>
    <parameter name="p650" parameter="1005171" dataset="area:650"/>
  </scheme>
  <scheme id="AGLS">
    <parameter name="channel_0001_reflectance" parameter="1196873" level="channel 1"/>
    <parameter name="channel_0002_brightness_temperature" parameter="1001028" level="channel 2"/>
    <parameter name="channel_0003_brightness_temperature" parameter="1001028" level="channel 3"/>
    <parameter name="channel_0004_brightness_temperature" parameter="1001028" level="channel 4"/>
    <parameter name="channel_0005_brightness_temperature" parameter="1001028" level="channel 5"/>
  </scheme>
  <scheme id="HARM">
    <parameter name="to3_noon" parameter="500010" level="gnd-surf"/>
  </scheme>
  <scheme id="WAFC">
    <parameter name="WCS_ICAO_WAFC_GRIB_Temp_300hPa" parameter="1000000" level="300hPa"/>
  </scheme>
  <scheme id="NCWFKKCI">
    <hints>
      <attribute-override key="Convective_weather_detection_index_entire_atmosphere:units">
        <value value=""/>
      </attribute-override>
    </hints>
    <parameter name="Convective_weather_detection_index_entire_atmosphere" long-name="Convective weather detection index @ Entire atmosphere" parameter="502202" level="atmosphere"/>
  </scheme>
  <scheme id="BSCD">
    <parameter name="od550_dust" parameter="755001"/>
    <parameter name="sconc_dust" parameter="755002"/>
  </scheme>
  <scheme id="KAWCS">
    <hints implicit-projection-plane-rectified="true">
      <attribute-override key="fmcf0020:units">
        <value value="mm"/>
      </attribute-override>
      <attribute-override key="lat:units">
        <value value="degrees_north"/>
      </attribute-override>
      <attribute-override key="lon:units">
        <value value="degrees_east"/>
      </attribute-override>
    </hints>
    <parameter name="fmcf*" parameter="500054" level="atmosphere"/>
  </scheme>
  <scheme id="TEST">
    <hints implicit-projection-plane-rectified="true">
      <attribute-role key="valid-min" value=""/>
      <attribute-role key="valid-max" value=""/>
      <attribute-role key="scale-factor" value="scale"/>
      <attribute-role key="add-offset" value="offset"/>
      <dimension-role key="lines" value="axis-y"/>
      <dimension-role key="samples" value="axis-x"/>
      <variable-role key="LATITUDE" value="latitude-field"/>
      <variable-role key="LONGITUDE" value="longitude-field"/>
    </hints>
    <parameter name="VIS" parameter="500020" level="gnd-surf"/>
    <parameter name="MWIR1" parameter="1001028" level="channel 1"/>
    <parameter name="cMWIR1" parameter="1001028" level="channel 2"/>
    <parameter name="WV" parameter="1001028" level="channel 5"/>
    <parameter name="cWV" parameter="1001028" level="channel 6"/>
    <parameter name="TIR" parameter="1001028" level="channel 10"/>
    <parameter name="cTIR" parameter="1001028" level="channel 11"/>
    <parameter name="GEOGRAPHY" parameter="500008" level="gnd-surf"/>
    <parameter name="CLOUD_TEMP" parameter="500011" level="cloud-base"/>
    <parameter name="CLOUD_BASE" parameter="500007" level="cloud-base"/>
    <parameter name="CLOUD_TOP_HEIGHT" parameter="500007" level="cloud-top"/>
    <parameter name="convective_rainfall_rate_max" parameter="1000332" level="gnd-surf"/>
  </scheme>
  <scheme id="GOES-ABI-L1b">
    <hints>
      <variable-role key="t" value="none"/>
    </hints>
    <parameter standard-name="toa_outgoing_radiance_per_unit_wavenumber" parameter="1001029"/>
    <parameter standard-name="toa_outgoing_radiance_per_unit_wavelength" parameter="1001030"/>
  </scheme>
  <scheme id="NWCSAF">
    <hints>
      <attribute-role key="reference-time" value="nominal_product_time"/>
      <variable-role key="lat" value="latitude-field"/>
      <variable-role key="lon" value="longitude-field"/>
      <variable-role key="nx" value="axis-x-coordinate"/>
      <variable-role key="ny" value="axis-y-coordinate"/>
    </hints>
    <parameter name="ctth_alti" parameter="1000774" level="cloud-top"/>
    <parameter name="ctth_pres" parameter="1000768" level="cloud-top" dataset="spec:regular"/>
    <parameter name="ctth_tempe" parameter="1000000" level="cloud-top" dataset="spec:regular"/>
    <parameter name="ctth_quality" parameter="1253204" level="cloud-top" dataset="spec:regular"/>
    <parameter name="ct" parameter="1253185" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_phase" parameter="1253187" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_reff" parameter="1253188" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_cot" parameter="1253189" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_lwp" parameter="1253190" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_iwp" parameter="1253191" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cmic_quality" parameter="1253192" level="cloud-top" dataset="spec:regular"/>
    <parameter name="pc" parameter="1253193" level="cloud-top" dataset="spec:regular"/>
    <parameter name="pc_quality" parameter="1253194" level="cloud-top" dataset="spec:regular"/>
    <parameter name="pcph" parameter="1253195" level="cloud-top" dataset="spec:regular"/>
    <parameter name="pcph_quality" parameter="1253196" level="cloud-top" dataset="spec:regular"/>
    <parameter name="crr" parameter="1253197" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="crr_intensity" parameter="1253198" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="crr_accum" parameter="1253199" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="crrph_intensity" parameter="1253201" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="crrph_accum" parameter="1253202" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="crrph_iqf" parameter="1253203" level="gnd-surf" dataset="spec:regular"/>
    <parameter name="ishai_tpw" parameter="1000259" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ishai_bl" parameter="1000259" level="850-1013hPa" dataset="spec:regular"/>
    <parameter name="ishai_ml" parameter="1000259" level="500-850hPa" dataset="spec:regular"/>
    <parameter name="ishai_hl" parameter="1000259" level="10-500hPa" dataset="spec:regular"/>
    <parameter name="ishai_li" parameter="1064055" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ishai_ki" parameter="1064051" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ishai_shw" parameter="1064053" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ishai_toz" parameter="1003584" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ishai_skt" parameter="1000017" level="atmosphere" dataset="spec:regular"/>
    <parameter name="asii_turb_trop_prob" parameter="1253205" level="tropopause" dataset="spec:regular"/>
    <parameter name="ci_prob30" parameter="1253207" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ci_prob60" parameter="1253208" level="atmosphere" dataset="spec:regular"/>
    <parameter name="ci_prob90" parameter="1253209" level="atmosphere" dataset="spec:regular"/>
    <parameter name="MapCellCatType" parameter="1253211" level="atmosphere" dataset="spec:regular"/>
    <parameter name="cma" parameter="1196615" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cma_cloudsnow" parameter="1253212" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cma_dust" parameter="1253213" level="cloud-top" dataset="spec:regular"/>
    <parameter name="cma_volcanic" parameter="1253214" level="cloud-top" dataset="spec:regular"/>
    <parameter name="ct_cumuliform" parameter="1253217" level="cloud-top" dataset="spec:regular"/>
    <parameter name="ct_multilayer" parameter="1253218" level="cloud-top" dataset="spec:regular"/>
    <parameter name="asii_turb_wave_prob" parameter="1253219" level="atmosphere" dataset="spec:regular"/>
  </scheme>
  <scheme id="OSI-SAF">
    <hints>
      <variable-role key="time" value="none"/>
	  <variable-role key="xc" value="axis-x-coordinate"/>
	  <variable-role key="yc" value="axis-y-coordinate"/>
    </hints>
    <parameter name="sea_surface_temperature" parameter="500011" level="gnd-surf"/>
	<parameter name="surface_temperature" parameter="754187" level="gnd-surf"/>
    <parameter name="wind_speed" parameter="703025" level="10m"/>
    <parameter name="sea_ice_fraction" parameter="628031" level="gnd-surf"/>
	<parameter name="ice_conc" parameter="500091" level="gnd-surf"/>
	<parameter name="dli" parameter="500115" level="gnd-surf"/>
	<parameter name="ssi" parameter="660176" level="gnd-surf"/>
	<parameter name="ice_edge" parameter="1253440" level="gnd-surf"/>
	<parameter name="ice_type" parameter="1253441" level="gnd-surf"/>
	<parameter name="ev" parameter="1253442" level="gnd-surf"/>
  </scheme>
  <scheme id="OSI-SAF-flipped">
    <hints>
      <attribute-override key="yc:scale_factor">
        <value value="-1.0"/>
      </attribute-override>
    </hints>
    <inherits>OSI-SAF</inherits>
  </scheme>
  <scheme id="WRFARW">
    <hints>
      <vertical-coordinate name="z" advertise="false"/>
    </hints>
    <parameter name="air_temperature_2m" parameter="500011" level="2m"/>
    <parameter name="x_wind_10" parameter="500033" level="10m" vector-components-uv="true"/>
    <parameter name="y_wind_10" parameter="500034" level="10m" vector-components-uv="true"/>
    <parameter name="specific_humidity_2m" parameter="500051" level="2m"/>
    <parameter standard-name="land_use" parameter="1131080" level="gnd-surf"/>
    <parameter standard-name="vegetation_fraction" parameter="628199" level="gnd-surf"/>
    <parameter standard-name="surface_snow_amount" parameter="500166" level="gnd-surf"/>
    <parameter standard-name="surface_altitude" parameter="500148" level="gnd-surf"/>
    <parameter standard-name="large_scale_precipitation_amount" parameter="1000310" level="gnd-surf"/>
    <parameter standard-name="cloud_fraction" parameter="500071"/>
    <parameter standard-name="sea_surface_temperature" parameter="628034" level="gnd-surf"/>
    <inherits>STANDARD</inherits>
  </scheme>
  <scheme id="WRF-Chem">
    <parameter parameter="1000773" name="surface_altitude" level="gnd-surf"/>
    <parameter parameter="13582912" name="aerosol_opt_depth"/>
    <parameter parameter="13582913" name="dust_loft" level="atmosphere"/>
    <parameter parameter="13582914" name="dry_dep_vel" level="atmosphere"/>
    <parameter parameter="13582915" name="pm10_dry_mass"/>
    <parameter parameter="13582916" name="pm25_aerosol_dry_mass"/>
    <parameter parameter="13582917" name="total_dust_conc"/>
    <parameter parameter="13582918" name="total_dust_eflux" level="atmosphere"/>
    <parameter parameter="13582919" name="dust_size_bin_1"/>
    <parameter parameter="13582920" name="dust_size_bin_2"/>
    <parameter parameter="13582921" name="dust_size_bin_3"/>
    <parameter parameter="13582922" name="dust_size_bin_4"/>
    <parameter parameter="13582923" name="dust_size_bin_5"/>
    <parameter parameter="13582924" name="visibility_dust"/>
    <inherits>WRFARW</inherits>
  </scheme>
  <scheme id="ibl.sg.test">
    <parameter name="temperature" parameter="1000000" level="2m"/>
    <parameter name="max_temperature" parameter="1000004" level="2m"/>
    <parameter name="min_temperature" parameter="1000005" level="2m"/>
    <parameter name="dewpoint_temperature" parameter="1000006" level="2m"/>
    <parameter name="surface_temperature" parameter="1000000" level="gnd-surf"/>
    <parameter name="feels_like_temperature" parameter="1064063" level="2m"/>
    <parameter name="wind_speed" parameter="1000513" level="10m"/>
    <parameter name="wind_direction" parameter="1000512" level="10m"/>
    <parameter name="wind_gust" parameter="1064016" level="10m"/>
    <parameter name="visibility" parameter="1004864" level="gnd-surf"/>
    <parameter name="relative_humidity" parameter="1000257" level="gnd-surf"/>
    <parameter name="pressure_at_mean_sea_level" parameter="1000769" level="mean-sea"/>
    <parameter name="uv_index" parameter="1001075" level="atmosphere"/>
    <parameter name="precipitation_rate" parameter="1000263" level="gnd-surf"/>
    <parameter name="precipitation" parameter="1000264" level="gnd-surf"/>
    <parameter name="precipitation_probability" parameter="631228" level="gnd-surf"/>
    <parameter name="sunshine_duration" parameter="1001569" level="gnd-surf"/>
    <parameter name="snow_depth" parameter="1000267" level="gnd-surf"/>
    <parameter name="low_cloud_amount" parameter="1001539" level="atmosphere"/>
    <parameter name="medium_cloud_amount" parameter="1001540" level="atmosphere"/>
    <parameter name="high_cloud_amount" parameter="1001541" level="atmosphere"/>
    <parameter name="cloud_coverage" parameter="1001537" level="atmosphere"/>
    <parameter name="wet_bulb_freezing_level" parameter="1064008" level="atmosphere"/>
    <!-- parameter name="snow_amount" parameter="" level=""/ -->
    <!-- parameter name="cloud_base_height_3okta" parameter="" level=""/ -->
    <!-- parameter name="cloud_base_height_5okta" parameter="" level=""/ -->
    <!-- parameter name="cloud_base_height_7okta" parameter="" level=""/ -->
    <!-- parameter name="cloud_amount_200ft" parameter="" level=""/ -->
    <!-- parameter name="instantaneous_direct_swdown_rad" parameter="" level=""/ -->
    <!-- parameter name="integrated_direct_swdown_rad" parameter="" level=""/ -->
    <!-- parameter name="instantaneous_diffuse_swdown_rad" parameter="" level=""/ -->
    <!-- parameter name="integrated_diffuse_swdown_rad" parameter="" level=""/ -->
    <!-- parameter name="dry_bulb_freezing_level" parameter="" level=""/ -->
  </scheme>
</XIBL-STORAGE>
