<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  01.06.2007
  Authors: <AUTHORS>
            <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (C) 2007-2020, IBL Software Engineering spol. s r. o., <<EMAIL>>.
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:resolvemodel">
<XIBL-STORAGE version="1.1">
  <!--==== Model resolution ====-->
  <!-- IBL Satellite Weather default models (new) -->
  <!-- Should be updated whenever "doc:global/settings/sw/ecd/satservices/Default" changes! -->
  <entry model-id="GOW-Disc" centre="77" sub-centre="61" process="20" pattern="YSA/// GOWX //////"/>
  <entry model-id="MET7Disc" centre="77" sub-centre="7" process="30" pattern="YSA/// METX //////"/>
  <entry model-id="HIM8Disc" centre="77" sub-centre="102" process="51" pattern="YSA/// HIMX //////"/>
  <entry model-id="MSGIDisc" centre="77" sub-centre="6" process="48" pattern="YSA/// MIOX //////"/>
  <entry model-id="MSGREur" centre="77" sub-centre="8" process="0" pattern="YSA/// MSRE //////"/>
  <entry model-id="MSGR" centre="77" sub-centre="8" process="45" pattern="YSA/// MSRX //////"/>
  <entry model-id="GOE-Disc" centre="77" sub-centre="62" process="10" pattern="YSA/// GOEX //////"/>
  <entry model-id="MSG-Disc" centre="77" sub-centre="9" process="40" pattern="YSA/// MSGX //////"/>
  <entry model-id="MSG-HRVN" centre="77" sub-centre="9" process="42" pattern="YSA/// MSGN //////"/>
  <entry model-id="MSG-Natv" centre="77" sub-centre="9" process="41" pattern="YSA/// MSGN //////"/>
  <!-- Other satellite GRIBs produced by IBL tools (not necessarily Satellite Weather, but format converters -->
  <entry model-id="FY2E" centre="77" sub-centre="122" process="0" pattern="YSAT// //// //////"/>
  <entry model-id="ATOVS" centre="77" sub-centre="132" process="0" pattern="YSAT// //// //////"/>
  <entry model-id="MPEFGII" centre="77" sub-centre="135" process="0" pattern="YSAT// MPEF //////"/>
  <entry model-id="MPEFRII" centre="77" sub-centre="136" process="0" pattern="YSAT// MPEF //////"/>
  <entry model-id="MPEIGII" centre="77" sub-centre="135" process="0" pattern="YSAI// MPEF //////"/>
  <!-- MPEF GRIB data coming from EumetCast, processed by ECD from XRIT to GRIB -->
  <entry model-id="MPEFCLAI" pattern="YCLI01 MPEF //////"/>
  <entry model-id="MPEFCLM" pattern="YCLM01 MPEF //////"/>
  <entry model-id="MPEFCRM" pattern="YCRM01 MPEF //////"/>
  <entry model-id="MPEFCTH" pattern="YCTH01 MPEF //////"/>
  <entry model-id="MPEFDIV" pattern="YDIV01 MPEF //////"/>
  <entry model-id="MPEFFIRG" pattern="YFIR01 MPEF //////"/>
  <entry model-id="MPEFMPEG" pattern="YMPE01 MPEF //////"/>
  <entry model-id="MPEFOCAE" pattern="YOCA01 MPEF //////"/>
  <entry model-id="MPEFRFIR" pattern="YFIR02 MPEF //////"/>
  <entry model-id="MPEFRMPE" pattern="YMPE02 MPEF //////"/>
  <!-- MPEF IODC GRIB data coming from EumetCast, processed by ECD -->
  <entry model-id="MPEICLAI" pattern="YCLI04 MPEF //////"/>
  <entry model-id="MPEICLM" pattern="YCLM04 MPEF //////"/>
  <entry model-id="MPEICRM" pattern="YCRM04 MPEF //////"/>
  <entry model-id="MPEICTH" pattern="YCTH04 MPEF //////"/>
  <entry model-id="MPEIDIV" pattern="YDIV04 MPEF //////"/>
  <entry model-id="MPEIFIRG" pattern="YFIR04 MPEF //////"/>
  <entry model-id="MPEIMPEG" pattern="YMPE04 MPEF //////"/>
  <!-- Ocean and Sea Ice SAF GRIBs -->
  <entry model-id="OSI-201" centre="211" sub-centre="0" process="220" pattern="YOST01 LFPW //////"/>
  <entry model-id="OSI-203" centre="88" sub-centre="0" process="1" pattern="YOST03 ENMI //////"/>
  <entry model-id="OSI-206" centre="211" sub-centre="0" process="220" pattern="YOST06 LFPW //////"/>
  <entry model-id="OSI-207" centre="211" sub-centre="0" process="220" pattern="YOST07 LFPW //////"/>
  <entry model-id="OSI-303" centre="211" sub-centre="0" process="220" pattern="YORF03 LFPW //////"/>
  <entry model-id="OSI-304" centre="211" sub-centre="0" process="220" pattern="YORF04 LFPW //////"/>
  <entry model-id="OSI-305" centre="211" sub-centre="0" process="220" pattern="YORF05 LFPW //////"/>
  <entry model-id="OSI-306" centre="211" sub-centre="0" process="220" pattern="YORF06 LFPW //////"/>
  <entry model-id="OSI-401a" centre="88" sub-centre="0" process="1" pattern="YOSI01 ENMI //////"/>
  <!-- Global Hydro Estimator https://satepsanone.nesdis.noaa.gov/pub/HydroEst/GHE/ converted to GRIB -->
  <entry model-id="HydEstRR" centre="77" sub-centre="141" process="99" pattern="Y///99 HYDE //////"/>
  <entry model-id="HydEst1h" centre="77" sub-centre="141" process="98" pattern="Y///98 HYDE //////"/>
  <!-- IBL Satellite Weather - Special entry for regional/relocatable area that will be updated
       when we are doing presentation for some country -->
  <entry model-id="Sat-Reg" centre="77" process="240" pattern="YSAT// ///R //////"/>
  <!-- SADIS/ISCS WAFC GRIB1 models -->
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/I/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/I/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/J/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/J/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/K/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/K/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/L/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/L/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/M/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/M/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/N/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/N/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/O/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/O/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="15" pattern="H/P/// EGRR //////"/>
  <entry model-id="GWAFEGRR" centre="74" sub-centre="0" process="45" pattern="H/P/// EGRR //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/I/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/I/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/J/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/J/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/K/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/K/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/L/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/L/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/M/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/M/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/N/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/N/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/O/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/O/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="81" pattern="H/P/// KWBC //////"/>
  <entry model-id="GWAFKWBC" centre="7" sub-centre="0" process="96" pattern="H/P/// KWBC //////"/>
  <!-- SADIS/ISCS WAFC GRIB2 models -->
  <entry model-id="EGRR" centre="74" sub-centre="5" process="1" pattern="Y///// EGRR //////"/>
  <entry model-id="KWBC" centre="7" sub-centre="0" process="96" pattern="Y///// KWBC //////"/>
  <entry model-id="KWBC" centre="7" sub-centre="4" process="96" pattern="Y///// KWBC //////"/>
  <!-- IBL GFS models - KGFX (GRIB2) and KGFS (GRIB1) -->
  <entry model-id="GEFS" centre="7" sub-centre="2" process="107">
    <ensemble-info ensemble-type="ENSEMBLE_CUSTOM_MEMBERS" members-count="30"/>
  </entry>
  <entry model-id="KGFX" pattern="////// KGFX //////"/>
  <entry model-id="KGFH" pattern="////// KGFH //////"/>
  <entry model-id="KGFS" pattern="////// KGFS //////"/>
  <entry model-id="KGEN" pattern="////// KGEN //////"/>
  <entry model-id="KWAF" pattern="////// KWAF //////"/>
  <entry model-id="KWAX" pattern="////// KWAX //////"/>
  <!-- IBL Environment Canada models -->
  <entry model-id="GlobGEM" pattern="Y///// CYGM //////"/>
  <entry model-id="GEPS" centre="57" sub-centre="4" process="99"/>
  <!-- IBL DWDSAT models-->
  <entry model-id="GlobDWD" pattern="////// GMEX //////"/>
  <entry model-id="LocDWD" pattern="////// LMEX //////"/>
  <!-- IBL WRF models -->
  <entry model-id="WRF-Rel1" pattern="////// WRFX //////"/>
  <entry model-id="WRF-Rel2" pattern="////// WRFY //////"/>
  <entry model-id="WRF-Rel3" pattern="////// WRFZ //////"/>
  <entry model-id="WRF-SA1" pattern="////// WRSU //////"/>
  <entry model-id="WRF-SA2" pattern="////// WRSV //////"/>
  <entry model-id="WRF-CE" pattern="////// WRFE //////"/>
  <entry model-id="WRF-SK" pattern="////// WRFS //////"/>
  <entry model-id="WRF-BA" pattern="////// WRFB //////"/>
  <!-- IBL COSMO models -->
  <entry model-id="COSMO-CE" pattern="////// CMCE //////"/>
  <entry model-id="COSMO-SK" pattern="////// CMSK //////"/>
  <!-- IBL WW3 models -->
  <entry model-id="WW3-MSM" pattern="H///// WWMM //////"/>
  <entry model-id="WW3-MSP" pattern="H///// WWMP //////"/>
  <!-- Met Eireann ECMWF models -->
  <entry model-id="ECMG" pattern="////// ECMG //////"/>
  <entry model-id="ECMH" pattern="////// ECMH //////"/>
  <entry model-id="ECMF" pattern="////// ECMF //////"/>
  <!-- UK Met Office models -->
  <entry model-id="GlobCWAO" centre="54" sub-centre="0" pattern="H///// CWAO //////"/>
  <entry model-id="GlobLFPW" centre="85" sub-centre="0" process="211" pattern="H/N/// LFPW //////"/>
  <entry model-id="GlobLFPW" centre="85" sub-centre="0" process="211" pattern="H/S/// LFPW //////"/>
  <entry model-id="GEurLFPW" centre="85" sub-centre="0" process="211" pattern="H/U/// LFPW //////"/>
  <entry model-id="GAfrLFPW" centre="85" sub-centre="0" process="211" pattern="H/H/// LFPW //////"/>
  <entry model-id="GlobEDZW" centre="78" sub-centre="255" process="188" pattern="H///// EDZW //////"/>
  <entry model-id="GlblKWBC" pattern="HXXX87 NCEP //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/A/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/A/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/B/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/B/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/C/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/C/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/D/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/D/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/E/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/E/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="81" pattern="H/F/// KWBC //////"/>
  <entry model-id="GExGKWBC" centre="7" sub-centre="0" process="96" pattern="H/F/// KWBC //////"/>
  <entry model-id="GlFFECMF" centre="74" sub-centre="0" process="15" pattern="HXXX83 EGRR //////"/>
  <entry model-id="GlGRECMF" centre="98" sub-centre="0" pattern="HXXX83 EGRR //////"/>
  <entry model-id="GlobECMF" centre="98" sub-centre="0" process="34" pattern="HZQ/// EGRR //////"/>
  <entry model-id="GlW1ECMF" centre="98" sub-centre="0" pattern="H///// ECMF //////"/>
  <entry model-id="GlW2ECMF" centre="98" sub-centre="0" pattern="H///// ECMG //////"/>
  <entry model-id="GlW2ECMF" centre="98" sub-centre="0" pattern="H///// ECMW //////"/>
  <entry model-id="E1ECMF" pattern="H///// ECED //////"/>
  <entry model-id="E1ECMF" pattern="H///// ECEM //////"/>
  <entry model-id="E1ECMF" pattern="H///// ECEP //////"/>
  <entry model-id="EPS2ECMF" centre="98" sub-centre="0" process="150"/>
  <entry model-id="HRESECMF" centre="98" sub-centre="0" process="153"/>
  <entry model-id="GlobRJTD" centre="34" sub-centre="0" process="4" pattern="H///// RJTD //////"/>
  <entry model-id="GlobETAX" centre="7" sub-centre="0" process="96" pattern="H///// ETAX //////"/>
  <entry model-id="WvGlEGRR" centre="74" sub-centre="0" process="89" pattern="HXXX84 EGRR //////"/>
  <entry model-id="WvGlKWBC" pattern="HXXX84 KWBC //////"/>
  <entry model-id="WvNAEGRR" centre="74" sub-centre="0" process="77" pattern="YXXX64 EGRR //////"/>
  <entry model-id="WvXNEGRR" centre="74" sub-centre="0" process="79" pattern="YXXX64 EGRR //////"/>
  <entry model-id="WvPGEGRR" centre="74" sub-centre="0" process="78" pattern="YXXX64 EGRR //////"/>
  <entry model-id="WvglEGRR" centre="74" sub-centre="0" process="19" pattern="HWQX01 EGRR //////"/>
  <entry model-id="WvglEGRR" centre="74" sub-centre="0" process="89" pattern="HWQX01 EGRR //////"/>
  <entry model-id="WvXUEGRR" centre="74" sub-centre="0" process="60" pattern="YJQ/// EGRR //////"/>
  <entry model-id="WvUKEGRR" centre="74" sub-centre="0" process="60" pattern="HWQX01 EGRR //////"/>
  <entry model-id="WvEuEGRR" centre="74" sub-centre="0" process="88" pattern="YJA/// EGRR //////"/>
  <entry model-id="WvExEGRR" centre="74" sub-centre="0" process="89" pattern="H///88 EGRR //////"/>
  <entry model-id="WvExEGRR" centre="74" sub-centre="0" process="19" pattern="H///88 EGRR //////"/>
  <entry model-id="SCS3EGRR" centre="74" sub-centre="0" process="56" pattern="YXXX68 EGRR //////"/>
  <entry model-id="SBCMEGRR" centre="74" sub-centre="0" process="57" pattern="YXXX68 EGRR //////"/>
  <entry model-id="SSRMEGRR" centre="74" sub-centre="0" process="58" pattern="YXXX68 EGRR //////"/>
  <entry model-id="SENSEGRR" centre="74" sub-centre="0" process="1" pattern="YXXX68 EGRR //////"/>
  <entry model-id="ReFKEGRR" centre="74" sub-centre="0" process="7" pattern="YXXX69 EGRR //////"/>
  <entry model-id="ReFLEGRR" centre="74" sub-centre="0" process="7" pattern="YMD/// EGRR //////"/>
  <entry model-id="ReAFEGRR" centre="74" sub-centre="0" process="49" pattern="YXXX69 EGRR //////"/>
  <entry model-id="ReAGEGRR" centre="74" sub-centre="0" process="49" pattern="H/E/// EGRR //////"/>
  <entry model-id="ReSAEGRR" centre="74" sub-centre="0" process="59" pattern="YXXX69 EGRR //////"/>
  <entry model-id="ReSBEGRR" centre="74" sub-centre="0" process="59" pattern="YMC/// EGRR //////"/>
  <entry model-id="ReHAEGRR" centre="74" sub-centre="0" process="50" pattern="YXXX69 EGRR //////"/>
  <entry model-id="NABOEGRR" centre="74" sub-centre="0" process="43" pattern="YXXX77 EGRR //////"/>
  <entry model-id="NAUKEGRR" centre="74" sub-centre="0" process="43" pattern="YXXX78 EGRR //////"/>
  <entry model-id="NAEEGRR" centre="74" sub-centre="0" process="43" pattern="YXXX79 EGRR //////"/>
  <entry model-id="NAPREGRR" centre="74" sub-centre="0" process="14" pattern="YXXX79 EGRR //////"/>
  <entry model-id="NAE1EGRR" centre="74" sub-centre="0" process="43" pattern="YMX/// EGRR //////"/>
  <entry model-id="NAE1EGRR" centre="74" sub-centre="0" process="43" pattern="YZZ/00 EGRR //////"/>
  <entry model-id="NAE1EGRR" centre="74" sub-centre="0" process="43" pattern="YXXX90 EGRR //////"/>
  <entry model-id="MfUKEGRR" centre="75" sub-centre="28" process="115" pattern="YMA/00 EGRR //////"/>
  <entry model-id="MfNAEGRR" centre="75" sub-centre="0" process="116" pattern="HMA/10 EGRR //////"/>
  <entry model-id="MfNAEGRR" centre="74" sub-centre="0" process="116" pattern="HMA/10 EGRR //////"/>
  <entry model-id="MFUKEGRR" centre="74" sub-centre="0" process="115" pattern="YXXX70 EGRR //////"/>
  <entry model-id="MFNAEGRR" centre="74" sub-centre="0" process="116" pattern="YXXX70 EGRR //////"/>
  <entry model-id="GAvnEGRR" centre="74" sub-centre="0" process="15" pattern="HXXX90 EGRR //////"/>
  <entry model-id="GAvnEGRR" centre="74" sub-centre="0" process="45" pattern="HXXX90 EGRR //////"/>
  <entry model-id="GtstEGRR" centre="74" sub-centre="0" process="15" pattern="HXXX88 EGRR //////"/>
  <entry model-id="GlobEGRR" centre="74" sub-centre="0" process="15" pattern="HXXX87 EGRR //////"/>
  <entry model-id="GHebEGRR" centre="74" sub-centre="0" process="15" pattern="HXXX86 EGRR //////"/>
  <entry model-id="GLGUEGRR" centre="74" sub-centre="0" process="15" pattern="HXXX85 EGRR //////"/>
  <entry model-id="GloSEGRR" centre="74" sub-centre="0" process="100" pattern="HMQX// EGRR //////"/>
  <entry model-id="GloSEGRR" centre="74" sub-centre="0" process="100" pattern="HMQY// EGRR //////"/>
  <entry model-id="GloAEGRR" centre="74" sub-centre="0" pattern="HWQX00 EGRR //////"/>
  <entry model-id="GloLEGRR" centre="74" sub-centre="0" process="45" pattern="HMQ/// EGRR //////"/>
  <entry model-id="GloLEGRR" centre="74" sub-centre="0" process="45" pattern="HMC/// EGRR //////"/>
  <entry model-id="GFliEGRR" centre="74" sub-centre="0" process="45" pattern="H/A/// EGRR //////"/>
  <entry model-id="GFliEGRR" centre="74" sub-centre="0" process="45" pattern="YMD/// EGRR //////"/>
  <entry model-id="GAscEGRR" centre="74" sub-centre="0" process="45" pattern="H/B/// EGRR //////"/>
  <entry model-id="GAkrEGRR" centre="74" sub-centre="0" process="45" pattern="H/C/// EGRR //////"/>
  <entry model-id="GGibEGRR" centre="74" sub-centre="0" process="14" pattern="H/D/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/S/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/S/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/T/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/T/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/U/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/U/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/V/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/V/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/W/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/W/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="15" pattern="H/X/// EGRR //////"/>
  <entry model-id="GExGEGRR" centre="74" sub-centre="0" process="45" pattern="H/X/// EGRR //////"/>
  <entry model-id="GExREGRR" centre="74" sub-centre="0" process="14" pattern="Y/R/// EGRR //////"/>
  <entry model-id="GExREGRR" centre="74" sub-centre="0" process="44" pattern="Y/R/// EGRR //////"/>
  <entry model-id="GExREGRR" centre="74" sub-centre="0" process="45" pattern="Y/R/// EGRR //////"/>
  <entry model-id="GExAEGRR" centre="74" sub-centre="0" process="49" pattern="H/H/// EGRR //////"/>
  <entry model-id="ssMREGRR" centre="74" sub-centre="0" process="65" pattern="YXXX88 EGRR //////"/>
  <entry model-id="ssGUEGRR" centre="74" sub-centre="0" process="66" pattern="YXXX88 EGRR //////"/>
  <entry model-id="ssIREGRR" centre="74" sub-centre="0" process="67" pattern="YXXX88 EGRR //////"/>
  <entry model-id="ssSHEGRR" centre="74" sub-centre="0" process="68" pattern="YXXX88 EGRR //////"/>
  <entry model-id="ssRLEGRR" centre="74" sub-centre="0" process="69" pattern="YXXX88 EGRR //////"/>
  <entry model-id="FGloEGRR" centre="74" sub-centre="0" process="85" pattern="HXXX89 EGRR //////"/>
  <entry model-id="FNAtEGRR" centre="74" sub-centre="0" process="103" pattern="YXXX89 EGRR //////"/>
  <entry model-id="FMedEGRR" centre="74" sub-centre="0" process="104" pattern="YXXX89 EGRR //////"/>
  <entry model-id="FIndEGRR" centre="74" sub-centre="0" process="105" pattern="YXXX89 EGRR //////"/>
  <entry model-id="FExeEGRR" centre="74" sub-centre="0" process="106" pattern="YXXX89 EGRR //////"/>
  <entry model-id="3DWiEGRR" centre="74" sub-centre="0" process="118" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DSpEGRR" centre="74" sub-centre="0" process="119" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DDaEGRR" centre="74" sub-centre="0" process="120" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DGrEGRR" centre="74" sub-centre="0" process="121" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DPeEGRR" centre="74" sub-centre="0" process="122" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DSnEGRR" centre="74" sub-centre="0" process="124" pattern="HW//00 EGRR //////"/>
  <entry model-id="3DFaEGRR" centre="74" sub-centre="0" process="126" pattern="HW//00 EGRR //////"/>
  <entry model-id="UKPP4KM" centre="74" sub-centre="0" process="1" pattern="////// EGRR //////"/>
  <entry model-id="UKPPDAv" centre="74" sub-centre="0" process="3" pattern="YXXX98 EGRR //////"/>
  <entry model-id="UKPPRAv" centre="74" sub-centre="0" process="3" pattern="YXXX97 EGRR //////"/>
  <entry model-id="UKPPNOW" centre="74" sub-centre="0" process="3" pattern="////// EGRR //////"/>
  <entry model-id="PPRain" centre="74" sub-centre="0" process="1" pattern="YXXX96 EGRR //////"/>
  <entry model-id="EurPPNOW" centre="74" sub-centre="0" process="5" pattern="////// EGRR //////"/>
  <entry model-id="EurPPNAE" centre="74" sub-centre="0" process="6" pattern="////// EGRR //////"/>
  <entry model-id="GlobalPP" centre="74" sub-centre="0" process="99" pattern="HXXX99 EGRR //////"/>
  <entry model-id="CSWAEGRR" centre="58" sub-centre="0" process="24" pattern="YXXX59 EGRR //////"/>
  <entry model-id="CWATEGRR" centre="58" sub-centre="0" process="63" pattern="YXXX59 EGRR //////"/>
  <entry model-id="CCAMEGRR" centre="58" sub-centre="0" process="27" pattern="YXXX59 EGRR //////"/>
  <entry model-id="EGloEGRR" centre="74" sub-centre="0" process="9" pattern="////// EGRR //////"/>
  <entry model-id="ENAEEGRR" centre="74" sub-centre="0" process="10" pattern="////// EGRR //////"/>
  <entry model-id="OSAA" centre="74" sub-centre="1" process="1" pattern="Y///// OSAX //////"/>
  <entry model-id="OSAB" centre="74" sub-centre="1" process="2" pattern="Y///// OSAX //////"/>
  <entry model-id="OSAC" centre="74" sub-centre="1" process="3" pattern="Y///// OSAX //////"/>
  <entry model-id="OSAD" centre="74" sub-centre="1" process="4" pattern="Y///// OSAX //////"/>
  <entry model-id="OSAE" centre="74" sub-centre="1" process="5" pattern="Y///// OSAX //////"/>
  <entry model-id="MSGVIS" centre="254" sub-centre="0" pattern="EVDA30 EGRR //////"/>
  <entry model-id="MSGVIS" centre="74" sub-centre="0" pattern="EVDA30 EGRR //////"/>
  <entry model-id="MSGHRVN" centre="254" sub-centre="0" pattern="EVDN70 EGRR //////"/>
  <entry model-id="MSGHRVN" centre="74" sub-centre="0" pattern="EVDN70 EGRR //////"/>
  <entry model-id="MSGWV" centre="254" sub-centre="0" pattern="EWDA30 EGRR //////"/>
  <entry model-id="MSGWV" centre="74" sub-centre="0" pattern="EWDA30 EGRR //////"/>
  <entry model-id="MSGHRVS" centre="254" sub-centre="0" pattern="EVDS70 EGRR //////"/>
  <entry model-id="MSGHRVS" centre="74" sub-centre="0" pattern="EVDS70 EGRR //////"/>
  <entry model-id="MSGIR" centre="254" sub-centre="0" pattern="EIDA50 EGRR //////"/>
  <entry model-id="MSGIR" centre="74" sub-centre="0" pattern="EIDA50 EGRR //////"/>
  <entry model-id="NameEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV90 EGRR //////"/>
  <entry model-id="NamBEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV91 EGRR //////"/>
  <entry model-id="NamWEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV92 EGRR //////"/>
  <entry model-id="NamKEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV93 EGRR //////"/>
  <entry model-id="TCStEGRR" centre="98" sub-centre="0" process="128" pattern="YPPV10 EGRR //////"/>
  <entry model-id="TCNSEGRR" centre="98" sub-centre="0" process="128" pattern="YPPV11 EGRR //////"/>
  <entry model-id="TCThEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV12 EGRR //////"/>
  <entry model-id="BlCIEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV60 //// //////"/>
  <entry model-id="BlEuEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV61 //// //////"/>
  <entry model-id="BlNIEGRR" centre="74" sub-centre="0" process="128" pattern="YPPV62 //// //////"/>
  <entry model-id="Bl01EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV63 //// //////"/>
  <entry model-id="Bl02EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV64 //// //////"/>
  <entry model-id="Bl03EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV65 //// //////"/>
  <entry model-id="Bl04EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV66 //// //////"/>
  <entry model-id="Bl05EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV67 //// //////"/>
  <entry model-id="Bl06EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV68 //// //////"/>
  <entry model-id="Bl07EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV69 //// //////"/>
  <entry model-id="Bl08EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV70 //// //////"/>
  <entry model-id="Bl09EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV71 //// //////"/>
  <entry model-id="Bl10EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV72 //// //////"/>
  <entry model-id="Bl11EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV73 //// //////"/>
  <entry model-id="Bl12EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV74 //// //////"/>
  <entry model-id="Bl13EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV75 //// //////"/>
  <entry model-id="Bl14EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV76 //// //////"/>
  <entry model-id="Bl15EGRR" centre="74" sub-centre="0" process="128" pattern="YPPV77 //// //////"/>
  <entry model-id="MwUKBBC" centre="74" sub-centre="0" process="115" pattern="YXXX49 EGRR //////"/>
  <entry model-id="MwUKWMO" centre="75" sub-centre="28" process="115" pattern="YXXX48 EGRR //////"/>
  <entry model-id="MwASXWMO" centre="75" sub-centre="0" process="116" pattern="YXXX47 EGRR //////"/>
  <entry model-id="MwFSXBBC" centre="74" sub-centre="0" process="116" pattern="YXXX46 EGRR //////"/>
  <entry model-id="MwFSXWMO" centre="74" sub-centre="0" process="116" pattern="YXXX45 EGRR //////"/>
  <entry model-id="Mw72BBC" centre="74" sub-centre="0" process="116" pattern="YXXX44 EGRR //////"/>
  <entry model-id="Mw72WMO" centre="74" sub-centre="0" process="116" pattern="YXXX43 EGRR //////"/>
  <entry model-id="Mw144BBC" centre="74" sub-centre="0" process="116" pattern="YXXX42 EGRR //////"/>
  <entry model-id="Mw144WMO" centre="74" sub-centre="0" process="116" pattern="YXXX41 EGRR //////"/>
  <entry model-id="MwAdhocA" centre="74" sub-centre="2" process="3" pattern="////// EGRR //////"/>
  <entry model-id="MwAdhocB" centre="74" sub-centre="2" process="4" pattern="////// EGRR //////"/>
  <entry model-id="MwAdhocC" centre="74" sub-centre="2" process="5" pattern="////// EGRR //////"/>
  <entry model-id="MwAdhocD" centre="74" sub-centre="2" process="6" pattern="////// EGRR //////"/>
  <entry model-id="MwAdhocE" centre="74" sub-centre="2" process="7" pattern="////// EGRR //////"/>
  <entry model-id="SRNWP001" pattern="HXXX91 TEST //////"/>
  <entry model-id="OSTIASST" centre="74" sub-centre="0" process="20" pattern="HXXX00 EGRR //////"/>
  <entry model-id="PWVEI" centre="254" sub-centre="0" pattern="EIDA54 EGRR //////"/>
  <entry model-id="PWVEWA" centre="254" sub-centre="0" pattern="EWDA34 EGRR //////"/>
  <entry model-id="PWVEWB" centre="254" sub-centre="0" pattern="EWDA14 EGRR //////"/>
  <!-- ICON models from DWD Offenbach -->
  <entry model-id="GLOBICON" centre="78" sub-centre="255" process="1" pattern="HPDA98 ETGX //////"/>
  <entry model-id="EUROICON" centre="78" sub-centre="255" process="2" pattern="HPDC98 ETGX //////"/>
  <entry model-id="GERMICON" centre="78" sub-centre="255" process="3" pattern="HPDD98 ETGX //////"/>
  <!-- EDZI ICON models -->
  <entry model-id="GLOBICON" centre="78" sub-centre="255" process="1" pattern="Y///// EDZI //////"/>
  <entry model-id="EUROICON" centre="78" sub-centre="255" process="2" pattern="Y///// EDZI //////"/>
  <entry model-id="GERMICON" centre="78" sub-centre="255" process="3" pattern="Y///// EDZI //////"/>
  <!-- Himawari au.bom benchmarks -->
  <entry model-id="HIMA" pattern="YCDF99 HIMA //////"/>
  <!-- *HARM models-->
  <entry model-id="HM25HARM" centre="233" process="40" pattern="Y///// HARM //////"/>
  <entry model-id="HybdHARM" centre="233" process="40" pattern="YETA01 HARB //////"/>
  <entry model-id="IEHARM" pattern="YCDF02 HARM //////"/>
  <entry model-id="HARM" pattern="YZZZ// HARM //////"/>
  <!-- -->
  <entry model-id="VorECMW" centre="98" sub-centre="0" process="147" pattern="H///// ECMW //////"/>
  <entry model-id="VorTECMW" centre="98" sub-centre="0" process="147" pattern="Y///// ECMW //////"/>
  <entry model-id="NorECMF" centre="98" sub-centre="0" process="147" pattern="H///// ECMF //////"/>
  <entry model-id="StDECMW" centre="98" sub-centre="0" pattern="Y///// ECED //////"/>
  <entry model-id="GlobECMF" centre="98" sub-centre="0" process="147" pattern="Y///// ECMF //////"/>
  <entry model-id="WaveECMF" centre="98" sub-centre="0" process="112" pattern="Y///// ECMF //////"/>

  <!--==== Parameter translation ====-->
  <translate version="2.0" pattern="*ECM*">
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/ECMWF Levels Compatibility"/>
  </translate>
  <translate version="2.0" pattern="/^(3D(Da|Gr|Pe|Sn|Sp|Wi)|(GloL|NAE1))EGRR$/">
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/UK MetOffice Wind Levels Compatibility"/>
  </translate>
  <translate version="2.0" pattern="/^(GloL|NAE1)EGRR$/">
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/UK MetOffice Temperature Levels Compatibility"/>
  </translate>
  <translate version="2.0" pattern="WRF*">
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/WRF Cloud Levels Compatibility"/>
  </translate>
  <translate version="2.0" pattern="*HARM">
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/Harmonie Graupel Rate"/>
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/Harmonie Rainfall Rate"/>
    <translation-rules-sets unistorage-path="doc:global/settings/catalogues/model/translations/Harmonie Snowfall Rate"/>
  </translate>
  <auto-index-unresolved>EXTENDED</auto-index-unresolved>
</XIBL-STORAGE>
