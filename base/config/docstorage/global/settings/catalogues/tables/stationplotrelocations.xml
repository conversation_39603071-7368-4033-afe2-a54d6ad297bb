<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  22.10.2012
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:tableconfiguration">
<XIBL-STORAGE version="1.1">
  <specification>
    <!-- Id of relocation set -->
    <field id="relocation-set-id" type="string"/>
    <!-- Place-id -->
    <field id="place-id" type="string"/>
    <!-- Kernel class restriction -->
    <field id="kernel-class-restriction" type="string"/>
    <!-- New latitude -->
    <field id="latitude" type="number"/>
    <!-- New longitude -->
    <field id="longitude" type="number"/>
  </specification>

  <!-- Define the table indices -->
  <index id="index">
    <key>
      <item field="relocation-set-id"/>
      <item field="place-id"/>
      <item field="kernel-class-restriction"/>
    </key>
  </index>

  <!-- Define the table loaders -->
  <load>
    <primary-key>
      <item field="relocation-set-id"/>
      <item field="place-id"/>
      <item field="kernel-class-restriction"/>
    </primary-key>
    <reader class="idb.CSVTableIO" path="config/stationplotrelocations.csv"/>
  </load>
</XIBL-STORAGE>
