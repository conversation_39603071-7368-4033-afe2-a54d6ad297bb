<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE XIBL-STORAGE SYSTEM "urn:ibl:storage:persistent:entry">
<XIBL-STORAGE version="1.1">
  <entry class="layers.bitmap.GridProcessorProductCatalogEntry" interpolation-enabled="true" scan-back="900" scan-forward="900" range-override-enabled="false" range-override-all="false">
    <data-provider class="dbase.image.GridProcessorProvider" model="==G18F" parameter="1001029" level="8.44um" dataset="spec:regular"/>
    <data-parser class="igeo.image.GridProcessorDataParser" model="==G18F" parameter="1001029" level="8.44um" dataset="spec:regular" reference="-273.14999999999998">
      <processing>P50,-1,0,0,0,3000000</processing>
    </data-parser>
    <top-view-generator class="igeo.image.GridPrecomputedTopViewGenerator"/>
    <spectra unistorage-path="doc:global/settings/catalogues/images/spectras/Satellite/IR"/>
  </entry>
</XIBL-STORAGE>
