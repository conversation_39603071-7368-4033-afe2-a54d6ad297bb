<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  14.05.2012
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<XIBL-STORAGE version="1.1">
  <access-right name="system.acl-rights.modify" description="User is allowed to modify user database and access rights rights."/>
  <access-right name="system.acl-rights.browse" description="User is allowed to browse user database and access rights rights."/>
  <access-right name="system.doc.global.modify" description="User is allowed to modify global DocStorage documents."/>
  <access-right name="system.doc.groups.modify" description="User is allowed to modify DocStorage documents in any group directory."/>
  <access-right name="system.services.manage" description="User is allowed to manage (start/stop/restart) services."/>
  <access-right name="system.services.restart" description="User is allowed to start/stop/restart the system."/>
  <access-right name="modifications.reports" description="User is allowed to modify reports and re-send changes to GTS."/>
  <access-right name="edit.stationcatalog" description="User is allowed to modify Place Catalogue entries."/>
  <access-right name="edit.keyboard_shortcuts" description="User is allowed to modify keyboard shortcuts."/>
  <access-right name="edit.messages.deletemessageandreport" description="User is allowed to delete messages and mark reports as invalid."/>
  <access-right name="applications.system.databasemanager" description="User is allowed to run database manager."/>
  <access-right name="applications.system.ipdslogviewer" description="User is allowed to run production log viewer."/>
  <access-right name="applications.system.logviewer" description="User is allowed to run log viewer."/>
  <access-right name="applications.system.schedulerlogviewer" description="User is allowed to run scheduler log viewer."/>
  <access-right name="applications.system.scsbrowser" description="User is allowed to use scsbrowser."/>
  <access-right name="applications.system.systemconfig" description="User is allowed to run system configuration."/>
  <access-right name="applications.system.processmanager" description="User is allowed to run process manager."/>
  <access-right name="applications.meteo.addressbookeditor" description="User is allowed to run Address Book Editor application."/>
  <access-right name="applications.meteo.alerteditor" description="User is allowed to run the Alert Editor application."/>
  <access-right name="applications.meteo.batchosa" description="User is allowed to run Batch OSA application."/>
  <access-right name="applications.meteo.equationeditor" description="User is allowed to run the Equation Editor application."/>
  <access-right name="applications.meteo.fieldcutter" description="User is allowed to run Field Cutter application."/>
  <access-right name="applications.meteo.fielddiagnostics" description="User is allowed to run Field Diagnostics application."/>
  <access-right name="applications.meteo.filesorter" description="User is allowed to run File Sorter configuration."/>
  <access-right name="applications.meteo.filteredit" description="User is allowed to create, edit, and delete filters in Message Viewer application."/>
  <access-right name="applications.meteo.flightbriefing" description="User is allowed to run the flightbriefing application." license="license.aw"/>
  <access-right name="applications.meteo.flightbriefing.flightdatabase.new" description="User is allowed to create a new flight." license="license.aw"/>
  <access-right name="applications.meteo.flightbriefing.flightdatabase.save" description="User is allowed to save a flight." license="license.aw"/>
  <access-right name="applications.meteo.flightbriefing.flightdatabase.delete" description="User is allowed to delete a flight." license="license.aw"/>
  <access-right name="applications.meteo.flightforecast" description="User is allowed to run the Flight Forecast application."/>
  <access-right name="applications.meteo.imageplayer" description="User is allowed to run the Image Player application."/>
  <access-right name="applications.meteo.mapeditor" description="User is allowed to run the Map Editor application."/>
  <access-right name="applications.meteo.mapeditor.save" description="User is allowed to save maps."/>
  <access-right name="applications.meteo.mapprint" description="User is allowed to define and print multiple maps in the Map Print application."/>
  <access-right name="applications.meteo.meteochart" description="User is allowed to run the Meteo Chart application."/>
  <access-right name="applications.meteo.messageeditor" description="User is allowed to run the Message Editor application."/>
  <access-right name="applications.meteo.messageeditor.admin" description="User has administrative rights for the Message Editor application."/>
  <access-right name="applications.meteo.messageview" description="User is allowed to run the Message Viewer application."/>
  <access-right name="applications.meteo.pipelineeditor" description="User is allowed to run Production Pipeline Editor application."/>
  <access-right name="applications.meteo.placeeditor" description="User is allowed to run the Place Editor application."/>
  <access-right name="applications.meteo.productcatalog" description="User is allowed to define new products in the Product Catalog application."/>
  <access-right name="applications.meteo.regulartaskstatistics" description="User is allowed to run Regular Task Statistics application."/>
  <access-right name="applications.meteo.reportmonitor" description="User is allowed to run the Report Monitor application."/>
  <access-right name="applications.meteo.tafmonitor" description="User is allowed to run TAF Monitor configuration."/>
  <access-right name="applications.meteo.taskeditor" description="User is allowed to run the Task Editor application."/>
  <access-right name="applications.meteo.taskmanager" description="User is allowed to run the Task Manager application."/>
  <access-right name="applications.meteo.taskmanager.enable-disable-jobs" description="User is allowed to enable and disable jobs in Task Manager application."/>
  <access-right name="applications.meteo.tofmonitor" description="User is allowed to run TOF Monitor application."/>
  <access-right name="applications.meteo.vectoreditor" description="User is allowed to run the Vector Editor application."/>
  <access-right name="applications.mainpanel" description="User is allowed to run the Main Panel."/>
  <access-right name="applications.mainpanel.license.import" description="User is allowed to import license from Main Panel."/>
  <access-right name="applications.mainpanel.edit-regular-tasks" description="User is allowed to add/edit regular tasks."/>
  <access-right name="applications.mainpanel.users-logged-on" description="User is allowed to view users logged on server."/>
  <access-right name="applications.featureeditor" description="User is allowed to run the Feature Editor application."/>
  <access-right name="applications.forecaster" description="User is allowed to run the Forecaster application."/>
  <access-right name="applications.marinetows" description="User is allowed to run Marine Tows application."/>
  <access-right name="applications.responsibilityschedules.configuration" description="User is allowed to run Responsibility Schedules Configuration."/>
  <access-right name="applications.responsibilityschedules.dataentry.modify" description="User is allowed to modify content of responsibility schedule databases."/>
  <access-right name="applications.satelliteviewer" description="User is allowed to run the Satellite Viewer application." license="license.sw"/>
  <access-right name="applications.system.mwconsole.General.BrowseEntireFileSystem" license="license.mw"/>
  <access-right name="applications.system.mwconsole.General.ShowPassword" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.LoadFromFile" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.SaveToFile" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.Print" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.LoadForm" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.EditForm" license="license.mw"/>
  <access-right name="applications.system.mwconsole.File.SaveForm" license="license.mw"/>
  <access-right name="applications.system.mwconsole.View.ChannelStatus" license="license.mw"/>
  <access-right name="applications.system.mwconsole.View.SystemLog" license="license.mw"/>
  <access-right name="applications.system.mwconsole.View.FaxViewer" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.NextNRMessage" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.NextIMMessage" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.NextIRMessage" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.NextOMMessage" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.ClearQueue" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.MessageFromDatabase" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.ReportsFromDatabase" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Retrieve.FileFromDatabase" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendToSystem" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendToChannels" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendToTimeWatch" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendAsAddressedMessage" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendCombinedRequest" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Send.SendFilesToRelay" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Channel.Start/Stop" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Channel.Input/Output" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Channel.Download/Upload" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Channel.New/Delete" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChannelBypassing" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChannelBypassing.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChannelRedirection" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChannelRedirection.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.QueueReorganization" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.QueueReorganization.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.DataTransferQuotas" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.DataTransferQuotas.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.AMHSSubmissionJournal" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.RoutingInformation" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChangeDestination" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.ChangeDestination.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.MailOriginators" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.MailOriginators.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.PhoneUsers" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.PhoneUsers.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.FTPUsers" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.FTPUsers.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.WebUsers" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Communication.WebUsers.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageRouting" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageRouting.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageRoutingBulkChange" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageRoutingBulkChange.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.TimedMessageRouting" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.TimedMessageRouting.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.AddressedMessageRouting" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.AddressedMessageRouting.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageCompilation" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageCompilation.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageGeneration" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageGeneration.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.BUFRCompilation" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.BUFRCompilation.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.ModelSubsetting" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.ModelSubsetting.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageConversions" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageConversions.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageCorrections" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageCorrections.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileDatabase" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileDatabase.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileRouting" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileRouting.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileRenaming" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.FileRenaming.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageSpecifications" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageSpecifications.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.ReportSpecifications" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.ReportSpecifications.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageHeadingWhitelist" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageHeadingWhitelist.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageHeadingBlacklist" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageHeadingBlacklist.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageInputLimitations" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.MessageInputLimitations.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.RequestProtection" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.RequestProtection.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.OPMETRequestProtection" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Data.OPMETRequestProtection.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.RealTimeMonitoring" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.RealTimeMonitoring.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.ReportMonitoringStatistics" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.BulletinMonitoring" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.BulletinMonitoring.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.ChannelMonitoring" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.ChannelMonitoring.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.ChannelMonitoringView" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.DirectoryMonitoring" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.DirectoryMonitoring.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.OPMETMonitoring" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Monitoring.OPMETMonitoring.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.StatisticsBrowser" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.StatisticsBrowser.ExportToFile" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogBrowser" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration.ConfirmationOptional" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration.CanCreateConfirmationRules" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration.CanSelectivelyDeactivateConfirmationRules" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.SystemLogConfiguration.CanCreateNotificationRules" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.StationCatalogue" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.StationCatalogue.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.LocalDirectory" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.RemoteDirectory" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.OutputToChannel" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.OutputToFile" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.OutputViaFtp" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ArchiveRetrieval.InsertToDatabase" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.NetworkTools" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.KnownHosts" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.KnownHosts.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.BUFREnrichment" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.BUFREnrichment.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ConsoleOrders" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.ConsoleOrders.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.Options" license="license.mw"/>
  <access-right name="applications.system.mwconsole.Tools.Options.Modify" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwcontrol.Startup" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwcontrol.Restart" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwcontrol.Shutdown" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwcontrol.RunFromConsole" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwconfig.RunFromConsole" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwrepview.RunFromConsole" license="license.mw"/>
  <access-right name="applications.system.mwconsole.mwbufrview.RunFromConsole" license="license.mw"/>
  <access-right name="applications.system.operatorconsole.view" description="User is allowed to run operator console." license="license.mw"/>
  <access-right name="applications.system.swconfig" description="User is allowed to run the Satellite Weather Configuration application." license="license.sw"/>
  <access-right name="applications.trainingserver" description="User is allowed to run training simulator server GUI."/>
</XIBL-STORAGE>