FROM 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-base:latest AS build-image

# move local files into a /deps/ folder
COPY --chown=vwadmin:vwadmin shredder/config/. /deps/shredder-config
COPY --chown=vwadmin:vwadmin shredder/functions/. /deps/functions
COPY --chown=vwadmin:vwadmin shredder/startup.sh /deps/.

USER vwadmin

ENV METPATH=/opt/OpenWeather
ENV PATH=${METPATH}/config/bin:${METPATH}/bin:${METPATH}/etc:${PATH}

WORKDIR ${METPATH}

RUN umask 0002 \
    && rsync -a /deps/shredder-config/ config/ \
    && rsync -a /deps/functions . \
    && cp /deps/startup.sh . \
    && chmod +x ${METPATH}/startup.sh \
    && source ./isystem.env \
    && iplugins --rebuild 

FROM build-image AS deploy_container

USER vwadmin

WORKDIR ${METPATH}

ENTRYPOINT ["/bin/bash", "-c", "$METPATH/startup.sh"]
