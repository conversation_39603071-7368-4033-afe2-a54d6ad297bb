FROM 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-base:latest

# Create a working user and env
USER vwadmin

ENV METPATH=/opt/OpenWeather
ENV PATH=${METPATH}/config/bin:${METPATH}/bin:${METPATH}/etc:${PATH}

# Copy jupyter install/run scripts
COPY --chown=vwadmin:vwadmin jupyter/ ${METPATH}/config/jupyter/

# Install Jupyter notebook dependenciesexit
RUN chmod +x ${METPATH}/config/jupyter/install_jupyter.sh \
    && ${METPATH}/config/jupyter/install_jupyter.sh

# Copy and make run script executable
RUN chmod +x ${METPATH}/config/jupyter/start_jupyter.sh \
    && cp ${METPATH}/config/jupyter/start_jupyter.sh ${METPATH}/bin/

# Set entrypoint to run Jupyter
ENTRYPOINT ["/opt/OpenWeather/bin/start_jupyter.sh"]
