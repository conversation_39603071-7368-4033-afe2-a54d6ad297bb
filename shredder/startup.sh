#!/bin/sh
# ============================================================
# This script is intended to set the proper configuration
# for shredder vs selection compiler.  These two configurations
# are nearly the same so we keep them in the same repo - the minor
# differences can be handled here.
#
# It is looking at the $INSTANCE_ROLE env variable to determine
# what role to take.  This should be passed into the docker run command.
# ============================================================

role="$INSTANCE_ROLE"


if [ "$role" == "SHREDDER" ]
then
    mv $METPATH/config/functions-shredder $METPATH/config/functions
fi

if [ "$role" == "SELECTION_COMPILER" ]
then
    mv $METPATH/config/functions-selcompiler $METPATH/config/functions
    # ensure .policy file is created for housekeep to work
    # cloudtool.py os set-policy elasticsearch://objectstorage --retention P1D
fi

if [ "$role" == "SPLINTER" ]
then
    mv $METPATH/config/functions-splinter $METPATH/config/functions
fi

isystem startwatch