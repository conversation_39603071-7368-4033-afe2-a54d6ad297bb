<?xml version="1.0" encoding="UTF-8"?>
<XIBL-STORAGE version="1.1">
	<!-- Define directories to scan -->
	<scanner class="iplugins.Platform.Python" base="IBL.Plugins.FieldDiagnostics"/>
	<scanner class="iplugins.Platform.Python" base="IBL.Plugins.Storage"/>
	<scanner class="iplugins.Platform.Python" base="IBL.Plugins.Kernel"/>
	<scanner class="iplugins.Platform.Python" base="IBL.Plugins.Kernel.GATE"/>
	<scanner class="iplugins.Platform.Python" base="IBL.Plugins.Maps"/>
    <scanner class="iplugins.Platform.Python" base="IBL.Plugins.Protocols"/>
	<scanner class="iplugins.Platform.Native" module="libidb"/>
	<scanner class="iplugins.Platform.Native" module="libdbase"/>
	<scanner class="iplugins.Platform.Native" module="libkernel"/>
	<scanner class="iplugins.Platform.Native" module="libmaps"/>
	<scanner class="iplugins.Platform.Native" base="lib/kernel"/>
	<scanner class="iplugins.Platform.Native" base="lib/maps"/>
	<scanner class="iplugins.Platform.Native" base="lib/plugins"/>
	<scanner class="iplugins.Platform.Native" base="" module="libfeaturedatabase"/>
	<!-- Customer specific modules -->
	<scanner class="iplugins.Platform.Python" base="Custom" recursive="true"/>
</XIBL-STORAGE>
