# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2018
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

from IBL.Cloud.GED.SelectionCompiler.Rules import DeclareDirectory, InstantPublish
from IBL.Cloud.GED.ShredderConfig import ExplicitShredderConfig

SHREDDER_CONFIG = ExplicitShredderConfig(
    rules=[
        InstantPublish("KGFH", "sd:NOAA-GFS-PGRB2-0p25-RAW", dstModelTitle="NOAA - GFS PGRB2 0.25x0.25"),
        InstantPublish("HRRR", "sd:NOAA-HRRR", dstModelTitle="High Resolution Rapid Refresh"),
        InstantPublish("HRRRSUBH", "sd:NOAA-SUBHOURLY", dstModelTitle="High Resolution Rapid Refresh Subhourly"),
    ],
)
