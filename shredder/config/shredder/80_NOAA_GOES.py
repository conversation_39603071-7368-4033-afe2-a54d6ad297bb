# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2018
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

from IBL.Cloud.GED.SelectionCompiler.Rules import InstantPublish
from IBL.Cloud.GED.ShredderConfig import ExplicitShredderConfig

SHREDDER_CONFIG = ExplicitShredderConfig(
    rules=[
        InstantPublish("ABIG16FD", "sd:NOAA-GOES16", dstModelTitle="NOAA - GOES 16"),
        InstantPublish("ABIG17FD", "sd:NOAA-GOES17", dstModelTitle="NOAA - GOES 17"),
        InstantPublish("ABIG18FD", "sd:NOAA-GOES18", dstModelTitle="NOAA - GOES 18"),
        InstantPublish("ABIG19FD", "sd:NOAA-GOES19", dstModelTitle="NOAA - GOES 19"),
    ],
)
