# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2018
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

from IBL.Cloud.GED.SelectionCompiler.Rules import DeclareDirectory, InstantPublish
from IBL.Cloud.GED.ShredderConfig import ExplicitShredderConfig

SHREDDER_CONFIG = ExplicitShredderConfig(
    rules=[
        InstantPublish("GEFS", "sd:NOAA-GEFS-Ensemble", dstModelTitle="NOAA - GEFS Ensemble"),
    ],
)
