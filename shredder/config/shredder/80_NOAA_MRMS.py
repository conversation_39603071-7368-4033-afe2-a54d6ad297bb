# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2018
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

from IBL.Cloud.GED.SelectionCompiler.Rules import InstantPublish
from IBL.Cloud.GED.ShredderConfig import ExplicitShredderConfig

SHREDDER_CONFIG = ExplicitShredderConfig(
    rules=[
        InstantPublish("MRMS", "sd:MRMS_CONUS", dstModelTitle="MRMS CONUS"),
        InstantPublish("MRMSAK", "sd:MRMS_ALASKA", dstModelTitle="MRMS ALASKA"),
        InstantPublish("MRMSHI", "sd:MRMS_HAWAII", dstModelTitle="MRMS HAWAII"),
        InstantPublish("MRMSCRB", "sd:MRMS_CARIB", dstModelTitle="MRMS CARIBBEAN"),
        InstantPublish("MRMSGUAM", "sd:MRMS_GUAM", dstModelTitle="MRMS GUAM"),
    ],
)
