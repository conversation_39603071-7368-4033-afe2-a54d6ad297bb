# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2018
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

import IBL.Geo as Geo
import IBL.Kernel as K
import IBL.Cloud.GED.Upload.UploadOptions as UploadOptions

# Global settings - Queues and metrics
#######################################################################################################################

# abbreviations for queue names, preference of processing follows lexicographical order of abbreviated names
QUEUE_NAME_ABBREVIATIONS = {}

# mapping model names from metrics to abbreviate queue name
METRICS_NAME_MAPPING = {}

# Global settings - NWP Selection Directories
#######################################################################################################################

# Deprecated settings:
#   NWP_DIRECTORY_NAME_MAPPING
#   MODELID_TO_NWP_DIRECTORY_MAPPING
#   MODEL_CUTOFF_TIME

# Global settings - NWPSelectionCompiler
#######################################################################################################################


def createCompilerRules():
    # NOTE: Add specific includes here to avoid cyclical dependencies
    import IBL.Cloud.GED.SelectionCompiler.Rules as Rules
    import IBL.Cloud.GED.SelectionCompiler.RulesConfig as RulesConfig

    return Rules.RuleCollection(
        [
            # used only by Satellite Weather for now
            RulesConfig.CollectionFromDirectory(dir_path="config/shredder/rules/", allow_directory_not_exist=False),
        ]
    )


# Global settings - Model uploader
#######################################################################################################################

STRATEGY_NONE = "none"  # no automated upload, upload only listed parameters
STRATEGY_AGGREGATES = "aggregates"  # automatically upload aggregated parameters
STRATEGY_ALL = "all"  # automatically upload all parameters (aggregates are uploaded as aggregates, other according to their parameter number)
STRATEGY_COMPONENTS = "components"  # automatically upload all components (components are uploaded according to their GRIB parameter numbers)

# auto-upload strategy
AUTO_UPLOAD_STRATEGY = STRATEGY_COMPONENTS

# explicit list of parameters to be uploaded (besides parameters selected by auto upload strategy)
UPLOAD_PARAMETERS = set()

# excluded parameters that shall be normally matched by auto upload strategy
UPLOAD_EXCLUDE_PARAMETERS = set()

# model blacklists and whitelists, white list takes precedence if matched by both
MODEL_BLACKLIST = ("pc:*",)
MODEL_WHITELIST = ()

# model translation may be used to rename models in cloud storage (dictionary str : str)
MODEL_TRANSLATION = {
    "pc:Radar/RADARNET/UKRR-1km#Precomputed": "UKRR1KM",
    "pc:Radar/RADARNET/UKRR-500m#Precomputed": "UKRR500M",
    "pc:Radar/RADARNET/UKPT-1km#Precomputed": "UKPT1KM",
    "pc:Radar/RADARNET/UKPT-500m#Precomputed": "UKPT500M",
    "Tv8B////": "DWDICON",
}


def buildUploadOptions(data):
    """
    Method responsible for creating upload options.
    @type data: L{IBL.Geo.Grid}
    @param data: Grid to build upload metadata for.
    @rtype: L{IBL.Cloud.GED.Upload.UploadOptions.UploadOptions}
    """
    options = UploadOptions.UploadOptions.buildDefaultOptions(data)
    return options


def listSupplementaryFiles(l_files):
    result = set()
    return result
