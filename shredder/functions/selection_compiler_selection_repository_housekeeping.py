# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    14.09.2022
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

"""
Service function performing housekeeping of selection repository.

Some repositories do not need housekeeping, but especially elasticsearch
requires housekeeping.

Function is expected to be called periodically with period at least of 1 day.

You shall set either `ICONTENTREPOSITORY`, `ISELECTIONREPOSITORY` or `IGED`.
"""
import typing

import API.NWPSelections
import IBL.Cloud.Functions as Functions


@Functions.asHandler
def lambda_handler(event: Functions.JSONType, context: typing.Any) -> str:
    API.NWPSelections.Repository().prune()
    return "OK"


if __name__ == "__main__":
    print(Functions.callHandler(lambda_handler, {}))
