# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    24.01.2019
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

"""
Queue processor (service function) performing GED Ingestion.

This function can be attached to shredder queue to perform data ingestion.

Environment variables:

    API_KEY         (deprecated) Applies to Lotus messages (Service Hub), which require api key for authorization.
                    Now using CredentialStore which recognises LOTUS_API_KEY instead, also other credentials
                    may be given e.g. via MUSTANG_API_KEY / MUSTANG_CUSTOMER_ID.
    EMPTY_AS_ERROR  Treat empty files as error. You shall limit SQS trigger to 1 message to make this working.
    SHREDDER_QUEUE  Queue for shredder messages (processed by Selection Compiler)

Process messages one-by-one from SQS queue (batch size shall be 1), to make sure errors are properly handled.
Also remember that that disk space in AWS Lambda is limited to around 500MB so AWS Lambda can process only smaller files.
Enable many concurrent functions to minimise latency.

The function is propagating errors, so if e.g. download fails, the exception is returned,
which in turn shall retry the processing according to queue's redrive policy.
You shall configure redrive policies for all queues to avoid infinite processing.

"""

import os
import typing

import IBL.Cloud.Functions as Functions
import IBL.Cloud.GED.CredentialStore as CredentialStore
import IBL.Cloud.GED.DataProcessor as DataProcessor
import IBL.Cloud.GED.ShredderMessage as ShredderMessage
import IBL.Cloud.GED.Upload.ModelUpload as ModelUpload
import IBL.Protocols.Queue as Queue

#######################################################################################
# Environment variables and static initialization
#######################################################################################

# read environment variables
EMPTY_AS_ERROR = bool(int(os.environ.get("EMPTY_AS_ERROR", "0")))

# provided for backward compatibility
API_KEY = os.environ.get("API_KEY")
if API_KEY is not None:
    CredentialStore.Credentials("LOTUS").setHeaders({"x-api-key": API_KEY})

#######################################################################################
# Processor
#######################################################################################


def processdirectory(directory: str):
    print("EXECUTING")
    with ModelUpload.WithUserDatabase(directory):
        # upload tiles
        uploader = ModelUpload.ModelUploader()
        uploader.setVerbosity(1)
        uploader.setLogFormatJSON(True)
        result = uploader.execute()

        # notify shredder about created fields
        print("REPORTING")
        sm = ShredderMessage.ShredderMessage.fromDirectory(directory, result)
        if EMPTY_AS_ERROR:
            processed = sm.processedFiles()
            # ignore supplementary files as no output is expected
            remainder = [
                x
                for x in sm.files()
                if x not in processed and ModelUpload.SUPPLEMENTARY_PREFIX not in x
            ]
            if remainder:
                for f in remainder:
                    print("ERROR-EMPTY", f)

                raise RuntimeError(
                    f'No output generated for {(" ".join(remainder))}, files not recognized'
                )
        sm.submit()


#######################################################################################
# Handler
#######################################################################################


@Functions.asQueueProcessor
def lambda_handler(claim: Queue.Claim, context: typing.Any) -> None:
    try:
        with DataProcessor.DataProcessor(retryTimeout=5) as p:
            print(f"Processing claim: {claim} for directory {p.directory}")
            p.add(claim)
            processdirectory(p.directory)
    except Exception as e:  # noqa: B902 blind except
        print("ERROR", str(e))
        raise
    except:  # noqa: B001 bare except
        print("ERROR: See traceback for further details")
        import traceback

        traceback.print_exc()
        raise
