# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    06.09.2022
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################
"""
Part of selection compiler which reads shredder queue and populates shredder spool.
Spool is representing data needed for compilation.

The spool and state repository (object storage) must be specified for lambda function via
the following environment variables:
```
COMPILER_STATE=local://state/compiler/
COMPILER_SPOOL=local://tmp/compiler/
```

State repository must support conditional updates. The function may be executed concurrently,
however order of records in spool (for particular model run) are not defined in that case.
"""
import typing

import IBL.Cloud.Functions as Functions
import IBL.Cloud.GED.SelectionCompiler as SelectionCompiler
import IBL.Core as Core
import IBL.Protocols.Queue as Queue
from IBL.Cloud.GED.SelectionCompiler.Compiler import Compiler
from IBL.Cloud.GED.ShredderMessage import ShredderMessage

compiler = Compiler.defaultCompiler()
SelectionCompiler.setupLoggers(1, consoleOnly=True)


@Functions.asQueueBatchProcessor
def lambda_handler(claims: typing.List[Queue.Claim], context: typing.Any) -> None:
    # read all messages from batch
    l_messages = []
    for claim in claims:
        for message in claim.messages():
            l_messages.append(ShredderMessage.fromJSON(message.message()))
    # enqueue in batch
    compiler.enqueueBatch(l_messages)
    Core.profileSummary(False)
