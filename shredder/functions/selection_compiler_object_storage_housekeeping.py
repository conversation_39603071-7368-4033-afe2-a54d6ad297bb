"""
Service function performing object storage housekeeping.

The housekeeping is controlled by the following environment variables:
```
SOURCES=elasticsearch://state local
RETENTION=PT1D
```

The buckets to be processed are specified by SOURCES environment variable.
You can add multiple space separated sources into variable.
The above example performs housekeeping of bucket elasticsearch://state
and all buckets from local: storage.

Variable RETENTION is optional. If set, top level policy with given retention is enforced.

Only object storages implementing lifecycle management are supported. At this time this is only elasticsearch.
"""

import os
import typing

import IBL.Cloud.Functions as Functions
import IBL.Core as Core
import IBL.Protocols.ObjectStorage as ObjectStorage


def sources():
    """
    Iterate buckets for housekeeping.
    """
    s_buckets = os.environ.get("SOURCES")
    if s_buckets is None:
        raise RuntimeError("No SOURCES specified")

    for s_bucket in s_buckets.split():
        if ":" not in s_bucket:
            storage = ObjectStorage.ObjectStorage.fromUri(f"{s_bucket}:")
            for cur in storage.listBuckets():
                yield storage.bucket(cur).rootObject()
        else:
            yield ObjectStorage.Object.fromUri(s_bucket)


def retention():
    """
    Get explicit retention if any.
    """
    s_retention = os.environ.get("RETENTION", "null")
    retention = Core.parseTimeSpan(s_retention)
    if retention is None and s_retention != "null":
        raise RuntimeError(f"Invalid specification of RETENTION: '{s_retention}'")
    return retention


@Functions.asHandler
def lambda_handler(event: Functions.JSONType, context: typing.Any) -> None:

    for obj in sources():
        print(f"Housekeeping {obj.uri}")
        if obj.lifecycleManagement is None:
            raise RuntimeError(
                f"Bucket {obj.uri} does not implement lifecycle management"
            )

        # apply bucket retention
        if retention() is not None:
            policy = obj.lifecycleManagement.lifecyclePolicy()
            if policy is None or policy.retention != retention():
                print(
                    f"... setting retention for {obj.uri} to {Core.formatTimeSpan(retention())}"
                )
                obj.lifecycleManagement.setLifecyclePolicy(
                    ObjectStorage.LifecyclePolicy(retention=retention())
                )

        # perform housekeeping
        obj.lifecycleManagement.housekeep()


if __name__ == "__main__":
    Functions.callHandler(lambda_handler, {})
