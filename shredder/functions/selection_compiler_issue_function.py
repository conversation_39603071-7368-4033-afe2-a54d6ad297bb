# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    06.09.2022
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################
"""
Part of selection compiler issuing issuing scheduled selections.
The shared state is cached globally and is refreshed with each function call.

The compilation process can be controlled with the following environment variables:
```
COMPILER_STATE=local://state/compiler/
COMPILER_SPOOL=local://tmp/compiler/
DATA_EXPIRATION=P1D
SELECTION_TOPIC=ged-selection
COMPILER_MAX_ATTEMPTS=999
```
Note: The issue function is controlled by queue, so there is COMPILER_MAX_ATTEMPTS set to 999.

State repository must support conditional updates.
"""
import typing

import IBL.Cloud.Functions as Functions
import IBL.Cloud.GED.SelectionCompiler as SelectionCompiler
import IBL.Kernel as K
import IBL.Protocols.Queue as Queue
from IBL.Cloud.GED.SelectionCompiler.Base import CompilationRequest
from IBL.Cloud.GED.SelectionCompiler.Compiler import Compiler

compiler = Compiler.defaultCompiler()
SelectionCompiler.setupLoggers(1, consoleOnly=True)


@Functions.asQueueProcessor
def lambda_handler(claim: Queue.Claim, context: typing.Any) -> None:
    K.NWPSelectionRepository.getInstance().poll()
    # get keys to be processed
    for m in claim.messages():
        req = CompilationRequest.fromDict(m.messageAsDict())
        print(f"ISSUE-BEGIN {req}")
        result = compiler.processPendingCompilation(
            maxCompilationAttempts=999, keys=[req.key()], sync=True
        )
        if not result:
            raise RuntimeError(f"Item {req.key()} not ready for compilation")
        for item in result:
            print(f"ISSUE-END {item}")
