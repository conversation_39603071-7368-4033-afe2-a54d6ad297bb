# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    06.09.2022
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################
"""
Part of selection compiler scheduling selections for issue.

The lambda function reviews all shared states and checks whether there are any that can be issued.
The shared state is cached globally and is refreshed on each function call.

The function can work in two modes:
* Compiler -- performing both scheduling and compilation
* Scheduler -- performing only scheduling, compilation is performed by dedicated function

When deploying compiler you may specify the following environment variables:
```
COMPILER_STATE=local://state/compiler/
COMPILER_SPOOL=local://tmp/compiler/
DATA_EXPIRATION=P1D
SELECTION_TOPIC=ged-selection
COMPILER_MAX_ATTEMPTS=3
```

When deploying scheduler you may specify the following variables:
```
COMPILER_STATE=local://state/compiler/
COMPILER_SPOOL=local://tmp/compiler/
COMPILER_QUEUE=compiler-queue
```

Some cloud platform does not support sub-minute function invocation. You can use `RUN_IN_LOOP=PT1M`
to overcome this issue. However this works only for schedule operation.

State repository must support conditional updates. The locking mechanism is used to ensure that function
is executed exactly once at a time.
"""
import os
import time
import typing

import IBL.Cloud.Functions as Functions
import IBL.Cloud.GED.SelectionCompiler as SelectionCompiler
import IBL.Core as Core
from IBL.Cloud.GED.SelectionCompiler.Compiler import Compiler

compiler = Compiler.defaultCompiler()
SelectionCompiler.setupLoggers(1, consoleOnly=True)
compiler.createDestinationDirectories()


def scheduleNow():
    # schedule items for compilation
    try:
        compiler.sync()
        compiler.scheduleCompilation(timeout=30)
    except TimeoutError:
        print("Suppressed, as already running")


def schedule():
    runInLoop = Core.parseTimeSpan(os.environ.get("RUN_IN_LOOP", "null"))
    if runInLoop is not None:
        # simulate sub-minute periodic execution
        deadline = time.monotonic() + runInLoop
        while time.monotonic() < deadline:
            Core.clogInfo("scheduling...")
            scheduleNow()
            time.sleep(5)
    else:
        scheduleNow()


@Functions.asHandler
def lambda_handler(event: Functions.JSONType, context: typing.Any) -> None:

    # when compilation queue is set, not compilation is done within scope of this function
    if compiler.settings().compilationQueue is not None:
        schedule()

    else:
        # perform both scheduling and compilation
        compiler.process()
