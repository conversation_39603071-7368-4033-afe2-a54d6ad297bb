# -*- coding: UTF-8 -*-
#################################################################################################
##
##  Created:    05.09.2019
##  Authors: <AUTHORS>
##
## <AUTHOR> <EMAIL>.
##  All rights reserved. Unauthorised use, modification or redistribution is prohibited.
##
#################################################################################################

"""
Queue processor for filtering, blacklisting, renaming, switching, backuping data notifications.

Environment variables:

    DATA_MAX_SIZE               Maximum file size to be processed by service functions. Smaller files are moved to LAMBDA_QUEUE,
                                bigger files are moved to OVERFLOW_QUEUE (e.g. 314572800 - 300*1024*1024)
    LAMBDA_QUEUE                Queue which is processed via GEDIngestion.py service function.
    OVERFLOW_QUEUE              Queue which is processed on Shredder instances

    AUTODETECT_AWS_TOPIC        Whether to autodetect AWS topic from file name.

    BACKUP_QUEUE                Optional queue for storing copy of notifications matching BACKUP_FILENAME_PATTERN
    BACKUP_FILENAME_PATTERN     Optional variable in Unix shell-style wildcards matching file name after whole processing (filtering, blacklisting ...). Use : to define multiple patterns. (e.g. *NewGOES16Object_OR_ABI-L2-*-M6C02*:*NewGOES16Object_OR_ABI-L2-*-M6C12*)
"""

import datetime
import json
import os
import re
import typing

import IBL.Cloud.GED.DataNotification as DataNotification
import IBL.Protocols.Notification as Notification
import IBL.Protocols.Queue as Queue
from IBL.Cloud import Functions

#######################################################################################
# Environment variables and static initialization
#######################################################################################

sqs = Queue.Service.service()  # explicit service caches queue-by-name resolutions
MAX_DAYS = 90
MAX_AGE = datetime.timedelta(days=MAX_DAYS)
bad_file_name = "filename does not end with .gr2"


class FormatLogger:
    """
    Class for formatting logging output statement
    """

    def __init__(self, op, dn=None, model=None, reason=None):
        self.event = {}
        self.event["op"] = op
        if dn is not None:
            self.event["fileName"] = dn.filename()
        if model:
            self.event["model"] = model
        if reason:
            self.event["reason"] = reason

    @property
    def json(self):
        return json.dumps(self.event)

    def format_file(self):
        return "%s: %s" % (self.event["op"], self.json)  # noqa


class LoggingEvents:
    def __init__(self):
        self._events: typing.List[FormatLogger] = []

    def add(self, *args, **kwargs):
        logging_events = FormatLogger(*args, **kwargs)
        print(logging_events.format_file())
        self._events.append(logging_events)


def _check_age_validity(
    dn: DataNotification,
    pattern: re.Pattern,
    filename: str,
    model: str,
    logger: LoggingEvents,
) -> bool:
    """Given a regex pattern and string, extracts date and compares to MAX_DAYS. If the files date is more than 90 days in the past then return False, otherwise True.

    Args:
        pattern (re.Pattern): regex pattern.
        filename (str): file name that contains a date.

    Returns:
        bool: False if the files date is more than 90 days in the past, otherwise True.
    """

    matches = pattern.match(filename)

    if matches is None:
        logger.add(
            "IGNORING", dn=dn, model=model, reason="No matches for the regex pattern"
        )
        return False

    try:
        file_year = int(matches.group("year"))

        # some file names have 2 digit years
        if file_year < 100:
            file_year += 2000

        file_month = int(matches.group("month"))
        file_day = int(matches.group("day"))

        file_date = datetime.date(file_year, file_month, file_day)
        now_date = datetime.datetime.now().date()

        if now_date - file_date > MAX_AGE:
            logger.add(
                "SKIPPING",
                dn=dn,
                model=model,
                reason=f"{file_date} is more than 90 days in the past",
            )
            return False
        else:
            return True

    except (AttributeError, IndexError, ValueError):
        return False


def attach_overflow_queue():
    s_name = os.environ.get("OVERFLOW_QUEUE")
    if not s_name:
        raise RuntimeError("No OVERFLOW_QUEUE environment variable specified")
    return Queue.Queue.fromQueueName(s_name, service=sqs)


def process_goes(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies GOES files within MAX_AGE, matching the a regex for GOES files

    Empty result means file shall be ignored.

    New file name contains also time stamp, which we use in GED ingestion as nominal time instead of coded time.
    Coded time has millisecond precision, which makes it difficult to work with (especially when merging channels),
    or selecting particular channel from GUI

    Args:
        filename (str): Input filename of GOES data

    Returns:
        Optional[str]: Filename
    """

    PATTERN = re.compile(
        r".*OR_ABI-(L\w+)-(\w+)-([A-Z0-9]+)_(G\d+)_s(\d{7})(\d{4})\d+_e(\d{7})(\d{4})\d+_c(\d{7})(\d{4})\d+(.*\.nc).*"
    )

    match = PATTERN.match(filename)
    if match is None:
        logger.add("SKIPPING", dn=dn, model=model, reason="No regex match in filename")
        return

    def convert_date(julian_date_string):
        day = datetime.datetime.strptime(julian_date_string, "%Y%j").date()
        return day.strftime("%Y-%m-%d")

    # use the already converted julian date string instead of the original file name
    GOES_PATTERN = re.compile(r"^(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})$")

    if _check_age_validity(
        dn, GOES_PATTERN, convert_date(match.group(5)), model, logger
    ):
        # use only the start time in the input file
        return "OR_ABI-{0}-{1}-{2}_{3}_s{4}-{5}{6}".format(
            match.group(1),
            match.group(2),
            match.group(3),
            match.group(4),
            convert_date(match.group(5)),
            match.group(6),
            match.group(11),
        )


def process_gfs0_25(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies GFS 0.25 x 0.25 files within MAX_AGE, contains all of the following: [gfs.t, pgrb2.0p25.f], not ending in .idx

    Args:
        filename (str): Input filename of GFS data

    Returns:
        Optional[str]: Filename
    """

    valid_filenames = ["gfs.t", "pgrb2.0p25.f"]

    for valid_filename in valid_filenames:
        if valid_filename not in filename:
            logger.add(
                "SKIPPING",
                dn=dn,
                model=model,
                reason=f"{valid_filename} not found in filename",
            )
            return
        else:
            return filename
        

def process_gefs(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies GEFS files  not ending in .idx

    Args:
        filename (str): Input filename of GFS data

    Returns:
        Optional[str]: Filename
    """

    valid_filenames = ["pgrb2a.0p50"]

    for valid_filename in valid_filenames:
        if valid_filename not in filename:
            logger.add(
                "SKIPPING",
                dn=dn,
                model=model,
                reason=f"{valid_filename} not found in filename",
            )
            return
        else:
            return filename


def process_mrms(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies MRMS files within MAX_AGE, containing one of the following: [EchoTop_18_00.50_, VIL_00.50_,
        MergedReflectivityQComposite_00.50_, MergedReflectivityQCComposite_00.50_], and not having .idx extension

    Args:
        filename (str): Input filename of MRMS data

    Returns:
        Optional[str]: Filename
    """

    if filename.endswith(".grib2"):
        return filename
    elif filename.endswith(".grib2.gz"):
        logger.add(
            "SKIPPING", dn=dn, model=model, reason="File needs to be unzipped before processing"
        )
    else:
        logger.add(
            "SKIPPING", dn=dn, model=model, reason="No matching patterns in filename for MRMS"
        )


def process_hrrr(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies HRRR files within MAX_AGE, containing conus, containing wrfsubh or wrfprs, and not having .idx extension

    Args:
        filename (str): Input filename of HRRR data

    Returns:
        Optional[str]: Filename
    """

    HRRR_PATTERN = re.compile(
        r"^.*_hrrr\.(?P<year>\d{4})(?P<month>\d{2})(?P<day>\d{2})_.*$"
    )

    if "conus" not in filename:
        logger.add("SKIPPING", dn=dn, model=model, reason="conus not found in filename")
        return
    if "wrfsubh" not in filename and "wrfprs" not in filename:
        logger.add(
            "SKIPPING",
            dn=dn,
            model=model,
            reason="wrfsubh or wrfprs not found in filename",
        )
        return

    if _check_age_validity(dn, HRRR_PATTERN, filename, model, logger):
        return filename

def process_odl(
    dn: DataNotification, filename: str, model: str, logger: LoggingEvents
) -> typing.Optional[str]:
    """Identifies ODL files within MAX_AGE and has .gr2 extension

    Args:
        filename (str): Input filename of ODL data

    Returns:
        Optional[str]: Filename with extension of .gr2
    """

    ODL_PATTERN = re.compile(
        r"^.*AUTHORITATIVE_\D*_(?P<year>\d{4})(?P<month>\d{2})(?P<day>\d{2})\d{6}Z_.*$"
    )

    if not filename.lower().endswith(".gr2"):
        logger.add("IGNORING", dn=dn, model=model, reason=bad_file_name)
        return

    if _check_age_validity(dn, ODL_PATTERN, filename, model, logger):
        return filename


@Functions.asQueueProcessor
def lambda_handler(claim: Queue.Claim, context: typing.Any) -> None:
    events = LoggingEvents()
    # build notification instance and find new name
    try:
        message = next(iter(claim.messages())).message()
        dn = DataNotification.DataNotification(claim.queue().name(), message)

    except RuntimeError as e:
        events.add("IGNORED", reason="Ignoring malformed record: %s" % str(e))  # noqa
        return

    s_file_name = dn.filename()
    s_new_file_name = dn.filename()
    s_queue_name = dn.queueName()
    s_topic = dn.event().topic().split(":")[-1]

    #######################################################################################
    # file processing
    #######################################################################################
    perform_process = False

    if s_new_file_name.endswith(".idx"):
        events.add("SKIPPING", dn=dn, reason="Skipping file ending with .idx")
        return  # skip file before even checking

    VALID_FILENAMES_HANDLERS = (
        (
            "OR_ABI-L1b-RadF",
            "GOES FULL DISK",
            process_goes,
        ),  # GOES 16 Full Disk
        ("MRMS_", "MRMS", process_mrms),  # MRMS
        ("gfs.t", "GFS", process_gfs0_25),  # GFS
        ("AWS-Open-Data_MODEL_HRRR", "HRRR", process_hrrr),  # HRRR
        ("ODLA_GRIB_AFWA_VW_ODLA-AUTHORITATIVE_", "ODLA", process_odl),  # ODLA
        ("ODLW_GRIB_AFWA_VW_ODLW-AUTHORITATIVE_", "ODLW", process_odl),  # ODLW
        ("gep", "GEFS", process_gefs),  # GEFS
    )

    file_model = "UNKNOWN"

    for startswith_pattern, model, handler in VALID_FILENAMES_HANDLERS:
        if s_file_name.startswith(startswith_pattern):
            file_model = model
            s_new_file_name = handler(dn, s_file_name, model, events)
            perform_process = True

    if perform_process == False or s_new_file_name is None:
        events.add(
            "SKIPPING",
            dn=dn,
            model=file_model,
            reason="Skipping file with no match regex pattern",
        )
        return  # skip file

    queue = attach_overflow_queue()

    # add src queue name and topic as prefix
    s_new_file_name = s_queue_name + "_" + s_topic + "_" + s_new_file_name

    # build proxy message and post, reuse original notification event
    pm = DataNotification.ProxyMessage.fromMessage(
        dn.message(), filename=s_new_file_name, timestamp=dn.timestamp()
    )

    proxy_event = Notification.NotificationEvent.makeEvent(
        s_topic, json.dumps(pm.message()), dn.timestamp()
    )

    queue.message(proxy_event.toJSON()).post()

    events.add("ENQUEUE", dn=dn, model=file_model)
