# Setting things up on a fresh box
You will need to install docker and minikube to start



Enable istio in minikube
```
# enable istio in minikube
minikube addons enable istio-provisioner
minikube addons enable istio

# enable jaeger sample
kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.24/samples/addons/jaeger.yaml

# enable jaeger tracing with istioctl
istioctl install -f infrastructure/istio/tracing.yaml
kubectl apply -f infrastructure/istio/enable_tracing.yaml

# enable kiali
kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.25/samples/addons/kiali.yaml

# enable prometheus
kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.24/samples/addons/prometheus.yaml

# enable grafana
kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.24/samples/addons/grafana.yaml



```

