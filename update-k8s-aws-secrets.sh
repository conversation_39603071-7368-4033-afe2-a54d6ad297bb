#!/bin/bash

NAMESPACE="open-weather"
AWS_SECRET_NAME="aws-credentials"
ECR_SECRET_NAME="ecr-creds"

# Parse command line arguments
USE_EC2_IAM=false
AWS_PROFILE=""
DURATION_HOURS=1  # Default to 1 hour

while [[ $# -gt 0 ]]; do
    case $1 in
        --ec2iam)
            USE_EC2_IAM=true
            shift
            ;;
        --profile)
            AWS_PROFILE="$2"
            shift 2
            ;;
        --duration)
            DURATION_HOURS="$2"
            if [[ ! "$DURATION_HOURS" =~ ^[1-9][0-9]*$ ]] || [[ "$DURATION_HOURS" -gt 12 ]]; then
                echo "❌ Duration must be a positive integer between 1 and 12 hours"
                exit 1
            fi
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [--ec2iam | --profile PROFILE_NAME] [--duration HOURS]"
            echo "  --ec2iam          Use EC2 instance IAM role"
            echo "  --profile NAME    Use AWS CLI profile"
            echo "  --duration HOURS  Session duration in hours (1-12, default: 1)"
            echo "  --help            Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Set up AWS command prefix based on mode
if [[ "$USE_EC2_IAM" == "true" ]]; then
    if [[ -n "$AWS_PROFILE" ]]; then
        echo "❌ Cannot use both --ec2iam and --profile options"
        exit 1
    fi
    AWS_CMD="aws"
    echo "Using EC2 IAM role for authentication..."
    ROLE_ARN="arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/OpenWeatherDevK8SAssumeRole"
elif [[ -n "$AWS_PROFILE" ]]; then
    AWS_CMD="aws --profile $AWS_PROFILE"
    echo "Using AWS profile: $AWS_PROFILE"
    ROLE_ARN="arn:aws:iam::$($AWS_CMD sts get-caller-identity --query Account --output text):role/OpenWeatherDevK8SAssumeRole"
else
    echo "❌ Must specify either --ec2iam or --profile option"
    echo "Use --help for usage information"
    exit 1
fi

echo "Assuming IAM role for K8s credentials (duration: ${DURATION_HOURS} hours)..."

# Calculate duration in seconds
DURATION_SECONDS=$((DURATION_HOURS * 3600))

# Assume the role
ASSUMED_ROLE=$($AWS_CMD sts assume-role \
    --role-arn "$ROLE_ARN" \
    --role-session-name "k8s-secrets-update" \
    --duration-seconds "$DURATION_SECONDS" \
    --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' \
    --output text)

# Extract credentials
AWS_ACCESS_KEY_ID=$(echo "$ASSUMED_ROLE" | cut -f1)
AWS_SECRET_ACCESS_KEY=$(echo "$ASSUMED_ROLE" | cut -f2)
AWS_SESSION_TOKEN=$(echo "$ASSUMED_ROLE" | cut -f3)

if [[ -z "$AWS_ACCESS_KEY_ID" || -z "$AWS_SECRET_ACCESS_KEY" || -z "$AWS_SESSION_TOKEN" ]]; then
    echo "❌ Failed to retrieve AWS credentials!"
    exit 1
fi

echo "✅ Successfully retrieved AWS credentials."

# Check if the secret exists
if kubectl get secret "$AWS_SECRET_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "🔄 Updating existing Kubernetes secret: $AWS_SECRET_NAME..."
    kubectl delete secret "$AWS_SECRET_NAME" -n "$NAMESPACE"
fi

# Create Kubernetes secret
kubectl create secret generic "$AWS_SECRET_NAME" -n "$NAMESPACE" \
    --from-literal=AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
    --from-literal=AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
    --from-literal=AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN"

echo "✅ Kubernetes secret updated successfully."

# Check if the ECR secret exists
if kubectl get secret "$ECR_SECRET_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "🔄 Updating existing Kubernetes secret: $ECR_SECRET_NAME..."
    kubectl delete secret "$ECR_SECRET_NAME" -n "$NAMESPACE"
fi

# Update ECR secret
$AWS_CMD ecr get-login-password --region us-east-2 | kubectl create secret docker-registry "$ECR_SECRET_NAME" \
  --namespace="$NAMESPACE" \
  --docker-server=125100372861.dkr.ecr.us-east-2.amazonaws.com \
  --docker-username=AWS \
  --docker-password=$($AWS_CMD ecr get-login-password --region us-east-2) \
  --docker-email=<EMAIL>

echo "✅ ECR secret updated successfully."
