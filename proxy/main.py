import os
import logging
from fastapi import FastAP<PERSON>, Request, HTTPException, Response
import httpx

# Configure Logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

app = FastAPI()

# Backend service URLs
WMS_GIS_URL = os.getenv("WMS_GIS_URL", "http://open-weather-wms-gis.open-weather-shredder.svc.cluster.local:8008")
WMS_GFS_URL = os.getenv("WMS_GFS_URL", "http://open-weather-wms-gfs.open-weather-shredder.svc.cluster.local:8008")

# Create a global AsyncClient
client = httpx.AsyncClient()

@app.on_event("shutdown")
async def shutdown_event():
    await client.aclose()

@app.get("/ogc/WMS")
async def route_wms(request: Request):
    try:
        # Log request details
        logger.info(f"Incoming request: {request.url}")

        # Extract LAYER query parameter
        layer = request.query_params.get("LAYER", "").upper()

        # Determine backend service
        if layer.startswith("GIS"):
            target_url = WMS_GIS_URL
        elif layer.startswith("GFS"):
            target_url = WMS_GFS_URL
        else:
            logger.warning(f"Invalid or missing LAYER: {layer}, defaulting to WMS_GFS")
            target_url = WMS_GFS_URL  # Default if no LAYER or invalid LAYER

        # Forward request to the correct backend
        headers = {
            "x-request-id": request.headers.get("x-request-id", ""),
            "x-b3-traceid": request.headers.get("x-b3-traceid", ""),
            "x-b3-spanid": request.headers.get("x-b3-spanid", ""),
            "x-b3-sampled": request.headers.get("x-b3-sampled", "1"),
        }

        response = await client.get(target_url + "/ogc/WMS", params=request.query_params, headers=headers)

        # Log successful request forwarding
        logger.info(f"Forwarded request to {target_url}, status {response.status_code}")

        # Handle non-JSON responses (WMS responses are usually XML)
        content_type = response.headers.get("Content-Type", "").lower()
        
        if "application/json" in content_type:
            return response.json()
        else:
            return Response(content=response.content, status_code=response.status_code, media_type=content_type)

    except httpx.HTTPStatusError as e:
        logger.error(f"Error from {target_url}: {e.response.status_code} - {e.response.text}")
        raise HTTPException(status_code=e.response.status_code, detail=f"Upstream error: {e.response.text}")

    except Exception as e:
        logger.exception("Unhandled exception occurred")
        raise HTTPException(status_code=500, detail="Internal Server Error")
