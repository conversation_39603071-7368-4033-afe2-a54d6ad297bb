<?xml version="1.0" encoding="UTF-8"?>
<XIBL-STORAGE version="1.1">
  <version>v3.4.4</version>
  <sequence>1000003</sequence>
  <setup></setup>
  <heading>PZZ/99</heading>
  <paper_ratio_correction>1</paper_ratio_correction>
  <prjname></prjname>
  <legend_display>true</legend_display>
  <projection class="igeo.prj.LambdaPhi" projID="1" allow_interpolation="1" count="0" sf-method="None" lo0="-3.1415926535897931">
    <tr m00="6.2831853071795862" m01="0" m10="0" m11="-3.1415926535897931">
      <z x="-3.1415926535897931" y="1.5707963267948966"/>
    </tr>
    <size.x>0</size.x>
    <size.y>0</size.y>
  </projection>
  <mapType>geographic</mapType>
  <layers class="maps.layers.Coast" name="land" legend_display="true" tooltip_display="false" visible="true" time_control="0" unique_id="1000001">
    <settings skin="doc:global/templates/Land/default" skin_modified="true" autoConvertGray="true" autoConvertBW="true">
      <once coast_precision="150" nopreprint="0"/>
      <color bumplevel="30" city-text-change="0" city_prefer="" city_prefonly="0" citycap="1" citydens="15" citymin="15" cityscale="1" cityshape="0" citysize="3" citytext="1" dem_enabled="0" enable-cities="0" glatlonformat="2" grid="0" grid-cross-size="0" grid-point-size="0" gstepla="0.26179938779914941" gsteplo="0.26179938779914941" gtextauto="1" gtextkeephoriz="0" gtextla="0.26179938779914941" gtextlaofs="0.1308996938995747" gtextlastep="0.26179938779914941" gtextlo="0.26179938779914941" gtextloofs="0.1308996938995747" gtextlostep="0.26179938779914941" gtextpos="1" gtickla="0.26179938779914941" gticklo="0.26179938779914941" height_units="2" lands="1" sign_e="E" sign_n="N" sign_s="S" sign_w="W" station_dens="15" station_placelist="" station_prefer="" station_prefonly="0" station_shape="0" station_size="3" stations="0" steps="1">
        <cityf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityf>
        <cityp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </cityp>
        <cityt attr="128" font="0" size="3.5">
          <color r="0" g="0" b="0" a="255"/>
        </cityt>
        <demg>
          <_0 value="0" middle="2500">
            <color r="0" g="100" b="0" a="255"/>
          </_0>
          <_1 value="5000" middle="0">
            <color r="100" g="100" b="0" a="255"/>
          </_1>
        </demg>
        <geosets min-inlandarea="0" minarea="0" showriverlakes="1">
          <borderp width="0.088194422" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </borderp>
          <coastf style="1">
            <color r="255" g="241" b="205" a="255"/>
          </coastf>
          <coastp width="0.088194422" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </coastp>
          <islandf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </islandf>
          <islandp width="0.17638884" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </islandp>
          <lakef style="1">
            <color r="212" g="229" b="225" a="255"/>
          </lakef>
          <lakep width="0.098777756" style="1">
            <color r="0" g="0" b="0" a="255"/>
          </lakep>
          <pondf style="0">
            <color r="255" g="255" b="255" a="255"/>
          </pondf>
          <pondp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </pondp>
          <provincep width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </provincep>
          <riverp width="0.088194422" style="0">
            <color r="255" g="255" b="255" a="255"/>
          </riverp>
          <terwaterp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </terwaterp>
          <undeff style="1">
            <color r="0" g="0" b="0" a="255"/>
          </undeff>
          <undefp width="0.098777756" style="0">
            <color r="0" g="0" b="0" a="255"/>
          </undefp>
        </geosets>
        <gridp width="0.17638884" style="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridp>
        <gridt attr="160" font="0" size="3">
          <color r="0" g="0" b="0" a="255"/>
        </gridt>
        <gridtickp width="0.17638884" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </gridtickp>
        <seaf style="1">
          <color r="212" g="229" b="225" a="255"/>
        </seaf>
        <seap width="0.35277769" style="0">
          <color r="0" g="0" b="0" a="255"/>
        </seap>
        <stationf style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationf>
        <stationp width="0" style="1">
          <color r="200" g="0" b="0" a="255"/>
        </stationp>
        <stationt attr="128" font="0" size="3.2">
          <color r="0" g="0" b="0" a="255"/>
        </stationt>
      </color>
    </settings>
  </layers>
  <common-data>
    <list key="center">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="dataset">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="level">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="run">
      <value class="token_combo">
        <value value="n"/>
      </value>
    </list>
    <list key="time-axis">
      <value class="time_axis_data" flags="1" mode="CLIP_RANGE"/>
    </list>
  </common-data>
  <legends>
    <layers/>
    <proto>
      <Topic name="Topic" expand_policy="6" search_policy="3" enabled="true" text="">
        <hint x="0" y="1"/>
        <boxp width="0.1" style="1">
          <color r="0" g="0" b="0" a="255"/>
        </boxp>
        <boxf style="1">
          <color r="255" g="255" b="255" a="255"/>
        </boxf>
        <fnt attr="112" font="0" size="4">
          <color r="0" g="0" b="0" a="255"/>
        </fnt>
      </Topic>
    </proto>
  </legends>
</XIBL-STORAGE>
