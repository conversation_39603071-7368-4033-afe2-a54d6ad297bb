<?xml version="1.0"?>
<webservice xmlns:xi="http://www.w3.org/2001/XInclude">
    <crs-definition name="EPSG:900913" type="proj4" proj4-string="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs" />

    <server port="8008" strict="true" error-response-translate="s,/opt/OpenWeather,METPATH,g"
        log-format='{"bytes_sent": %B, "remote_host": "%h", "HTTP_method": "%m", "HTTP_request_id": "%{x-request-id}i", "request_serving_port": %p, "pid": %P, "query_string": "%q", "HTTP_status": %s, "timestamp": "%t", "time_elapsed": %T, "endpoint": "%U", "HTTP_content_type" : "%{content-type}o"}'>
    </server>

    <service name="WMS" path="/ogc/WMS" title="Shyft Web Mapping Services" cors-allow-origin="*" base-uri="${server.host-name}${path}">
        <!-- recommended for observation data, which may change in time -->
        <tiling-profile name="tile256x256-256px-margin-non-cached" tile-width="256" tile-height="256" rendering-margin="256" caching-strategy="none">
            <tiling-boundary crs="EPSG:900913" x-min="-20037508" y-min="-20037508" x-max="20037508" y-max="20037508.34" />
            <tiling-boundary crs="EPSG:4326" x-min="-180" y-min="-180" x-max="180" y-max="180" />
            <tiling-boundary crs="CRS:84" x-min="-180" y-min="-180" x-max="180" y-max="180" />
        </tiling-profile>

        <!-- recommended for NWP data -->
        <tiling-profile name="tile256x256-256px-margin-cached" tile-width="256" tile-height="256" rendering-margin="256" rendering-tile-grid-width="5" rendering-tile-grid-height="5" caching-strategy="hard">
            <tiling-boundary crs="EPSG:900913" x-min="-20037508" y-min="-20037508" x-max="20037508" y-max="20037508.34" />
            <tiling-boundary crs="EPSG:4326" x-min="-180" y-min="-180" x-max="180" y-max="180" />
            <tiling-boundary crs="CRS:84" x-min="-180" y-min="-180" x-max="180" y-max="180" />
        </tiling-profile>

        

        <!-- recommended for satellite/radar data -->
        <tiling-profile name="tile256x256-0px-margin-cached" tile-width="256" tile-height="256" rendering-margin="10" rendering-tile-grid-width="5" rendering-tile-grid-height="5" caching-strategy="hard">
            <tiling-boundary crs="EPSG:900913" x-min="-20037508" y-min="-20037508" x-max="20037508" y-max="20037508.34" />
            <tiling-boundary crs="EPSG:4326" x-min="-180" y-min="-180" x-max="180" y-max="180" />
            <tiling-boundary crs="CRS:84" x-min="-180" y-min="-180" x-max="180" y-max="180" />
        </tiling-profile>

        <!-- recommended for satellite/radar data -->
        <tiling-profile name="tile256x256-0px-margin-non-cached" tile-width="256" tile-height="256" rendering-margin="10" rendering-tile-grid-width="5" rendering-tile-grid-height="5" caching-strategy="none">
            <tiling-boundary crs="EPSG:900913" x-min="-20037508" y-min="-20037508" x-max="20037508" y-max="20037508.34" />
            <tiling-boundary crs="EPSG:4326" x-min="-180" y-min="-180" x-max="180" y-max="180" />
            <tiling-boundary crs="CRS:84" x-min="-180" y-min="-180" x-max="180" y-max="180" />
        </tiling-profile>

        <!--Single High Level Layer for OGC Compliance -->
        <layer-group title="WMS Layers">

            <!-- Web Mapping Service for MODEL_DATA -->
            <xi:include href="wms_configs/iwebservice-model_data.xml" xpointer="MODEL_DATA"/>

            <!-- Web Mapping Service for Radar -->
            <!-- <xi:include href="wms_configs/iwebservice-metwatch_RADAR.xml" xpointer="RADAR"/> -->

            <!-- Web Mapping Service for METSAT -->
            <!-- <xi:include href="wms_configs/iwebservice-satellite_data.xml" xpointer="METSAT"/> -->

            <!-- Web Mapping Service for Backgrounds and Overlays -->
            <xi:include href="wms_configs/iwebservice-bg_and_overlay.xml" xpointer="BACKGROUND_LAYERS"/>

        </layer-group>
	</service>

</webservice>
