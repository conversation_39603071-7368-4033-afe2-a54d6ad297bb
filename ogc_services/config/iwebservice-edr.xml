<?xml version="1.0" ?>
<webservice>
  <service type="edr" path="/ogc/edr" base-uri="${server.url}${path}">
    <!--This configuration was automatically generated by edrtool.py-->
    <collection-profile id="full">
      <position-query/>
      <radius-query/>
      <area-query/>
      <cube-query/>
      <trajectory-query/>
      <corridor-query/>
    </collection-profile>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-depth" edr-id="GFS_between-depth" title="KGFS - Layer between two depths below land surface" profile="full">
      <plain-model-parameter internal-id="1131074" dataset="spec:regular" edr-id="soil-temperature" label="Soil temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1131264" dataset="spec:regular" edr-id="volumetric-soil-moisture-content" label="Volumetric soil moisture content"/>
      <plain-model-parameter internal-id="1132032" dataset="spec:regular" edr-id="liquid-volumetric-soil-moisture-non-frozen" label="Liquid volumetric soil moisture (non frozen)" unit="N_NUM"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-meters" edr-id="GFS_between-height-above-ground" title="KGFS - Layer between two heights above ground" profile="full">
      <plain-model-parameter internal-id="1000706" dataset="spec:regular" edr-id="u-component-storm-motion" label="U component storm motion" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000707" dataset="spec:regular" edr-id="v-component-storm-motion" label="V component storm motion" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-meters" edr-id="GFS_between-height-above-ground_2" title="KGFS - Layer between two heights above ground (2)" profile="full">
      <plain-model-parameter internal-id="1001800" dataset="spec:regular" edr-id="storm-relative-helicity" label="Storm relative helicity"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-pressure-diff" edr-id="GFS_between-pressure-diff" title="KGFS - Layer between two levels at specified pressure differences from ground to level" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000256" dataset="spec:regular" edr-id="specific-humidity" label="Specific humidity"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" edr-id="relative-humidity" label="Relative humidity" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-pressure-diff" edr-id="GFS_between-pressure-diff_2" title="KGFS - Layer between two levels at specified pressure differences from ground to level (2)" profile="full">
      <plain-model-parameter internal-id="1000968" dataset="spec:regular" edr-id="pressure-of-level-from-which-parcel-was-lifted" label="Pressure of level from which parcel was lifted" unit="P_PA"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-pressure-diff" edr-id="GFS_between-pressure-diff_3" title="KGFS - Layer between two levels at specified pressure differences from ground to level (3)" profile="full">
      <plain-model-parameter internal-id="1001798" dataset="spec:regular" edr-id="convective-available-potential-energy" label="Convective available potential energy"/>
      <plain-model-parameter internal-id="1001799" dataset="spec:regular" edr-id="convective-inhibition" label="Convective inhibition"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="between-sigma" edr-id="GFS_between-sigma" title="KGFS - Layer between two sigma levels" profile="full">
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" edr-id="relative-humidity" label="Relative humidity" unit="N_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground" title="KGFS - Height above ground" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_2" title="KGFS - Height above ground (2)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT1H" edr-id="maximum-temperature_stat:max/PT1H" label="Maximum temperature - Maximum 1h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT1H" edr-id="minimum-temperature_stat:min/PT1H" label="Minimum temperature - Minimum 1h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_3" title="KGFS - Height above ground (3)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT2H" edr-id="maximum-temperature_stat:max/PT2H" label="Maximum temperature - Maximum 2h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT2H" edr-id="minimum-temperature_stat:min/PT2H" label="Minimum temperature - Minimum 2h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_4" title="KGFS - Height above ground (4)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT3H" edr-id="maximum-temperature_stat:max/PT3H" label="Maximum temperature - Maximum 3h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT3H" edr-id="minimum-temperature_stat:min/PT3H" label="Minimum temperature - Minimum 3h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_5" title="KGFS - Height above ground (5)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT4H" edr-id="maximum-temperature_stat:max/PT4H" label="Maximum temperature - Maximum 4h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT4H" edr-id="minimum-temperature_stat:min/PT4H" label="Minimum temperature - Minimum 4h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_6" title="KGFS - Height above ground (6)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT5H" edr-id="maximum-temperature_stat:max/PT5H" label="Maximum temperature - Maximum 5h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT5H" edr-id="minimum-temperature_stat:min/PT5H" label="Minimum temperature - Minimum 5h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_7" title="KGFS - Height above ground (7)" profile="full">
      <plain-model-parameter internal-id="1000004" dataset="spec:regular;stat:maximum/PT6H" edr-id="maximum-temperature_stat:max/PT6H" label="Maximum temperature - Maximum 6h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000005" dataset="spec:regular;stat:minimum/PT6H" edr-id="minimum-temperature_stat:min/PT6H" label="Minimum temperature - Minimum 6h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_8" title="KGFS - Height above ground (8)" profile="full">
      <plain-model-parameter internal-id="1000006" dataset="spec:regular" edr-id="dewpoint-temperature" label="Dewpoint temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000021" dataset="spec:regular" edr-id="apparent-temperature" label="Apparent temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" edr-id="relative-humidity" label="Relative humidity" unit="N_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_9" title="KGFS - Height above ground (9)" profile="full">
      <plain-model-parameter internal-id="1000256" dataset="spec:regular" edr-id="specific-humidity" label="Specific humidity"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_10" title="KGFS - Height above ground (10)" profile="full">
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_11" title="KGFS - Height above ground (11)" profile="full">
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" edr-id="pressure" label="Pressure" unit="P_PA"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters" edr-id="GFS_height-above-ground_12" title="KGFS - Height above ground (12)" profile="full">
      <plain-model-parameter internal-id="1004291" dataset="spec:regular" edr-id="reflectivity" label="Reflectivity"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters-above-sea" edr-id="GFS_height-above-sea" title="KGFS - Height above mean sea level" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="meters-above-sea" edr-id="GFS_height-above-sea_2" title="KGFS - Height above mean sea level (2)" profile="full">
      <plain-model-parameter internal-id="1655878" dataset="spec:regular" edr-id="ice-growth-rate" label="Ice growth rate" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="hybrid" edr-id="GFS_hybrid" title="KGFS - Hybrid level" profile="full">
      <plain-model-parameter internal-id="1000278" dataset="spec:regular" edr-id="cloud-mixing-ratio" label="Cloud mixing ratio"/>
      <plain-model-parameter internal-id="1000279" dataset="spec:regular" edr-id="ice-water-mixing-ratio" label="Ice water mixing ratio"/>
      <plain-model-parameter internal-id="1000280" dataset="spec:regular" edr-id="rain-mixing-ratio" label="Rain mixing ratio"/>
      <plain-model-parameter internal-id="1000281" dataset="spec:regular" edr-id="snow-mixing-ratio" label="Snow mixing ratio"/>
      <plain-model-parameter internal-id="1000288" dataset="spec:regular" edr-id="graupel-snow-pellets" label="Graupel (snow pellets)"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="hybrid" edr-id="GFS_hybrid_2" title="KGFS - Hybrid level (2)" profile="full">
      <plain-model-parameter internal-id="1004291" dataset="spec:regular" edr-id="reflectivity" label="Reflectivity"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="isobaric" edr-id="GFS_isobaric" title="KGFS - Isobaric level" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000256" dataset="spec:regular" edr-id="specific-humidity" label="Specific humidity"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" edr-id="relative-humidity" label="Relative humidity" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000520" dataset="spec:regular" edr-id="vertical-velocity-pressure" label="Vertical velocity (pressure)"/>
      <plain-model-parameter internal-id="1000521" dataset="spec:regular" edr-id="vertical-velocity-geometric" label="Vertical velocity (geometric)" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000522" dataset="spec:regular" edr-id="absolute-vorticity" label="Absolute vorticity"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" edr-id="geopotential-height" label="Geopotential height" unit="D_GPM"/>
      <plain-model-parameter internal-id="1003776" dataset="spec:regular" edr-id="ozone-mixing-ratio" label="Ozone mixing ratio"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="isobaric" edr-id="GFS_isobaric_2" title="KGFS - Isobaric level (2)" profile="full">
      <plain-model-parameter internal-id="1000278" dataset="spec:regular" edr-id="cloud-mixing-ratio" label="Cloud mixing ratio"/>
      <plain-model-parameter internal-id="1000279" dataset="spec:regular" edr-id="ice-water-mixing-ratio" label="Ice water mixing ratio"/>
      <plain-model-parameter internal-id="1000280" dataset="spec:regular" edr-id="rain-mixing-ratio" label="Rain mixing ratio"/>
      <plain-model-parameter internal-id="1000281" dataset="spec:regular" edr-id="snow-mixing-ratio" label="Snow mixing ratio"/>
      <plain-model-parameter internal-id="1000288" dataset="spec:regular" edr-id="graupel-snow-pellets" label="Graupel (snow pellets)"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular" edr-id="total-cloud-cover" label="Total cloud cover" unit="C_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="potential-vorticity-surface" edr-id="GFS_potential-vorticity-surface" title="KGFS - Potential vorticity surface" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000704" dataset="spec:regular" edr-id="vertical-speed-shear" label="Vertical speed shear"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" edr-id="pressure" label="Pressure" unit="P_PA"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" edr-id="geopotential-height" label="Geopotential height" unit="D_GPM"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" level-type="sigma" edr-id="GFS_sigma" title="KGFS - Sigma level" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" edr-id="temperature" label="Temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000002" dataset="spec:regular" edr-id="potential-temperature" label="Potential temperature" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" edr-id="relative-humidity" label="Relative humidity" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" edr-id="u-component-of-wind" label="U component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" edr-id="v-component-of-wind" label="V component of wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000520" dataset="spec:regular" edr-id="vertical-velocity-pressure" label="Vertical velocity (pressure)"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level" title="KGFS - Single Level" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" special-level="gnd-surf" edr-id="temperature_gnd-surf" label="Temperature - Ground surface" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" special-level="max-wind" edr-id="temperature_max-wind" label="Temperature - Max. wind" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular" special-level="tropopause" edr-id="temperature_tropopause" label="Temperature - Tropopause" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" special-level="0-isoterm" edr-id="relative-humidity_0-isoterm" label="Relative humidity - 0C isoterm" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" special-level="atmosphere" edr-id="relative-humidity_atmosphere" label="Relative humidity - Atmosphere" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000257" dataset="spec:regular" special-level="tropo-freeze" edr-id="relative-humidity_tropo-freeze" label="Relative humidity - Highest tropospheric freezing level" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000259" dataset="spec:regular" special-level="atmosphere" edr-id="precipitable-water_atmosphere" label="Precipitable water - Atmosphere" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf" label="Precipitation rate - Ground surface" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000267" dataset="spec:regular" special-level="gnd-surf" edr-id="snow-depth_gnd-surf" label="Snow depth - Ground surface" unit="D_M"/>
      <plain-model-parameter internal-id="1000269" dataset="spec:regular" special-level="gnd-surf" edr-id="water-equivalent-of-accumulated-snow-depth_gnd-surf" label="Water equivalent of accumulated snow depth - Ground surface"/>
      <plain-model-parameter internal-id="1000295" dataset="spec:regular" special-level="gnd-surf" edr-id="percent-frozen-precipitation_gnd-surf" label="Percent frozen precipitation - Ground surface" unit="N_PERC"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf" label="Categorical rain (yes=1; no=0) - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf" label="Categorical freezing rain (yes=1; no=0) - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf" label="Categorical ice pellets (yes=1; no=0) - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf" label="Categorical snow (yes=1; no=0) - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" special-level="max-wind" edr-id="u-component-of-wind_max-wind" label="U component of wind - Max. wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" special-level="tropopause" edr-id="u-component-of-wind_tropopause" label="U component of wind - Tropopause" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000514" dataset="spec:regular" special-level="PBL" edr-id="u-component-of-wind_PBL" label="U component of wind - Planetary boundary layer" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" special-level="max-wind" edr-id="v-component-of-wind_max-wind" label="V component of wind - Max. wind" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" special-level="tropopause" edr-id="v-component-of-wind_tropopause" label="V component of wind - Tropopause" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000515" dataset="spec:regular" special-level="PBL" edr-id="v-component-of-wind_PBL" label="V component of wind - Planetary boundary layer" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000534" dataset="spec:regular" special-level="gnd-surf" edr-id="wind-speed-gust_gnd-surf" label="Wind speed (gust) - Ground surface" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000704" dataset="spec:regular" special-level="tropopause" edr-id="vertical-speed-shear_tropopause" label="Vertical speed shear - Tropopause"/>
      <plain-model-parameter internal-id="1000709" dataset="spec:regular" special-level="gnd-surf" edr-id="frictional-velocity_gnd-surf" label="Frictional velocity - Ground surface" unit="S_MPS"/>
      <plain-model-parameter internal-id="1000736" dataset="spec:regular" special-level="PBL" edr-id="ventilation-rate_PBL" label="Ventilation rate - Planetary boundary layer"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" special-level="gnd-surf" edr-id="pressure_gnd-surf" label="Pressure - Ground surface" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" special-level="max-wind" edr-id="pressure_max-wind" label="Pressure - Max. wind" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" special-level="tropopause" edr-id="pressure_tropopause" label="Pressure - Tropopause" unit="P_PA"/>
      <plain-model-parameter internal-id="1000769" dataset="spec:regular" special-level="mean-sea" edr-id="pressure-reduced-to-msl_mean-sea" label="Pressure reduced to MSL - Mean sea" unit="P_PA"/>
      <plain-model-parameter internal-id="1000771" dataset="spec:regular" special-level="max-wind" edr-id="icao-standard-atmosphere-reference-height_max-wind" label="ICAO Standard Atmosphere reference height - Max. wind" unit="D_M"/>
      <plain-model-parameter internal-id="1000771" dataset="spec:regular" special-level="tropopause" edr-id="icao-standard-atmosphere-reference-height_tropopause" label="ICAO Standard Atmosphere reference height - Tropopause" unit="D_M"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="gnd-surf" edr-id="geopotential-height_gnd-surf" label="Geopotential height - Ground surface" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="0-isoterm" edr-id="geopotential-height_0-isoterm" label="Geopotential height - 0C isoterm" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="max-wind" edr-id="geopotential-height_max-wind" label="Geopotential height - Max. wind" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="tropopause" edr-id="geopotential-height_tropopause" label="Geopotential height - Tropopause" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="tropo-freeze" edr-id="geopotential-height_tropo-freeze" label="Geopotential height - Highest tropospheric freezing level" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000773" dataset="spec:regular" special-level="cloud-ceiling" edr-id="geopotential-height_cloud-ceiling" label="Geopotential height - Cloud ceiling" unit="D_GPM"/>
      <plain-model-parameter internal-id="1000960" dataset="spec:regular" special-level="mean-sea" edr-id="msl-pressure-eta-reduction_mean-sea" label="MSL pressure (Eta reduction) - Mean sea" unit="P_PA"/>
      <plain-model-parameter internal-id="1000964" dataset="spec:regular" special-level="gnd-surf" edr-id="planetary-boundary-layer-height_gnd-surf" label="Planetary boundary layer height - Ground surface" unit="D_M"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere" label="Total cloud cover - Atmosphere" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud" label="Low cloud cover - Low cloud layer" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud" label="Medium cloud cover - Middle cloud layer" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular" special-level="high-cld" edr-id="high-cloud-cover_high-cld" label="High cloud cover - High cloud layer" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001542" dataset="spec:regular" special-level="atmosphere" edr-id="cloud-water_atmosphere" label="Cloud water - Atmosphere"/>
      <plain-model-parameter internal-id="1001737" dataset="spec:regular" special-level="gnd-surf" edr-id="sunshine-duration_gnd-surf" label="Sunshine duration - Ground surface" unit="T_SEC"/>
      <plain-model-parameter internal-id="1001798" dataset="spec:regular" special-level="gnd-surf" edr-id="convective-available-potential-energy_gnd-surf" label="Convective available potential energy - Ground surface"/>
      <plain-model-parameter internal-id="1001799" dataset="spec:regular" special-level="gnd-surf" edr-id="convective-inhibition_gnd-surf" label="Convective inhibition - Ground surface"/>
      <plain-model-parameter internal-id="1001984" dataset="spec:regular" special-level="gnd-surf" edr-id="surface-lifted-index_gnd-surf" label="Surface lifted index - Ground surface" unit="T_KELV"/>
      <plain-model-parameter internal-id="1001985" dataset="spec:regular" special-level="gnd-surf" edr-id="best-4-layer-lifted-index_gnd-surf" label="Best (4 layer) lifted index - Ground surface" unit="T_KELV"/>
      <plain-model-parameter internal-id="1003584" dataset="spec:regular" special-level="atmosphere" edr-id="total-ozone_atmosphere" label="Total ozone - Atmosphere"/>
      <plain-model-parameter internal-id="1004292" dataset="spec:regular" special-level="atmosphere" edr-id="composite-reflectivity_atmosphere" label="Composite reflectivity - Atmosphere"/>
      <plain-model-parameter internal-id="1004864" dataset="spec:regular" special-level="gnd-surf" edr-id="visibility_gnd-surf" label="Visibility - Ground surface" unit="D_M"/>
      <plain-model-parameter internal-id="1131072" dataset="spec:regular" special-level="gnd-surf" edr-id="land-cover_gnd-surf" label="Land cover - Ground surface"/>
      <plain-model-parameter internal-id="1131073" dataset="spec:regular" special-level="gnd-surf" edr-id="surface-roughness_gnd-surf" label="Surface roughness - Ground surface" unit="D_M"/>
      <plain-model-parameter internal-id="1131076" dataset="spec:regular" special-level="gnd-surf" edr-id="vegetation_gnd-surf" label="Vegetation - Ground surface"/>
      <plain-model-parameter internal-id="1131268" dataset="spec:regular" special-level="gnd-surf" edr-id="plant-canopy-surface-water_gnd-surf" label="Plant canopy surface water - Ground surface"/>
      <plain-model-parameter internal-id="1131273" dataset="spec:regular" special-level="gnd-surf" edr-id="wilting-point_gnd-surf" label="Wilting point - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1131840" dataset="spec:regular" special-level="gnd-surf" edr-id="soil-type_gnd-surf" label="Soil type - Ground surface"/>
      <plain-model-parameter internal-id="1132043" dataset="spec:regular" special-level="gnd-surf" edr-id="field-capacity_gnd-surf" label="Field capacity - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1132098" dataset="spec:regular" special-level="gnd-surf" edr-id="haines-index_gnd-surf" label="Haines index - Ground surface" unit="N_NUM"/>
      <plain-model-parameter internal-id="1655872" dataset="spec:regular" special-level="gnd-surf" edr-id="ice-cover_gnd-surf" label="Ice cover - Ground surface"/>
      <plain-model-parameter internal-id="1655873" dataset="spec:regular" special-level="gnd-surf" edr-id="ice-thickness_gnd-surf" label="Ice thickness - Ground surface" unit="D_M"/>
      <plain-model-parameter internal-id="1655880" dataset="spec:regular" special-level="gnd-surf" edr-id="ice-temperature_gnd-surf" label="Ice temperature - Ground surface" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_2" title="KGFS - Single Level (2)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT1H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT1H" label="Temperature - Low cloud top level - Average 1h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT1H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT1H" label="Temperature - Middle cloud top level - Average 1h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT1H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT1H" label="Temperature - High cloud top level - Average 1h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT1H" label="Latent heat net flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT1H" label="Sensible heat net flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT1H" label="Precipitation rate - Ground surface - Average 1h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT1H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT1H" label="Total precipitation - Ground surface - Accumulation 1h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT1H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT1H" label="Convective precipitation - Ground surface - Accumulation 1h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT1H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 1h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT1H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 1h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT1H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 1h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT1H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 1h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT1H" label="Convective precipitation rate - Ground surface - Average 1h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT1H" label="Momentum flux, U component - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT1H" label="Momentum flux, V component - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT1H" label="Pressure - Low cloud bottom level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT1H" label="Pressure - Low cloud top level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT1H" label="Pressure - Middle cloud bottom level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT1H" label="Pressure - Middle cloud top level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT1H" label="Pressure - High cloud bottom level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT1H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT1H" label="Pressure - High cloud top level - Average 1h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT1H" label="Zonal flux of gravity wave stress - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT1H" label="Meridional flux of gravity wave stress - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT1H" label="Downward short-wave radiation flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT1H" label="Upward short-wave radiation flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT1H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT1H" label="Upward short-wave radiation flux - Atmosphere top - Average 1h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT1H" label="Downward long-wave radiation flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT1H" label="Upward long-wave radiation flux - Ground surface - Average 1h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT1H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT1H" label="Upward long-wave radiation flux - Atmosphere top - Average 1h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT1H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT1H" label="Total cloud cover - Atmosphere - Average 1h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT1H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT1H" label="Total cloud cover - Boundary layer cloud layer - Average 1h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT1H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT1H" label="Low cloud cover - Low cloud layer - Average 1h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT1H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT1H" label="Medium cloud cover - Middle cloud layer - Average 1h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT1H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT1H" label="High cloud cover - High cloud layer - Average 1h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT1H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT1H" label="Cloud work function - Atmosphere - Average 1h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT1H" label="Albedo - Ground surface - Average 1h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT1H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT1H" label="Water runoff - Ground surface - Accumulation 1h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT1H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT1H" label="Ground heat flux - Ground surface - Average 1h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_3" title="KGFS - Single Level (3)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT2H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT2H" label="Temperature - Low cloud top level - Average 2h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT2H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT2H" label="Temperature - Middle cloud top level - Average 2h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT2H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT2H" label="Temperature - High cloud top level - Average 2h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT2H" label="Latent heat net flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT2H" label="Sensible heat net flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT2H" label="Precipitation rate - Ground surface - Average 2h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT2H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT2H" label="Total precipitation - Ground surface - Accumulation 2h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT2H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT2H" label="Convective precipitation - Ground surface - Accumulation 2h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT2H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 2h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT2H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 2h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT2H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 2h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT2H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 2h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT2H" label="Convective precipitation rate - Ground surface - Average 2h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT2H" label="Momentum flux, U component - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT2H" label="Momentum flux, V component - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT2H" label="Pressure - Low cloud bottom level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT2H" label="Pressure - Low cloud top level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT2H" label="Pressure - Middle cloud bottom level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT2H" label="Pressure - Middle cloud top level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT2H" label="Pressure - High cloud bottom level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT2H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT2H" label="Pressure - High cloud top level - Average 2h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT2H" label="Zonal flux of gravity wave stress - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT2H" label="Meridional flux of gravity wave stress - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT2H" label="Downward short-wave radiation flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT2H" label="Upward short-wave radiation flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT2H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT2H" label="Upward short-wave radiation flux - Atmosphere top - Average 2h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT2H" label="Downward long-wave radiation flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT2H" label="Upward long-wave radiation flux - Ground surface - Average 2h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT2H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT2H" label="Upward long-wave radiation flux - Atmosphere top - Average 2h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT2H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT2H" label="Total cloud cover - Atmosphere - Average 2h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT2H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT2H" label="Total cloud cover - Boundary layer cloud layer - Average 2h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT2H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT2H" label="Low cloud cover - Low cloud layer - Average 2h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT2H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT2H" label="Medium cloud cover - Middle cloud layer - Average 2h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT2H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT2H" label="High cloud cover - High cloud layer - Average 2h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT2H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT2H" label="Cloud work function - Atmosphere - Average 2h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT2H" label="Albedo - Ground surface - Average 2h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT2H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT2H" label="Water runoff - Ground surface - Accumulation 2h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT2H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT2H" label="Ground heat flux - Ground surface - Average 2h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_4" title="KGFS - Single Level (4)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT3H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT3H" label="Temperature - Low cloud top level - Average 3h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT3H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT3H" label="Temperature - Middle cloud top level - Average 3h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT3H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT3H" label="Temperature - High cloud top level - Average 3h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT3H" label="Latent heat net flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT3H" label="Sensible heat net flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT3H" label="Precipitation rate - Ground surface - Average 3h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT3H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT3H" label="Total precipitation - Ground surface - Accumulation 3h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT3H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT3H" label="Convective precipitation - Ground surface - Accumulation 3h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT3H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 3h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT3H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 3h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT3H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 3h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT3H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 3h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT3H" label="Convective precipitation rate - Ground surface - Average 3h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT3H" label="Momentum flux, U component - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT3H" label="Momentum flux, V component - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT3H" label="Pressure - Low cloud bottom level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT3H" label="Pressure - Low cloud top level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT3H" label="Pressure - Middle cloud bottom level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT3H" label="Pressure - Middle cloud top level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT3H" label="Pressure - High cloud bottom level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT3H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT3H" label="Pressure - High cloud top level - Average 3h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT3H" label="Zonal flux of gravity wave stress - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT3H" label="Meridional flux of gravity wave stress - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT3H" label="Downward short-wave radiation flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT3H" label="Upward short-wave radiation flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT3H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT3H" label="Upward short-wave radiation flux - Atmosphere top - Average 3h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT3H" label="Downward long-wave radiation flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT3H" label="Upward long-wave radiation flux - Ground surface - Average 3h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT3H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT3H" label="Upward long-wave radiation flux - Atmosphere top - Average 3h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT3H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT3H" label="Total cloud cover - Atmosphere - Average 3h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT3H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT3H" label="Total cloud cover - Boundary layer cloud layer - Average 3h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT3H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT3H" label="Low cloud cover - Low cloud layer - Average 3h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT3H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT3H" label="Medium cloud cover - Middle cloud layer - Average 3h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT3H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT3H" label="High cloud cover - High cloud layer - Average 3h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT3H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT3H" label="Cloud work function - Atmosphere - Average 3h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT3H" label="Albedo - Ground surface - Average 3h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT3H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT3H" label="Water runoff - Ground surface - Accumulation 3h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT3H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT3H" label="Ground heat flux - Ground surface - Average 3h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_5" title="KGFS - Single Level (5)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT4H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT4H" label="Temperature - Low cloud top level - Average 4h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT4H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT4H" label="Temperature - Middle cloud top level - Average 4h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT4H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT4H" label="Temperature - High cloud top level - Average 4h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT4H" label="Latent heat net flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT4H" label="Sensible heat net flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT4H" label="Precipitation rate - Ground surface - Average 4h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT4H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT4H" label="Total precipitation - Ground surface - Accumulation 4h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT4H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT4H" label="Convective precipitation - Ground surface - Accumulation 4h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT4H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 4h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT4H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 4h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT4H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 4h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT4H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 4h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT4H" label="Convective precipitation rate - Ground surface - Average 4h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT4H" label="Momentum flux, U component - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT4H" label="Momentum flux, V component - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT4H" label="Pressure - Low cloud bottom level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT4H" label="Pressure - Low cloud top level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT4H" label="Pressure - Middle cloud bottom level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT4H" label="Pressure - Middle cloud top level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT4H" label="Pressure - High cloud bottom level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT4H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT4H" label="Pressure - High cloud top level - Average 4h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT4H" label="Zonal flux of gravity wave stress - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT4H" label="Meridional flux of gravity wave stress - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT4H" label="Downward short-wave radiation flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT4H" label="Upward short-wave radiation flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT4H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT4H" label="Upward short-wave radiation flux - Atmosphere top - Average 4h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT4H" label="Downward long-wave radiation flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT4H" label="Upward long-wave radiation flux - Ground surface - Average 4h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT4H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT4H" label="Upward long-wave radiation flux - Atmosphere top - Average 4h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT4H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT4H" label="Total cloud cover - Atmosphere - Average 4h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT4H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT4H" label="Total cloud cover - Boundary layer cloud layer - Average 4h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT4H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT4H" label="Low cloud cover - Low cloud layer - Average 4h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT4H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT4H" label="Medium cloud cover - Middle cloud layer - Average 4h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT4H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT4H" label="High cloud cover - High cloud layer - Average 4h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT4H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT4H" label="Cloud work function - Atmosphere - Average 4h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT4H" label="Albedo - Ground surface - Average 4h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT4H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT4H" label="Water runoff - Ground surface - Accumulation 4h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT4H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT4H" label="Ground heat flux - Ground surface - Average 4h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_6" title="KGFS - Single Level (6)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT5H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT5H" label="Temperature - Low cloud top level - Average 5h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT5H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT5H" label="Temperature - Middle cloud top level - Average 5h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT5H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT5H" label="Temperature - High cloud top level - Average 5h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT5H" label="Latent heat net flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT5H" label="Sensible heat net flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT5H" label="Precipitation rate - Ground surface - Average 5h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT5H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT5H" label="Total precipitation - Ground surface - Accumulation 5h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT5H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT5H" label="Convective precipitation - Ground surface - Accumulation 5h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT5H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 5h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT5H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 5h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT5H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 5h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT5H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 5h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT5H" label="Convective precipitation rate - Ground surface - Average 5h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT5H" label="Momentum flux, U component - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT5H" label="Momentum flux, V component - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT5H" label="Pressure - Low cloud bottom level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT5H" label="Pressure - Low cloud top level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT5H" label="Pressure - Middle cloud bottom level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT5H" label="Pressure - Middle cloud top level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT5H" label="Pressure - High cloud bottom level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT5H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT5H" label="Pressure - High cloud top level - Average 5h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT5H" label="Zonal flux of gravity wave stress - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT5H" label="Meridional flux of gravity wave stress - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT5H" label="Downward short-wave radiation flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT5H" label="Upward short-wave radiation flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT5H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT5H" label="Upward short-wave radiation flux - Atmosphere top - Average 5h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT5H" label="Downward long-wave radiation flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT5H" label="Upward long-wave radiation flux - Ground surface - Average 5h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT5H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT5H" label="Upward long-wave radiation flux - Atmosphere top - Average 5h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT5H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT5H" label="Total cloud cover - Atmosphere - Average 5h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT5H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT5H" label="Total cloud cover - Boundary layer cloud layer - Average 5h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT5H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT5H" label="Low cloud cover - Low cloud layer - Average 5h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT5H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT5H" label="Medium cloud cover - Middle cloud layer - Average 5h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT5H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT5H" label="High cloud cover - High cloud layer - Average 5h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT5H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT5H" label="Cloud work function - Atmosphere - Average 5h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT5H" label="Albedo - Ground surface - Average 5h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT5H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT5H" label="Water runoff - Ground surface - Accumulation 5h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT5H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT5H" label="Ground heat flux - Ground surface - Average 5h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_7" title="KGFS - Single Level (7)" profile="full">
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT6H" special-level="low-cld-top" edr-id="temperature_low-cld-top_stat:avg/PT6H" label="Temperature - Low cloud top level - Average 6h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT6H" special-level="mid-cld-top" edr-id="temperature_mid-cld-top_stat:avg/PT6H" label="Temperature - Middle cloud top level - Average 6h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000000" dataset="spec:regular;stat:average/PT6H" special-level="high-cld-top" edr-id="temperature_high-cld-top_stat:avg/PT6H" label="Temperature - High cloud top level - Average 6h" unit="T_KELV"/>
      <plain-model-parameter internal-id="1000010" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_stat:avg/PT6H" label="Latent heat net flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1000011" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_stat:avg/PT6H" label="Sensible heat net flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1000263" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="precipitation-rate_gnd-surf_stat:avg/PT6H" label="Precipitation rate - Ground surface - Average 6h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT6H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT6H" label="Total precipitation - Ground surface - Accumulation 6h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT6H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT6H" label="Convective precipitation - Ground surface - Accumulation 6h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000448" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="categorical-rain-yes-1-no-0_gnd-surf_stat:avg/PT6H" label="Categorical rain (yes=1; no=0) - Ground surface - Average 6h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000449" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="categorical-freezing-rain-yes-1-no-0_gnd-surf_stat:avg/PT6H" label="Categorical freezing rain (yes=1; no=0) - Ground surface - Average 6h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000450" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="categorical-ice-pellets-yes-1-no-0_gnd-surf_stat:avg/PT6H" label="Categorical ice pellets (yes=1; no=0) - Ground surface - Average 6h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000451" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="categorical-snow-yes-1-no-0_gnd-surf_stat:avg/PT6H" label="Categorical snow (yes=1; no=0) - Ground surface - Average 6h" unit="N_NUM"/>
      <plain-model-parameter internal-id="1000452" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf_stat:avg/PT6H" label="Convective precipitation rate - Ground surface - Average 6h" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000529" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="momentum-flux-u-component_gnd-surf_stat:avg/PT6H" label="Momentum flux, U component - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1000530" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="momentum-flux-v-component_gnd-surf_stat:avg/PT6H" label="Momentum flux, V component - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="low-cld-bottom" edr-id="pressure_low-cld-bottom_stat:avg/PT6H" label="Pressure - Low cloud bottom level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="low-cld-top" edr-id="pressure_low-cld-top_stat:avg/PT6H" label="Pressure - Low cloud top level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="mid-cld-bottom" edr-id="pressure_mid-cld-bottom_stat:avg/PT6H" label="Pressure - Middle cloud bottom level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="mid-cld-top" edr-id="pressure_mid-cld-top_stat:avg/PT6H" label="Pressure - Middle cloud top level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="high-cld-bottom" edr-id="pressure_high-cld-bottom_stat:avg/PT6H" label="Pressure - High cloud bottom level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular;stat:average/PT6H" special-level="high-cld-top" edr-id="pressure_high-cld-top_stat:avg/PT6H" label="Pressure - High cloud top level - Average 6h" unit="P_PA"/>
      <plain-model-parameter internal-id="1000962" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="zonal-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT6H" label="Zonal flux of gravity wave stress - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1000963" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="meridional-flux-of-gravity-wave-stress_gnd-surf_stat:avg/PT6H" label="Meridional flux of gravity wave stress - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1001216" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="downward-short-wave-radiation-flux_gnd-surf_stat:avg/PT6H" label="Downward short-wave radiation flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="upward-short-wave-radiation-flux_gnd-surf_stat:avg/PT6H" label="Upward short-wave radiation flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1001217" dataset="spec:regular;stat:average/PT6H" special-level="atmosphere-top" edr-id="upward-short-wave-radiation-flux_atmosphere-top_stat:avg/PT6H" label="Upward short-wave radiation flux - Atmosphere top - Average 6h"/>
      <plain-model-parameter internal-id="1001472" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="downward-long-wave-radiation-flux_gnd-surf_stat:avg/PT6H" label="Downward long-wave radiation flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="upward-long-wave-radiation-flux_gnd-surf_stat:avg/PT6H" label="Upward long-wave radiation flux - Ground surface - Average 6h"/>
      <plain-model-parameter internal-id="1001473" dataset="spec:regular;stat:average/PT6H" special-level="atmosphere-top" edr-id="upward-long-wave-radiation-flux_atmosphere-top_stat:avg/PT6H" label="Upward long-wave radiation flux - Atmosphere top - Average 6h"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT6H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_stat:avg/PT6H" label="Total cloud cover - Atmosphere - Average 6h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular;stat:average/PT6H" special-level="bound-cloud" edr-id="total-cloud-cover_bound-cloud_stat:avg/PT6H" label="Total cloud cover - Boundary layer cloud layer - Average 6h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001539" dataset="spec:regular;stat:average/PT6H" special-level="low-cloud" edr-id="low-cloud-cover_low-cloud_stat:avg/PT6H" label="Low cloud cover - Low cloud layer - Average 6h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001540" dataset="spec:regular;stat:average/PT6H" special-level="mid-cloud" edr-id="medium-cloud-cover_mid-cloud_stat:avg/PT6H" label="Medium cloud cover - Middle cloud layer - Average 6h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001541" dataset="spec:regular;stat:average/PT6H" special-level="high-cld" edr-id="high-cloud-cover_high-cld_stat:avg/PT6H" label="High cloud cover - High cloud layer - Average 6h" unit="C_PERC"/>
      <plain-model-parameter internal-id="1001729" dataset="spec:regular;stat:average/PT6H" special-level="atmosphere" edr-id="cloud-work-function_atmosphere_stat:avg/PT6H" label="Cloud work function - Atmosphere - Average 6h"/>
      <plain-model-parameter internal-id="1004865" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="albedo_gnd-surf_stat:avg/PT6H" label="Albedo - Ground surface - Average 6h" unit="N_PERC"/>
      <plain-model-parameter internal-id="1131077" dataset="spec:regular;stat:accumulation/PT6H" special-level="gnd-surf" edr-id="water-runoff_gnd-surf_stat:acc/PT6H" label="Water runoff - Ground surface - Accumulation 6h"/>
      <plain-model-parameter internal-id="1131265" dataset="spec:regular;stat:average/PT6H" special-level="gnd-surf" edr-id="ground-heat-flux_gnd-surf_stat:avg/PT6H" label="Ground heat flux - Ground surface - Average 6h"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_8" title="KGFS - Single Level (8)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT7H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT7H" label="Total precipitation - Ground surface - Accumulation 7h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT7H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT7H" label="Convective precipitation - Ground surface - Accumulation 7h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_9" title="KGFS - Single Level (9)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT8H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT8H" label="Total precipitation - Ground surface - Accumulation 8h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT8H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT8H" label="Convective precipitation - Ground surface - Accumulation 8h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_10" title="KGFS - Single Level (10)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT9H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT9H" label="Total precipitation - Ground surface - Accumulation 9h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT9H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT9H" label="Convective precipitation - Ground surface - Accumulation 9h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_11" title="KGFS - Single Level (11)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT10H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT10H" label="Total precipitation - Ground surface - Accumulation 10h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT10H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT10H" label="Convective precipitation - Ground surface - Accumulation 10h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_12" title="KGFS - Single Level (12)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT11H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT11H" label="Total precipitation - Ground surface - Accumulation 11h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT11H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT11H" label="Convective precipitation - Ground surface - Accumulation 11h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_13" title="KGFS - Single Level (13)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT12H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT12H" label="Total precipitation - Ground surface - Accumulation 12h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT12H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT12H" label="Convective precipitation - Ground surface - Accumulation 12h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_14" title="KGFS - Single Level (14)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT13H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT13H" label="Total precipitation - Ground surface - Accumulation 13h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT13H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT13H" label="Convective precipitation - Ground surface - Accumulation 13h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_15" title="KGFS - Single Level (15)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT14H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT14H" label="Total precipitation - Ground surface - Accumulation 14h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT14H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT14H" label="Convective precipitation - Ground surface - Accumulation 14h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_16" title="KGFS - Single Level (16)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT15H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT15H" label="Total precipitation - Ground surface - Accumulation 15h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT15H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT15H" label="Convective precipitation - Ground surface - Accumulation 15h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_17" title="KGFS - Single Level (17)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT16H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT16H" label="Total precipitation - Ground surface - Accumulation 16h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT16H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT16H" label="Convective precipitation - Ground surface - Accumulation 16h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_18" title="KGFS - Single Level (18)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT17H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT17H" label="Total precipitation - Ground surface - Accumulation 17h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT17H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT17H" label="Convective precipitation - Ground surface - Accumulation 17h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_19" title="KGFS - Single Level (19)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT18H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT18H" label="Total precipitation - Ground surface - Accumulation 18h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT18H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT18H" label="Convective precipitation - Ground surface - Accumulation 18h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_20" title="KGFS - Single Level (20)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT19H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT19H" label="Total precipitation - Ground surface - Accumulation 19h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT19H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT19H" label="Convective precipitation - Ground surface - Accumulation 19h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_21" title="KGFS - Single Level (21)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT20H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT20H" label="Total precipitation - Ground surface - Accumulation 20h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT20H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT20H" label="Convective precipitation - Ground surface - Accumulation 20h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_22" title="KGFS - Single Level (22)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT21H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT21H" label="Total precipitation - Ground surface - Accumulation 21h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT21H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT21H" label="Convective precipitation - Ground surface - Accumulation 21h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_23" title="KGFS - Single Level (23)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT22H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT22H" label="Total precipitation - Ground surface - Accumulation 22h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT22H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT22H" label="Convective precipitation - Ground surface - Accumulation 22h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_24" title="KGFS - Single Level (24)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/PT23H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/PT23H" label="Total precipitation - Ground surface - Accumulation 23h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/PT23H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/PT23H" label="Convective precipitation - Ground surface - Accumulation 23h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_25" title="KGFS - Single Level (25)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1D" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1D" label="Total precipitation - Ground surface - Accumulation 24h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1D" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1D" label="Convective precipitation - Ground surface - Accumulation 24h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_26" title="KGFS - Single Level (26)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT1H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT1H" label="Total precipitation - Ground surface - Accumulation 25h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT1H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT1H" label="Convective precipitation - Ground surface - Accumulation 25h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_27" title="KGFS - Single Level (27)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT2H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT2H" label="Total precipitation - Ground surface - Accumulation 26h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT2H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT2H" label="Convective precipitation - Ground surface - Accumulation 26h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_28" title="KGFS - Single Level (28)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT3H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT3H" label="Total precipitation - Ground surface - Accumulation 27h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT3H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT3H" label="Convective precipitation - Ground surface - Accumulation 27h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_29" title="KGFS - Single Level (29)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT4H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT4H" label="Total precipitation - Ground surface - Accumulation 28h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT4H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT4H" label="Convective precipitation - Ground surface - Accumulation 28h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_30" title="KGFS - Single Level (30)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT5H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT5H" label="Total precipitation - Ground surface - Accumulation 29h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT5H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT5H" label="Convective precipitation - Ground surface - Accumulation 29h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_31" title="KGFS - Single Level (31)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT6H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT6H" label="Total precipitation - Ground surface - Accumulation 30h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT6H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT6H" label="Convective precipitation - Ground surface - Accumulation 30h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_32" title="KGFS - Single Level (32)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT7H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT7H" label="Total precipitation - Ground surface - Accumulation 31h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT7H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT7H" label="Convective precipitation - Ground surface - Accumulation 31h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_33" title="KGFS - Single Level (33)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT8H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT8H" label="Total precipitation - Ground surface - Accumulation 32h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT8H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT8H" label="Convective precipitation - Ground surface - Accumulation 32h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_34" title="KGFS - Single Level (34)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT9H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT9H" label="Total precipitation - Ground surface - Accumulation 33h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT9H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT9H" label="Convective precipitation - Ground surface - Accumulation 33h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_35" title="KGFS - Single Level (35)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT10H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT10H" label="Total precipitation - Ground surface - Accumulation 34h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT10H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT10H" label="Convective precipitation - Ground surface - Accumulation 34h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_36" title="KGFS - Single Level (36)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT11H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT11H" label="Total precipitation - Ground surface - Accumulation 35h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT11H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT11H" label="Convective precipitation - Ground surface - Accumulation 35h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_37" title="KGFS - Single Level (37)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT12H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT12H" label="Total precipitation - Ground surface - Accumulation 36h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT12H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT12H" label="Convective precipitation - Ground surface - Accumulation 36h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_38" title="KGFS - Single Level (38)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT13H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT13H" label="Total precipitation - Ground surface - Accumulation 37h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT13H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT13H" label="Convective precipitation - Ground surface - Accumulation 37h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_39" title="KGFS - Single Level (39)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT14H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT14H" label="Total precipitation - Ground surface - Accumulation 38h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT14H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT14H" label="Convective precipitation - Ground surface - Accumulation 38h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_40" title="KGFS - Single Level (40)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT15H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT15H" label="Total precipitation - Ground surface - Accumulation 39h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT15H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT15H" label="Convective precipitation - Ground surface - Accumulation 39h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_41" title="KGFS - Single Level (41)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT16H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT16H" label="Total precipitation - Ground surface - Accumulation 40h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT16H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT16H" label="Convective precipitation - Ground surface - Accumulation 40h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_42" title="KGFS - Single Level (42)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT17H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT17H" label="Total precipitation - Ground surface - Accumulation 41h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT17H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT17H" label="Convective precipitation - Ground surface - Accumulation 41h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_43" title="KGFS - Single Level (43)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT18H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT18H" label="Total precipitation - Ground surface - Accumulation 42h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT18H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT18H" label="Convective precipitation - Ground surface - Accumulation 42h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_44" title="KGFS - Single Level (44)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT19H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT19H" label="Total precipitation - Ground surface - Accumulation 43h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT19H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT19H" label="Convective precipitation - Ground surface - Accumulation 43h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_45" title="KGFS - Single Level (45)" profile="full">
      <plain-model-parameter internal-id="1000264" dataset="spec:regular;stat:accumulation/P1DT20H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_stat:acc/P1DT20H" label="Total precipitation - Ground surface - Accumulation 44h" unit="P_KG_M2"/>
      <plain-model-parameter internal-id="1000266" dataset="spec:regular;stat:accumulation/P1DT20H" special-level="gnd-surf" edr-id="convective-precipitation_gnd-surf_stat:acc/P1DT20H" label="Convective precipitation - Ground surface - Accumulation 44h" unit="P_KG_M2"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_46" title="KGFS - Single Level (46)" profile="full">
      <plain-model-parameter internal-id="1000293" dataset="spec:regular" special-level="gnd-surf" edr-id="convective-precipitation-rate_gnd-surf" label="Convective precipitation rate - Ground surface" unit="P_KG_M2_S"/>
      <plain-model-parameter internal-id="1000456" dataset="spec:regular" special-level="gnd-surf" edr-id="potential-evaporation-rate_gnd-surf" label="Potential evaporation rate - Ground surface"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" special-level="conv-cld-bottom" edr-id="pressure_conv-cld-bottom" label="Pressure - Convective cloud bottom level" unit="P_PA"/>
      <plain-model-parameter internal-id="1000768" dataset="spec:regular" special-level="conv-cld-top" edr-id="pressure_conv-cld-top" label="Pressure - Convective cloud top level" unit="P_PA"/>
      <plain-model-parameter internal-id="1001537" dataset="spec:regular" special-level="conv-cld" edr-id="total-cloud-cover_conv-cld" label="Total cloud cover - Convective cloud layer" unit="C_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" edr-id="GFS_single-level_47" title="KGFS - Single Level (47)" profile="full">
      <plain-model-parameter internal-id="1245813" dataset="spec:regular" special-level="atmosphere-top" edr-id="simulated-brightness-temperature-for-nadir-abi-goes-r-band-8_atmosphere-top" label="Simulated brightness temperature for nadir ABI GOES-R, band-8 - Atmosphere top"/>
      <plain-model-parameter internal-id="1245814" dataset="spec:regular" special-level="atmosphere-top" edr-id="simulated-brightness-temperature-for-nadir-abi-goes-r-band-9_atmosphere-top" label="Simulated brightness temperature for nadir ABI GOES-R, band-9 - Atmosphere top"/>
      <plain-model-parameter internal-id="1245815" dataset="spec:regular" special-level="atmosphere-top" edr-id="simulated-brightness-temperature-for-nadir-abi-goes-r-band-10_atmosphere-top" label="Simulated brightness temperature for nadir ABI GOES-R, band-10 - Atmosphere top"/>
      <plain-model-parameter internal-id="1245818" dataset="spec:regular" special-level="atmosphere-top" edr-id="simulated-brightness-temperature-for-nadir-abi-goes-r-band-13_atmosphere-top" label="Simulated brightness temperature for nadir ABI GOES-R, band-13 - Atmosphere top"/>
    </model-collection>

    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="between-depth" edr-id="GEFS_between-depth" title="GEFS - Layer between two depths below land surface" profile="full">
      <ensemble-model-parameter internal-id="1131074" member-type="positively-perturbed" total-member-count="30" edr-id="soil-temperature_positively-perturbed" label="Soil temperature - Positively perturbed" unit="T_KELV"/>
      <ensemble-model-parameter internal-id="1131264" member-type="positively-perturbed" total-member-count="30" edr-id="1131264_positively-perturbed" label="? - Positively perturbed"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="between-pressure-diff" edr-id="GEFS_between-pressure-diff" title="GEFS - Layer between two levels at specified pressure differences from ground to level" profile="full">
      <ensemble-model-parameter internal-id="1001798" member-type="positively-perturbed" total-member-count="30" edr-id="convective-available-potential-energy_positively-perturbed" label="Convective available potential energy - Positively perturbed"/>
      <ensemble-model-parameter internal-id="1001799" member-type="positively-perturbed" total-member-count="30" edr-id="convective-inhibition_positively-perturbed" label="Convective inhibition - Positively perturbed"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="meters" edr-id="GEFS_height-above-ground" title="GEFS - Height above ground" profile="full">
      <ensemble-model-parameter internal-id="1000000" member-type="positively-perturbed" total-member-count="30" edr-id="temperature_positively-perturbed" label="Temperature - Positively perturbed" unit="T_KELV"/>
      <ensemble-model-parameter internal-id="1000257" member-type="positively-perturbed" total-member-count="30" edr-id="relative-humidity_positively-perturbed" label="Relative humidity - Positively perturbed" unit="N_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="meters" edr-id="GEFS_height-above-ground_2" title="GEFS - Height above ground (2)" profile="full">
      <ensemble-model-parameter internal-id="1000514" member-type="positively-perturbed" total-member-count="30" edr-id="u-component-of-wind_positively-perturbed" label="U component of wind - Positively perturbed" unit="S_MPS"/>
      <ensemble-model-parameter internal-id="1000515" member-type="positively-perturbed" total-member-count="30" edr-id="v-component-of-wind_positively-perturbed" label="V component of wind - Positively perturbed" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="meters" edr-id="GEFS_height-above-ground_3" title="GEFS - Height above ground (3)" profile="full">
      <ensemble-model-parameter internal-id="1000004" member-type="positively-perturbed" total-member-count="30" dataset="stat:maximum/PT3H" edr-id="maximum-temperature_positively-perturbed_stat:max/PT3H" label="Maximum temperature - Positively perturbed ; Maximum 3h" unit="T_KELV"/>
      <ensemble-model-parameter internal-id="1000005" member-type="positively-perturbed" total-member-count="30" dataset="stat:minimum/PT3H" edr-id="minimum-temperature_positively-perturbed_stat:min/PT3H" label="Minimum temperature - Positively perturbed ; Minimum 3h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="meters" edr-id="GEFS_height-above-ground_4" title="GEFS - Height above ground (4)" profile="full">
      <ensemble-model-parameter internal-id="1000004" member-type="positively-perturbed" total-member-count="30" dataset="stat:maximum/PT6H" edr-id="maximum-temperature_positively-perturbed_stat:max/PT6H" label="Maximum temperature - Positively perturbed ; Maximum 6h" unit="T_KELV"/>
      <ensemble-model-parameter internal-id="1000005" member-type="positively-perturbed" total-member-count="30" dataset="stat:minimum/PT6H" edr-id="minimum-temperature_positively-perturbed_stat:min/PT6H" label="Minimum temperature - Positively perturbed ; Minimum 6h" unit="T_KELV"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="isobaric" edr-id="GEFS_isobaric" title="GEFS - Isobaric level" profile="full">
      <ensemble-model-parameter internal-id="1000773" member-type="positively-perturbed" total-member-count="30" edr-id="geopotential-height_positively-perturbed" label="Geopotential height - Positively perturbed" unit="D_GPM"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="isobaric" edr-id="GEFS_isobaric_2" title="GEFS - Isobaric level (2)" profile="full">
      <ensemble-model-parameter internal-id="1000000" member-type="positively-perturbed" total-member-count="30" edr-id="temperature_positively-perturbed" label="Temperature - Positively perturbed" unit="T_KELV"/>
      <ensemble-model-parameter internal-id="1000257" member-type="positively-perturbed" total-member-count="30" edr-id="relative-humidity_positively-perturbed" label="Relative humidity - Positively perturbed" unit="N_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="isobaric" edr-id="GEFS_isobaric_3" title="GEFS - Isobaric level (3)" profile="full">
      <ensemble-model-parameter internal-id="1000514" member-type="positively-perturbed" total-member-count="30" edr-id="u-component-of-wind_positively-perturbed" label="U component of wind - Positively perturbed" unit="S_MPS"/>
      <ensemble-model-parameter internal-id="1000515" member-type="positively-perturbed" total-member-count="30" edr-id="v-component-of-wind_positively-perturbed" label="V component of wind - Positively perturbed" unit="S_MPS"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" level-type="isobaric" edr-id="GEFS_isobaric_4" title="GEFS - Isobaric level (4)" profile="full">
      <ensemble-model-parameter internal-id="1000520" member-type="positively-perturbed" total-member-count="30" edr-id="vertical-velocity-pressure_positively-perturbed" label="Vertical velocity (pressure) - Positively perturbed"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" edr-id="GEFS_single-level" title="GEFS - Single Level" profile="full">
      <ensemble-model-parameter internal-id="1000773" member-type="positively-perturbed" total-member-count="30" special-level="gnd-surf" edr-id="geopotential-height_gnd-surf_positively-perturbed" label="Geopotential height - Ground surface - Positively perturbed" unit="D_GPM"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" edr-id="GEFS_single-level_2" title="GEFS - Single Level (2)" profile="full">
      <ensemble-model-parameter internal-id="1000259" member-type="positively-perturbed" total-member-count="30" special-level="atmosphere" edr-id="precipitable-water_atmosphere_positively-perturbed" label="Precipitable water - Atmosphere - Positively perturbed" unit="P_KG_M2"/>
      <ensemble-model-parameter internal-id="1000267" member-type="positively-perturbed" total-member-count="30" special-level="gnd-surf" edr-id="snow-depth_gnd-surf_positively-perturbed" label="Snow depth - Ground surface - Positively perturbed" unit="D_M"/>
      <ensemble-model-parameter internal-id="1000269" member-type="positively-perturbed" total-member-count="30" special-level="gnd-surf" edr-id="water-equivalent-of-accumulated-snow-depth_gnd-surf_positively-perturbed" label="Water equivalent of accumulated snow depth - Ground surface - Positively perturbed"/>
      <ensemble-model-parameter internal-id="1000768" member-type="positively-perturbed" total-member-count="30" special-level="gnd-surf" edr-id="pressure_gnd-surf_positively-perturbed" label="Pressure - Ground surface - Positively perturbed" unit="P_PA"/>
      <ensemble-model-parameter internal-id="1000769" member-type="positively-perturbed" total-member-count="30" special-level="mean-sea" edr-id="pressure-reduced-to-msl_mean-sea_positively-perturbed" label="Pressure reduced to MSL - Mean sea - Positively perturbed" unit="P_PA"/>
      <ensemble-model-parameter internal-id="1655873" member-type="positively-perturbed" total-member-count="30" special-level="gnd-surf" edr-id="ice-thickness_gnd-surf_positively-perturbed" label="Ice thickness - Ground surface - Positively perturbed" unit="D_M"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" edr-id="GEFS_single-level_3" title="GEFS - Single Level (3)" profile="full">
      <ensemble-model-parameter internal-id="1000010" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_positively-perturbed_stat:avg/PT3H" label="Latent heat net flux - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1000011" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_positively-perturbed_stat:avg/PT3H" label="Sensible heat net flux - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1000264" member-type="positively-perturbed" total-member-count="30" dataset="stat:accumulation/PT3H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_positively-perturbed_stat:acc/PT3H" label="Total precipitation - Ground surface - Positively perturbed ; Accumulation 3h" unit="P_KG_M2"/>
      <ensemble-model-parameter internal-id="1000448" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1000448_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1000449" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1000449_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1000450" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1000450_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1000451" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1000451_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001216" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1001216_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001217" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1001217_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001472" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1001472_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001473" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="gnd-surf" edr-id="1001473_gnd-surf_positively-perturbed_stat:avg/PT3H" label="? - Ground surface - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001473" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="atmosphere-top" edr-id="1001473_atmosphere-top_positively-perturbed_stat:avg/PT3H" label="? - Atmosphere top - Positively perturbed ; Average 3h"/>
      <ensemble-model-parameter internal-id="1001537" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT3H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_positively-perturbed_stat:avg/PT3H" label="Total cloud cover - Atmosphere - Positively perturbed ; Average 3h" unit="C_PERC"/>
    </model-collection>
    <model-collection model-id="sd:NOAA-GEFS-Ensemble" edr-id="GEFS_single-level_4" title="GEFS - Single Level (4)" profile="full">
      <ensemble-model-parameter internal-id="1000010" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="latent-heat-net-flux_gnd-surf_positively-perturbed_stat:avg/PT6H" label="Latent heat net flux - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1000011" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="sensible-heat-net-flux_gnd-surf_positively-perturbed_stat:avg/PT6H" label="Sensible heat net flux - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1000264" member-type="positively-perturbed" total-member-count="30" dataset="stat:accumulation/PT6H" special-level="gnd-surf" edr-id="total-precipitation_gnd-surf_positively-perturbed_stat:acc/PT6H" label="Total precipitation - Ground surface - Positively perturbed ; Accumulation 6h" unit="P_KG_M2"/>
      <ensemble-model-parameter internal-id="1000448" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1000448_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1000449" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1000449_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1000450" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1000450_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1000451" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1000451_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001216" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1001216_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001217" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1001217_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001472" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1001472_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001473" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="gnd-surf" edr-id="1001473_gnd-surf_positively-perturbed_stat:avg/PT6H" label="? - Ground surface - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001473" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="atmosphere-top" edr-id="1001473_atmosphere-top_positively-perturbed_stat:avg/PT6H" label="? - Atmosphere top - Positively perturbed ; Average 6h"/>
      <ensemble-model-parameter internal-id="1001537" member-type="positively-perturbed" total-member-count="30" dataset="stat:average/PT6H" special-level="atmosphere" edr-id="total-cloud-cover_atmosphere_positively-perturbed_stat:avg/PT6H" label="Total cloud cover - Atmosphere - Positively perturbed ; Average 6h" unit="C_PERC"/>
    </model-collection>
  </service>
</webservice>