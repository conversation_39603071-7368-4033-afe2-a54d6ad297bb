<?xml version="1.0" encoding="UTF-8"?>
<!--

  Created:  08.04.2014
  Authors: <AUTHORS>
  All rights reserved. Unauthorised use, modification or redistribution is prohibited.

-->
<XIBL-STORAGE version="1.1">
  <docstorage-mappings>
    <!-- Mappings -->
    <mapping path="" destination="xml:$METPATH/config/docstorage"/>
    <mapping path="current-user" destination="xml:$PROFILE/$IBLPRODUCT"/>
    <!-- doc:runtime is for runtime data local to this machine (e.g. caches)-->
    <mapping path="runtime" destination="xml:$METPATH/var"/>
    <!-- doc:runtime-shared is for runtime data that is shared between server and clients like DBs, counters -->
    <mapping path="runtime-shared" destination="xml:$METPATH/var"/>
    <mapping path="global/onlineweather" destination="file:$METPATH/config/docstorage/global/onlineweather"/>
    <mapping path="global/messageeditor" destination="file:$METPATH/config/MessageEditor"/>
    <mapping path="global/messageeditor/registeredforms" destination="forms:"/>
    <mapping path="global/settings/anyt" destination="file:$METPATH/config/anyt"/>

    <!-- Exposed paths -->
    <expose path="doc:" access="readonly"/>
    <expose path="doc:runtime/fdb" access="readwrite"/>
    <expose path="doc:runtime/reportamendment" access="readwrite"/>
    <expose path="doc:runtime/MessageEditor" access="readwrite"/>
    <expose path="doc:global/messageeditor" access="readwrite"/>
    <expose path="doc:runtime/notam-db" access="readwrite"/>
    <expose path="doc:runtime/appdata/Counters" access="readwrite"/>
    <expose path="doc:global/settings/counters" access="readwrite"/>
    <expose path="doc:runtime/appdata/Marine Tows" access="readwrite"/>
    <expose path="doc:runtime/appdata/Regular Tasks" access="readwrite"/>
    <expose path="doc:runtime/contentrepository" access="readwrite"/>
    <expose path="doc:runtime/PlaceCatalog" access="readwrite"/>
    <expose path="doc:runtime-shared" access="readwrite"/>
  </docstorage-mappings>
  <docstorage-auditing operations="changes"/>
</XIBL-STORAGE>