<?xml version="1.0"?>
<layer-group title="BACKGROUND" xml:id="BACKGROUND_LAYERS">
    <bbox crs="CRS:84" x-min="-180.0" y-min="-90.0" x-max="180.0" y-max="90.0"/>
    <layer-group title="BACKGROUNDS" tiling-profile="tile256x256-0px-margin-cached">
        <bbox crs="CRS:84" x-min="-180.0" y-min="-90.0" x-max="180.0" y-max="90.0"/>
        <layer name="GIS_LAND" title="Map (Low Contrast)">
            <map source="maps:Base_Layers/Simple_Base_Map" clones="8" />
            <style name="default" title="Map (Low Contrast)" />
        </layer>
        <layer name="GIS_OROGRAPHY" title="Relief">
            <map source="maps:Base_Layers/Orography_Base_Map" clones="8" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="Relief" />
        </layer>
        <layer name="GIS_BROWN_LAND_BLUE_SEA" title="Map (High Contrast)">
            <map source="maps:Base_Layers/Brown_Blue_Base_Map" clones="8" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="Map (High Contrast)" />
        </layer>
    </layer-group>
    <layer-group title="OVERLAYS" tiling-profile="tile256x256-0px-margin-cached">
        <layer name="GIS_BOUNDARIES" title="Boundaries">
            <map source="maps:Base_Layers/Border_Overlay" />
            <style name="default" title="Boundaries" />
            <style name="thick_boundaries" title="Thick Boundaries">
                <alter-layer with-name-matching="*">
                    <set-property name="geosets/borderp" line-style="solid" width="2.0pt" color="black" />
                    <set-property name="geosets/coastp" line-style="solid" width="2.0pt" color="black" />
                    <set-property name="geosets/provincep" line-style="solid" width="0.56pt" color="black" />
                    <set-property name="geosets/lakep" line-style="solid" width="0.56pt" color="#FFFF00" />
                </alter-layer>
            </style>
            <style name="yellow_boundaries" title="Yellow Boundaries">
                <alter-layer with-name-matching="*">
                    <set-property name="geosets/borderp" line-style="solid" width="2.0pt" color="#FFFF00" />
                    <set-property name="geosets/coastp" line-style="solid" width="2.0pt" color="#FFFF00" />
                    <set-property name="geosets/provincep" line-style="solid" width="0.56pt" color="#FFFF00" />
                    <set-property name="geosets/lakep" line-style="solid" width="1.0pt" color="#FFFF00" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GIS_Satellite_Border_Overlay" title="Satellite_Border_Overlay">
            <map source="maps:Base_Layers/Satellite_Border_Overlay" />
            <style name="default" title="Satellite Border Overlay" />
        </layer>
        <layer name="GIS_CITIES" title="Cities">
            <map source="maps:Base_Layers/Cities_Overlay" />
            <style name="default" title="Cities" />
        </layer>
        <layer name="GIS_GRATICULE" title="Lat/Lon Lines">
            <map source="maps:Base_Layers/Lat_Lon_Lines" />
            <style name="default" title="Lat/Lon Lines" />
            <style name="interval_1_black" title="Black 1 Degree Interval">
                <alter-layer with-name-matching="*">
                    <set-property name="gstepla" value="0.0174533" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gsteplo" value="0.0174533" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gtextla" value="0.0174533" width="0.5pt" color="black" />
                    <set-property name="gtextlaofs" value="0.00872665" width="0.5pt" color="black" />
                    <set-property name="gtextlastep" value="0.0174533" width="0.5pt" color="black" />
                    <set-property name="gtextlo" value="0.0174533" width="0.5pt" color="black" />
                    <set-property name="gtextloofs" value="0.00872665" width="0.5pt" color="black" />
                    <set-property name="gtextlostep" value="0.0174533" width="0.5pt" color="black" />
                </alter-layer>
            </style>
            <style name="interval_1_white" title="White 1 Degree Interval">
                <alter-layer with-name-matching="*">
                    <set-property name="gridp" line-style="dotted" width="0.5pt" color="white" />
                    <set-property name="gridt" font="5" size="3pt" color="white" />
                    <set-property name="gstepla" value="0.0174533" line-style="dashed" width="0.5pt" color="white" />
                    <set-property name="gsteplo" value="0.0174533" line-style="dashed" width="0.5pt" color="white" />
                    <set-property name="gtextla" value="0.0174533" width="0.5pt" color="white" />
                    <set-property name="gtextlaofs" value="0.00872665" width="0.5pt" color="white" />
                    <set-property name="gtextlastep" value="0.0174533" width="0.5pt" color="white" />
                    <set-property name="gtextlo" value="0.0174533" width="0.5pt" color="white" />
                    <set-property name="gtextloofs" value="0.00872665" width="0.5pt" color="white" />
                    <set-property name="gtextlostep" value="0.0174533" width="0.5pt" color="white" />
                </alter-layer>
            </style>
            <style name="interval_10" title="10 Degree Interval">
                <alter-layer with-name-matching="*">
                    <set-property name="gstepla" value="0.174533" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gsteplo" value="0.174533" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gtextla" value="0.174533" width="0.5pt" color="black" />
                    <set-property name="gtextlaofs" value="0.0872665" width="0.5pt" color="black" />
                    <set-property name="gtextlastep" value="0.174533" width="0.5pt" color="black" />
                    <set-property name="gtextlo" value="0.174533" width="0.5pt" color="black" />
                    <set-property name="gtextloofs" value="0.0872665" width="0.5pt" color="black" />
                    <set-property name="gtextlostep" value="0.174533" width="0.5pt" color="black" />
                </alter-layer>
            </style>
            <style name="interval_15" title="Polar Projection 15 Degree Interval">
                <alter-layer with-name-matching="*">
                    <set-property name="gstepla" value="0.087266462599716474" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gsteplo" value="0.087266462599716474" line-style="dashed" width="0.5pt" color="black" />
                    <set-property name="gtextla" value="0.26179938779914941" width="0.5pt" color="black" />
                    <set-property name="gtextlaofs" value="0.043633231299858237" width="0.5pt" color="black" />
                    <set-property name="gtextlastep" value="0.087266462599716474" width="0.5pt" color="black" />
                    <set-property name="gtextlo" value="0.26179938779914941" width="0.5pt" color="black" />
                    <set-property name="gtextloofs" value="0.043633231299858237" width="0.5pt" color="black" />
                    <set-property name="gtextlostep" value="0.087266462599716474" width="0.5pt" color="black" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GIS_LINEOFSUN" title="Day Night Terminator">
            <map source="maps:Base_Layers/Line_of_Sun" />
            <control mapping-type="select-frame" min-age="0" />
            <dimension name="TIME" mapping-type="global-frame" nearest-value="0h" />
            <style name="default" title="Day Night Terminator" />
        </layer>
    </layer-group>
</layer-group>
