<?xml version="1.0"?>
<layer-group title="HRRR" xml:id="HRRR">
    <bbox crs="CRS:84" x-min="-180.0" y-min="-90.0" x-max="180.0" y-max="90.0"/>
    <layer-group title="Clouds">
        <layer name="HRRR_CloudTops" title="HRRR Cloud Tops">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Cloud_Tops" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500853"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Cloud Tops">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Cloud Tops" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_CloudBase" title="HRRR Cloud Bases">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Cloud_Base" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500853"/>
            <style name="default" title="HRRR Cloud Bases">
                <legend sizing-mode="fixed" default-width="500" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Cloud Base" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SIMULATED_GOES-12_IR_CLOUDS" title="SIMULATED GOES-12 IR CLOUDS">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/SIMULATED_GOES-12_IR_CLOUDS" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1245762"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1245762"/>
            <style name="default" title="Simulated GOES-12 IR Clouds">
                <legend sizing-mode="fixed" default-width="500" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="GOES-12_IR" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SIMULATED_GOES_IR_CLOUDS" title="SIMULATED GOES IR CLOUDS">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/SIMULATED_GOES_IR_CLOUDS" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1245762"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1245762"/>
            <style name="default" title="Simulated GOES IR Clouds">
                <legend sizing-mode="fixed" default-width="500" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="GOES-12_IR" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SIMULATED_GOES_WV_CLOUDS" title="SIMULATED GOES WV CLOUDS">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/SIMULATED_GOES_WV_CLOUDS" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1245761"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1245761"/>
            <style name="default" title="Simulated GOES WV Clouds">
                <legend sizing-mode="fixed" default-width="500" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="GOES-12_WV" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Clouds_Dewpoint" title="HRRR Dewpoint Depression">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Dewpoint_Depression" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000000" exclude-values="/.*M/"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="1000000"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Dewpoint Depression">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Dewpoint Depression" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Clouds_Relative_Humidity" title="HRRR Relative Humidity">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Relative_Humidity" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000257"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000257"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="1000257"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Relative Humidity Colorfill">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="RH_Contours"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_10_Interval"
                   title="HRRR Relative Humidity Contours 10% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_5_Interval"
                   title="HRRR Relative Humidity Contours 5% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,5]"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_2_Interval"
                   title="HRRR Relative Humidity Contours 2% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,2]"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Clouds_Relative_Humidity_0-100" title="HRRR Relative Humidity 0-100%">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Relative_Humidity_0-100" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000257"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000257"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="1000257"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Relative Humidity 0-100%">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="RH_Contours"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_10_Interval"
                   title="HRRR Relative Humidity Contours 10% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_5_Interval"
                   title="HRRR Relative Humidity Contours 5%  Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,5]"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Relative_Humidity_Contours_2_Interval"
                   title="HRRR Relative Humidity Contours 2% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="RH_Contours"/>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,2]"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Clouds_Cloud_Cover" title="HRRR Total Cloud Cover">
            <map source="maps:MODEL_DATA/HRRR/CLOUDS/Total_Cloud_Cover" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500871"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500871" exclude-values="/PT0H|PT0S"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Total Cloud Cover">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="HRRR Total Cloud Cover" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Soil">
        <layer name="HRRR_Soil_Moisture" title="HRRR Soil Moisture">
            <map source="maps:MODEL_DATA/HRRR/SOIL/Soil_Moisture" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1131264"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1131264"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="Soil Moisture">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Soil Moisture" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Sfc_Soil_Temp_Diff" title="Sfc Soil Temp Diff">
            <map source="maps:MODEL_DATA/HRRR/SOIL/SFC_Soil_Temp_Diff" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1131074"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1131074"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="Sfc Soil Temp Diff">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Temp Diff" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Smoke">
        <layer name="HRRR_Ceiling" title="HRRR Ceiling">
            <map source="maps:MODEL_DATA/HRRR/SMOKE/HRRR_CEILING" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500853"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Ceiling">
                <legend sizing-mode="fixed" default-width="325" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Ceiling" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_COLMD" title="HRRR COLMD">
            <map source="maps:MODEL_DATA/HRRR/SMOKE/HRRR_COLMD" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1005121"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1005121"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR COLMD">
                <legend sizing-mode="fixed" default-width="325" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="COLMD" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_VISIBILITY" title="HRRR VISIBILITY">
            <map source="maps:MODEL_DATA/HRRR/SMOKE/HRRR_VISIBILITY" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1004864"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1004864"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR VISIBILITY">
                <legend sizing-mode="fixed" default-width="325" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Visibility" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Precipitation">
        <layer name="HRRR_Precip_1hr" title="HRRR Precipitation 1hr Accumulation">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/HRRR_Precipitation_1hr" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000264"/> <!--Accumulation forecast hs should be slighlty different, no 0hr so using 1000264, Total Precip-->
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="1 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Precip_3hr" title="HRRR Precipitation 3hr Accumulation">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/HRRR_Precipitation_3hr" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000264"/> <!--Accumulation forecast hs should be slighlty different, no 0hr so using 1000264, Total Precip-->
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="3 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Precip_6hr" title="HRRR Precipitation 6hr Accumulation">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/HRRR_Precipitation_6hr" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000264"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="6 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Precip_12hr" title="HRRR Precipitation 12hr Accumulation">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/HRRR_Precipitation_12hr" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000264"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="12 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Precip_Total" title="HRRR Precipitation Total Accumulation">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/HRRR_Precipitation_Total_Accumulation" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000264"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000264"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Total Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Precip_Water" title="HRRR Precipitable Water">
            <map source="maps:MODEL_DATA/HRRR/PRECIPITATION/Precipitable_Water" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000259"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000259"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Precipitable Water">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Precip Water" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Pressure">
        <layer name="HRRR_MSLP" title="HRRR MSLP">
            <map source="maps:MODEL_DATA/HRRR/PRESSURE/MSLP_Styles" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000966"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000966"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="2mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="1mb_Interval" title="1mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="4mb_Interval" title="4mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="6mb_Interval" title="6mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="6"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Geopotential_Height" title="HRRR Geopotential Height">
            <map source="maps:MODEL_DATA/HRRR/PRESSURE/Geopotential_Height" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500853"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="10m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="30m" title="30m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="60m" title="60m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="6"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="120m" title="120m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="12"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Thickness" title="HRRR Thickness">
            <map source="maps:MODEL_DATA/HRRR/PRESSURE/Thickness_styles"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="1000-500mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="1000-500mb Thickness"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="850-700mb_Thickness" title="850-700mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="850-700mb" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="850-700mb Thickness"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="1000-850mb_Thickness" title="1000-850mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="1000-850mb" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <show-layer with-name="1000-850mb Thickness"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Severe Weather">
        <layer name="HRRR_700MB_VV_Contours" title="HRRR VV Contours">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/700mb_w_VV_Contours" clones="8" />
            <feature-info mapping-type="point-info" />
            <control mapping-type="select-centre" />
            <control mapping-type="select-run" min-age="4h" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR" parameter-id="1000520"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR" parameter-id="1000520"/>
            <style name="default" title="VV Contours">
                <legend sizing-mode="fixed" default-width="350" default-height="40" cached="true">
                    <part name="Vertical Velocity" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_Abs_Vort" title="HRRR Absolute Vorticity">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Abs_Vorticity" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000522"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000522"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="1000522"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Absolute Vorticity Colorfill">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="48%" width="100%" height="50%"/>
                    <part layer-name="Absolute Vorticity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Abs_Vort_Contours" title="HRRR Absolute Vorticity Contours">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_CINH" title="HRRR CINH">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/CINH" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001799"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001799"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="CINH Colorfill">
                <legend sizing-mode="fixed" default-width="340" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="CINH Colorfill" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="CINH Contoured"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="CINH_Contoured" title="CINH Contoured">
                <legend sizing-mode="fixed" default-width="340" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="CINH Colorfill"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_Moisture_Convergence" title="HRRR Moisture Convergence">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Moisture_Convergence" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000282"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000282"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Moisture Convergence">
                <legend sizing-mode="fixed" default-width="325" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Divergence_Convergence" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                    <part layer-name="Divergence_Contour" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_Storm_Rel_Hel_0-1km" title="HRRR Storm Relative Helicity (0-1km)">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/0-1km_Storm_Relative_Helicity" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001800"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001800"/>
            <style name="default" title="HRRR Storm Relative Helicity">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="0-1km Storm Helicity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_Storm_Rel_Hel_0-3km" title="HRRR Storm Relative Helicity (0-3km)">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/0-3km_Storm_Relative_Helicity" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001800"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001800"/>
            <style name="default" title="HRRR Storm Relative Helicity">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="0-3km Storm Helicity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_CAPE_0-3km" title="HRRR CAPE (0-3km)">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/0-3km_CAPE" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001798"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001798"/>
            <style name="default" title="HRRR CAPE (0-3km)">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="0-3km CAPE" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_3Hr_10m_Wind_Change" title="HRRR 3hr 10m Wind Change">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/3hr_10m_wind_change" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <style name="default" title="HRRR Wind Change">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part layer-name="wind at" name="colors" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_3Hr_MLSP_Change" title="HRRR 3hr MSLP Change">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/3hr_MLSP_Change" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000966"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <style name="default" title="HRRR 3hr MLSP Change">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part layer-name="wind at" name="colors" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_6hr_500hpa_gph_change" title="HRRR 6hr 500hPa GPH Change">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/6hr 500hPa GPH Change" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast"/>
            <style name="default" title="HRRR 6hr 500hPa GPH Change">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="6hr GPH Change" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SevereWx_Divergence" title="HRRR Divergence/Convergence">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Divergence" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run"  fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500800"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Divergence/Convergence Colorfill">
                <legend sizing-mode="fixed" default-width="330" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Divergence_Convergence" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Divergence_Contour"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Contour" title="HRRR Divergence/Convergence Contour">
                <legend sizing-mode="fixed" default-width="330" default-height="25" cached="true">
                    <part name="Div_Contour" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="Divergence_Convergence"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Derived_Base_Reflectivity" title="HRRR Derived Base Reflectivity">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_Derived_Base_Reflectivity" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1004291"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1004291"/>
            <style name="default" title="HRRR Derived Base Reflectivity">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Max_Reflectivity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Derived_Radar_Echo_Tops" title="HRRR Derived Radar Echo Tops">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_Derived_Radar_Echo_Tops" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1004099"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1004099"/>
            <style name="default" title="HRRR Derived Radar Echo Tops">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Enhanced_Stretching_Potential" title="HRRR Enhanced Stretching Potential">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_ESP" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001997"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001997"/>
            <style name="default" title="HRRR Enhanced Stretching Potential">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Effective_Storm_Relative_Helicity" title="HRRR Effective Storm Relative Helicity">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_ESRH" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001996"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1001996"/>
            <style name="default" title="HRRR Effective Storm Relative Helicity">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_LCL" title="HRRR LCL">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_LCL" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="HRRR LCL">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="LCL" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_LFC" title="HRRR LFC">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/HRRR_LFC" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="HRRR LFC">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="LFC" name="Colour gradient" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Lifted_Index" title="Lifted Index">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Lifted_Index" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001985"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="HRRR Lifted Index">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Lifted Index" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Lightning" title="Lightning">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Lightning" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1004544"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1004544"/>
            <style name="default" title="HRRR Lightning">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part name="colors" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Max_Reflectivity" title="Max Reflectivity">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Max_Reflectivity" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1004292"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1004292"/>
            <style name="default" title="Max Reflectivity">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Max_Reflectivity" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_MLCAPE" title="MLCAPE">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/MLCAPE" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001798"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="HRRR Lightning">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="MLCAPE" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_MUCAPE" title="MUCAPE">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/MUCAPE" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001798"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="MUCAPE">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="MUCAPE" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SFC_CAPE" title="SFC CAPE">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/SFC_CAPE" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1001798"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="SFC_CAPE">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <part layer-name="Surface CAPE" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Topography_Lift" title="Topography Lift">
            <map source="maps:MODEL_DATA/HRRR/SEVERE_WEATHER/Topography_Lift" clones="8"/>
            <feature-info mapping-type="point-info"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500853"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <style name="default" title="Topography_Lift">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="200%" height="50%"/>
                    <!--part layer-name="Surface CAPE" name="colors" x="0%" y="0%" width="100%" height="50%"/-->
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Standard Model Output">
        <layer name="HRRR_200MB" title="HRRR 200MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/200MB" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 200MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Divergence" title="Divergence">
                <legend sizing-mode="fixed" default-width="700" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Divergence" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Temperature"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_300MB" title="HRRR 300MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/300MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 300MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Divergence"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Divergence" title="Divergence">
                <legend sizing-mode="fixed" default-width="700" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Divergence" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Temperature"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_500MB" title="HRRR 500MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/500MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 500MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Absolute_Vorticity_Colorfill" title="Absolute Vorticity Colorfill">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Absolute Vorticity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Absolute_Vorticity_Contours" title="Absolute Vorticity Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="40" cached="true">
                    <part name="Topic1" x="0%" y="0%" width="100%" height="50%"/>
                    <part name="Other" x="0%" y="50%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_600MB" title="HRRR 600MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/600MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 600MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Absolute_Vorticity_Colorfill" title="Absolute Vorticity Colorfill">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Absolute Vorticity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Absolute_Vorticity_Contours" title="Absolute Vorticity Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="40" cached="true">
                    <part name="Topic1" x="0%" y="0%" width="100%" height="50%"/>
                    <part name="Other" x="0%" y="50%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="Vertical Velocity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Vertical_Velocity" title="Vertical Velocity">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part name="Vertical Velocity" x="0%" y="44%" width="100%" height="28%"/>
                    <part layer-name="Vertical Velocity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Absolute Vorticity"/>
                <hide-layer with-name="Relative Humidity"/>
                <hide-layer with-name="Absolute Vorticity Contours"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_700MB" title="HRRR 700MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/700MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 700MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Vertical Velocity"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Vertical Velocity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Vertical Velocity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Vertical Velocity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Vertical Velocity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Vertical_Velocity" title="Vertical Velocity">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Vertical Velocity" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Vertical Velocity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="Relative Humidity"/>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_850MB" title="HRRR 850MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/850MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 850MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_925MB" title="HRRR 925MB CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/925MB" clones="8"/>
                        <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR 925MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%"/>
                    <part name="Other" x="0%" y="72%" width="100%" height="28%"/>
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%"/>
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SFC" title="HRRR SFC CHART">
            <map source="maps:MODEL_DATA/HRRR/STANDARD_MODEL_OUTPUT/SFC" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR SFC CHART">
                <legend sizing-mode="fixed" default-width="450" default-height="73" cached="true">
                    <part name="Topic" x="0%" y="34%" width="100%" height="66%"/>
                    <part layer-name="Gradient" name="colors" x="0%" y="0%" width="100%" height="34%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Temperature">
        <layer name="HRRR_Temperature_in_C" title="HRRR Temperature &#0176;C">
            <map source="maps:MODEL_DATA/HRRR/TEMPERATURE/Temperature_in_C" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500850"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500850" exclude-values="/.*M/"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500850"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <!-- Begin Isotherms in C incremented contours -->
            <style name="default" title="Isotherms in C, 5&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,4]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,3]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,2]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,1]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <!-- Begin HRRR Colorfill styles -->
            <style name="Temperatures_in_C" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Temperatures in C" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Isotherms in C"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Temperature_in_F" title="HRRR Temperature &#0176;F">
            <map source="maps:MODEL_DATA/HRRR/TEMPERATURE/Temperature_in_F" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500850"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500850" exclude-values="/.*M/"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500850"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <!-- Begin Isotherms in F incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,4]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,3]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,2]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,1]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <!-- Begin HRRR Colorfill styles -->
            <style name="Temperatures_in_F" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Colorfill F" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Temperatures in F" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Isotherms in F"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Surface_Temperature_in_C" title="HRRR Surface Temperature &#0176;C">
            <map source="maps:MODEL_DATA/HRRR/TEMPERATURE/SFC_Temperature_in_C" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <control mapping-type="select-level" level="2m"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500850"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500850"/>
            <feature-info mapping-type="point-info"/>
            <!-- Begin Isotherms in C incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,4]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,3]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,2]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in C"/>
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,1]#0"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <!-- Begin HRRR Colorfill styles -->
            <style name="Temperatures_in_C" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Temperatures in C" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Isotherms in C"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Surface_Temperature_in_F" title="HRRR Surface Temperature &#0176;F">
            <map source="maps:MODEL_DATA/HRRR/TEMPERATURE/SFC_Temperature_in_F" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <control mapping-type="select-level" level="2m"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500850"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500850"/>
            <feature-info mapping-type="point-info"/>
            <!-- Begin Isotherms in F incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,4]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,3]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,2]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%"/>
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%"/>
                </legend>
                <hide-layer with-name="Temperatures in F"/>
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,1]#32"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <!-- Begin HRRR Colorfill styles -->
            <style name="Temperatures_in_F" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Temperatures in F" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Isotherms in F"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Freezing_Level" title="HRRR Freezing Level">
            <map source="maps:MODEL_DATA/HRRR/TEMPERATURE/Freezing_Level" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500850"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500850"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Freezing Level Contours">
                <legend sizing-mode="fixed" default-width="250" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="Freezing Level Fill"/>
                <hide-layer with-name="land"/>
            </style>
            <style name="Freezing_Level_Fill" title="HRRR Freezing Level Colorfill">
                <legend sizing-mode="fixed" default-width="500" default-height="50" cached="true">
                    <part name="Colorfill" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Freezing Level Fill" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="Freezing Level Contours"/>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Tendencies">
        <layer name="HRRR_Temp_Ten" title="HRRR Temperature Tendency">
            <map source="maps:MODEL_DATA/HRRR/TENDENCIES/Temp_Tendency" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="1000000"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="1000000" exclude-values="/.*M/"/>
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="1000000"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Temperature Tendency">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Tendency" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Winds">
        <layer name="HRRR_Wind_Barbs" title="HRRR Winds">
            <map source="maps:MODEL_DATA/HRRR/WINDS/Wind_Barbs" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500800"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="Black Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Blue_Wind_Barbs" title="Blue Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%"/>
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="#05C5FF"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Red_Wind_Barbs" title="Red Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%"/>
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="red"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_Gray_Wind_Barbs" title="Gray Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%"/>
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="#A0A0A4"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
            <style name="HRRR_White_Wind_Barbs" title="White Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%"/>
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="white"/>
                </alter-layer>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Surface_Winds" title="HRRR Surface Winds">
            <map source="maps:MODEL_DATA/HRRR/WINDS/SFC_Wind_Speeds" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500800"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Wind Speeds">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Wind Speeds" name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_SFC_Wind_Barbs" title="HRRR Surface Wind Barbs">
            <map source="maps:MODEL_DATA/HRRR/WINDS/SFC_Wind_Barbs" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4hour" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="==HRRR"
                       parameter-id="500800"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Surface Wind Barbs">
                <legend sizing-mode="fixed" default-width="225" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Wind_GTE_to_25kts" title="HRRR Wind GTE to 25kts">
            <map source="maps:MODEL_DATA/HRRR/WINDS/Wind_GTE_to_25kts" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500800"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Wind GTE to 25kts">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Max Wind Contour " name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Wind_GTE_to_70kts" title="HRRR Wind GTE to 70kts">
            <map source="maps:MODEL_DATA/HRRR/WINDS/Wind_GTE_to_70kts" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500800"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Wind GTE to 70kts">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%"/>
                    <part layer-name="Max Wind Contour " name="colors" x="0%" y="0%" width="100%" height="50%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
        <layer name="HRRR_Wind_Streamlines" title="HRRR Streamlines">
            <map source="maps:MODEL_DATA/HRRR/WINDS/Streamlines" clones="8"/>
            <control mapping-type="select-centre" model-id="==HRRR"/>
            <control mapping-type="select-run" min-age="4h" fallback="latest"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="==HRRR"
                       parameter-id="500800"/>
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <dimension name="ELEVATION"
                       format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;"
                       mapping-type="model-parameter-query" query="levels" model-id="==HRRR" parameter-id="500800"
                       units="hPa"/>
            <feature-info mapping-type="point-info"/>
            <style name="default" title="HRRR Streamlines">
                <legend sizing-mode="fixed" default-width="200" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%"/>
                </legend>
                <hide-layer with-name="land"/>
            </style>
        </layer>
    </layer-group>
</layer-group>
