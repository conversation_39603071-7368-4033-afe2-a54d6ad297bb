<?xml version="1.0"?>
<layer-group title="GFS" xml:id="GFS">
    <bbox crs="CRS:84" x-min="-180.0" y-min="-90.0" x-max="180.0" y-max="90.0" />
    <layer-group title="Clouds">
        <layer name="GFS_Clouds_Dewpoint" title="GFS Dewpoint Depression">
            <map source="maps:MODEL_DATA/GFS/CLOUDS/Dewpoint_Depression" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Dewpoint Depression">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Dewpoint Depression" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Clouds_Relative_Humidity" title="GFS Relative Humidity">
            <map source="maps:MODEL_DATA/GFS/CLOUDS/Relative_Humidity" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Relative Humidity Colorfill">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="RH_Contours" />
            </style>
            <style name="GFS_Relative_Humidity_Contours_10_Interval" title="GFS Relative Humidity Contours 10% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
            </style>
            <style name="GFS_Relative_Humidity_Contours_5_Interval" title="GFS Relative Humidity Contours 5% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,5]" />
                </alter-layer>
            </style>
            <style name="GFS_Relative_Humidity_Contours_2_Interval" title="GFS Relative Humidity Contours 2% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,2]" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_Clouds_Relative_Humidity_0-100" title="GFS Relative Humidity 0-100%">
            <map source="maps:MODEL_DATA/GFS/CLOUDS/Relative_Humidity_0-100" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000257" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Relative Humidity 0-100%">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="RH_Contours" />
            </style>
            <style name="GFS_Relative_Humidity_Contours_10_Interval" title="GFS Relative Humidity Contours 10% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
            </style>
            <style name="GFS_Relative_Humidity_Contours_5_Interval" title="GFS Relative Humidity Contours 5%  Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,5]" />
                </alter-layer>
            </style>
            <style name="GFS_Relative_Humidity_Contours_2_Interval" title="GFS Relative Humidity Contours 2% Interval">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="RH_Contours" />
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="RH_Contours">
                    <set-property name="steps" value="[5,100,2]" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_Clouds_Cloud_Cover" title="GFS Total Cloud Cover">
            <map source="maps:MODEL_DATA/GFS/CLOUDS/Total_Cloud_Cover" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500871" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500871" exclude-values="/PT0H|PT0S" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Total Cloud Cover">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="GFS Total Cloud Cover" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Precipitation">
        <layer name="GFS_Precip_3hr" title="GFS Precipitation 3hr Accumulation">
            <map source="maps:MODEL_DATA/GFS/PRECIPITATION/GFS_Precipitation_3hr" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000264" /> <!--Accumulation forecast hs should be slighlty different, no 0hr so using 1000264, Total Precip-->
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="3 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="3 HR :" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Precip_6hr" title="GFS Precipitation 6hr Accumulation">
            <map source="maps:MODEL_DATA/GFS/PRECIPITATION/GFS_Precipitation_6hr" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000264" />
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="6 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="6 HR :" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Precip_12hr" title="GFS Precipitation 12hr Accumulation">
            <map source="maps:MODEL_DATA/GFS/PRECIPITATION/GFS_Precipitation_12hr" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000264" />
            <dimension name="FORECAST" mapping-type="global-frame-forecast" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="12 HR Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="12 HR :" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Precip_Total" title="GFS Precipitation Total Accumulation">
            <map source="maps:MODEL_DATA/GFS/PRECIPITATION/GFS_Precipitation_Total_Accumulation" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000264" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000264" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Total Accumulation">
                <legend sizing-mode="fixed" default-width="350" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Total Accumulation :" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Precip_Water" title="GFS Precipitable Water">
            <map source="maps:MODEL_DATA/GFS/PRECIPITATION/Precipitable_Water" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000259" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000259" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Precipitable Water">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Precip Water" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Pressure">
        <layer name="GFS_MSLP" title="GFS MSLP">
            <map source="maps:MODEL_DATA/GFS/PRESSURE/MSLP_Styles" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000769" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000769" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="2mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
            <style name="1mb_Interval" title="1mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
            <style name="4mb_Interval" title="4mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="6mb_Interval" title="6mb Interval">
                <legend sizing-mode="fixed" default-width="375" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="MSLP">
                    <set-property name="steps" value="6" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_Geopotential_Height" title="GFS Geopotential Height">
            <map source="maps:MODEL_DATA/GFS/PRESSURE/Geopotential_Height" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="10m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
            <style name="30m" title="30m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="60m" title="60m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="6" />
                </alter-layer>
            </style>
            <style name="120m" title="120m Isoline Stepping">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <alter-layer with-name-matching="Geopotential">
                    <set-property name="steps" value="12" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_Thickness" title="GFS Thickness">
            <map source="maps:MODEL_DATA/GFS/PRESSURE/Thickness_styles" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="1000-500mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="1000-500mb Thickness" />
            </style>
            <style name="850-700mb_Thickness" title="850-700mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="850-700mb" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="850-700mb Thickness" />
            </style>
            <style name="1000-850mb_Thickness" title="1000-850mb Thickness">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="1000-850mb" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <show-layer with-name="1000-850mb Thickness" />
            </style>
        </layer>
        <layer name="GFS_Pressure_D_Values" title="GFS D-Values">
            <map source="maps:MODEL_DATA/GFS/PRESSURE/DValue" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="10Kft D-Values">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="10Kft" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <hide-layer with-name="D-Value_15Kft" />
                <hide-layer with-name="D-Value_20Kft" />
            </style>
            <style name="15Kft_DValue" title="15Kft D-Values">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="15Kft" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <hide-layer with-name="D-Value_10Kft" />
                <hide-layer with-name="D-Value_20Kft" />
            </style>
            <style name="20Kft_DValue" title="20Kft D-Values">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="20Kft" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <hide-layer with-name="D-Value_15Kft" />
                <hide-layer with-name="D-Value_10Kft" />
            </style>
        </layer>
        <layer name="GFS_Tropo_Hgt" title="GFS Tropopause Height">
            <map source="maps:MODEL_DATA/GFS/PRESSURE/Tropopause_Height" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500853" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Tropopause Height">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Tropopause Height" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Severe Weather">
        <layer name="GFS_700MB_VV_Contours" title="GFS VV Contours">
            <map source="maps:MODEL_DATA/GFS/SEVERE_WEATHER/700mb_w_VV_Contours" clones="8" />
            <feature-info mapping-type="point-info" />
            <control mapping-type="select-centre" />
            <control mapping-type="select-run" min-age="4h" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000520"/>
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000520"/>
            <style name="default" title="VV Contours">
                <legend sizing-mode="fixed" default-width="350" default-height="40" cached="true">
                    <part name="Vertical Velocity" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_SevereWx_Abs_Vort" title="GFS Absolute Vorticity">
            <map source="maps:MODEL_DATA/GFS/SEVERE_WEATHER/Abs_Vorticity" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000522" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000522" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000522" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Absolute Vorticity Colorfill">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="48%" width="100%" height="50%" />
                    <part layer-name="Absolute Vorticity" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity Contours" />
            </style>
            <style name="Abs_Vort_Contours" title="GFS Absolute Vorticity Contours">
                <legend sizing-mode="fixed" default-width="300" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
            </style>
        </layer>
        <layer name="GFS_SevereWx_CAPE" title="GFS CAPE">
            <map source="maps:MODEL_DATA/GFS/SEVERE_WEATHER/CAPE" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1001798" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1001798" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS CAPE">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="CAPE" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_SevereWx_CINH" title="GFS CINH">
            <map source="maps:MODEL_DATA/GFS/SEVERE_WEATHER/CINH" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1001799" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1001799" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="CINH Colorfill">
                <legend sizing-mode="fixed" default-width="340" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="CINH Colorfill" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="CINH Contoured" />
            </style>
            <style name="CINH_Contoured" title="CINH Contoured">
                <legend sizing-mode="fixed" default-width="340" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
                <hide-layer with-name="CINH Colorfill" />
            </style>
        </layer>
    </layer-group>
    <layer-group title="Standard Model Output">
        <layer name="GFS_200MB" title="GFS 200MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/200MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="200hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 200MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
            <style name="Divergence" title="Divergence">
                <legend sizing-mode="fixed" default-width="700" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Divergence" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Temperature" />
            </style>
        </layer>
        <layer name="GFS_300MB" title="GFS 300MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/300MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="300hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 300MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Temp" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Divergence" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
            <style name="Divergence" title="Divergence">
                <legend sizing-mode="fixed" default-width="700" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Divergence" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Wind Speed" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Temperature" />
            </style>
        </layer>
        <layer name="GFS_500MB" title="GFS 500MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/500MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="500hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 500MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Relative Humidity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
            <style name="Absolute_Vorticity_Colorfill" title="Absolute Vorticity Colorfill">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Absolute Vorticity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Relative Humidity" />
                <hide-layer with-name="Absolute Vorticity Contours" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5" />
                </alter-layer>
            </style>
            <style name="Absolute_Vorticity_Contours" title="Absolute Vorticity Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="40" cached="true">
                    <part name="Topic1" x="0%" y="0%" width="100%" height="50%" />
                    <part name="Other" x="0%" y="50%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Absolute Vorticity" />
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_700MB" title="GFS 700MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/700MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="700hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 700MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Vertical Velocity" />
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Vertical Velocity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Vertical Velocity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Vertical Velocity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Vertical Velocity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
            <style name="Vertical_Velocity" title="Vertical Velocity">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Vertical Velocity" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Vertical Velocity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <hide-layer with-name="Relative Humidity" />
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="5" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_850MB" title="GFS 850MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/850MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="850hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 850MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_925MB" title="GFS 925MB CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/925MB" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="925hPa"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS 925MB CHART">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
            </style>
            <style name="Temperature4" title="Temperature--4&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="4" />
                </alter-layer>
            </style>
            <style name="Temperature3" title="Temperature--3&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="3" />
                </alter-layer>
            </style>
            <style name="Temperature2" title="Temperature--2&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="2" />
                </alter-layer>
            </style>
            <style name="Temperature1" title="Temperature--1&#0176; Contours">
                <legend sizing-mode="fixed" default-width="600" default-height="57" cached="true">
                    <part name="Topic" x="0%" y="44%" width="100%" height="28%" />
                    <part name="Other" x="0%" y="72%" width="100%" height="28%" />
                    <part layer-name="Relative Humidity" name="colors" x="0%" y="0%" width="100%" height="44%" />
                </legend>
                <alter-layer with-name-matching="Temperature">
                    <set-property name="steps" value="1" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_SFC" title="GFS SFC CHART">
            <map source="maps:MODEL_DATA/GFS/STANDARD_MODEL_OUTPUT/SFC" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS SFC CHART">
                <legend sizing-mode="fixed" default-width="450" default-height="73" cached="true">
                    <part name="Topic" x="0%" y="34%" width="100%" height="66%" />
                    <part layer-name="Gradient" name="colors" x="0%" y="0%" width="100%" height="34%" />
                </legend>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Temperature">
        <layer name="GFS_Temperature_in_C" title="GFS Temperature &#0176;C">
            <map source="maps:MODEL_DATA/GFS/TEMPERATURE/Temperature_in_C" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" units="hPa" />
            <feature-info mapping-type="point-info" />
            <!-- Begin Isotherms in C incremented contours -->
            <style name="default" title="Isotherms in C, 5&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,4]#0" />
                </alter-layer>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,3]#0" />
                </alter-layer>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,2]#0" />
                </alter-layer>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,1]#0" />
                </alter-layer>
            </style>
            <!-- Begin GFS Colorfill styles -->
            <style name="Temperatures_in_C" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Temperatures in C" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Isotherms in C" />
            </style>
        </layer>
        <layer name="GFS_Temperature_in_F" title="GFS Temperature &#0176;F">
            <map source="maps:MODEL_DATA/GFS/TEMPERATURE/Temperature_in_F" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" units="hPa" />
            <feature-info mapping-type="point-info" />
            <!-- Begin Isotherms in F incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,4]#32" />
                </alter-layer>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,3]#32" />
                </alter-layer>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,2]#32" />
                </alter-layer>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="225" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,1]#32" />
                </alter-layer>
            </style>
            <!-- Begin GFS Colorfill styles -->
            <style name="Temperatures_in_F" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Colorfill F" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Temperatures in F" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Isotherms in F" />
            </style>
        </layer>
        <layer name="GFS_Surface_Temperature_in_C" title="GFS Surface Temperature &#0176;C">
            <map source="maps:MODEL_DATA/GFS/TEMPERATURE/SFC_Temperature_in_C" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="2m"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <feature-info mapping-type="point-info" />
            <!-- Begin Isotherms in C incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,4]#0" />
                </alter-layer>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,3]#0" />
                </alter-layer>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,2]#0" />
                </alter-layer>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in C" />
                <alter-layer with-name-matching="Isotherms in C">
                    <set-property name="steps" value="[-50,50,1]#0" />
                </alter-layer>
            </style>
            <!-- Begin GFS Colorfill styles -->
            <style name="Temperatures_in_C" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Temperatures in C" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Isotherms in C" />
            </style>
        </layer>
        <layer name="GFS_Surface_Temperature_in_F" title="GFS Surface Temperature &#0176;F">
            <map source="maps:MODEL_DATA/GFS/TEMPERATURE/SFC_Temperature_in_F" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <control mapping-type="select-level" level="2m"/>
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500850" />
            <feature-info mapping-type="point-info" />
            <!-- Begin Isotherms in F incremented contours -->
            <style name="default" title="5&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
            </style>
            <style name="4_deg_contours" title="4&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,4]#32" />
                </alter-layer>
            </style>
            <style name="3_deg_contours" title="3&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,3]#32" />
                </alter-layer>
            </style>
            <style name="2_deg_contours" title="2&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,2]#32" />
                </alter-layer>
            </style>
            <style name="1_deg_contours" title="1&#0176; contours">
                <legend sizing-mode="fixed" default-width="270" default-height="65" cached="true">
                    <part name="Title" x="0%" y="60%" width="100%" height="40%" />
                    <part name="Topic" x="0%" y="0%" width="100%" height="60%" />
                </legend>
                <hide-layer with-name="Temperatures in F" />
                <alter-layer with-name-matching="Isotherms in F">
                    <set-property name="steps" value="[-100,150,1]#32" />
                </alter-layer>
            </style>
            <!-- Begin GFS Colorfill styles -->
            <style name="Temperatures_in_F" title="Colorfill">
                <legend sizing-mode="fixed" default-width="400" default-height="50" cached="true">
                    <part name="Title" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Temperatures in F" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
                <hide-layer with-name="Isotherms in F" />
            </style>
        </layer>
    </layer-group>
    <layer-group title="Tendencies">
        <layer name="GFS_Temp_Ten" title="GFS Temperature Tendency">
            <map source="maps:MODEL_DATA/GFS/TENDENCIES/Temp_Tendency" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="1000000" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Temperature Tendency">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Tendency" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
    </layer-group>
    <layer-group title="Winds">
        <layer name="GFS_Wind_Barbs" title="GFS Winds">
            <map source="maps:MODEL_DATA/GFS/WINDS/Wind_Barbs" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="Black Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%" />
                </legend>
            </style>
            <style name="GFS_Blue_Wind_Barbs" title="Blue Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%" />
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="#05C5FF" />
                </alter-layer>
            </style>
            <style name="GFS_Red_Wind_Barbs" title="Red Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%" />
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="red" />
                </alter-layer>
            </style>
            <style name="GFS_Gray_Wind_Barbs" title="Gray Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%" />
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="#A0A0A4" />
                </alter-layer>
            </style>
            <style name="GFS_White_Wind_Barbs" title="White Wind Barbs">
                <legend sizing-mode="fixed" default-width="220" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="200%" height="100%" />
                </legend>
                <alter-layer with-name="Wind">
                    <set-property name="barbp" line-style="solid" width="1pt" color="white" />
                </alter-layer>
            </style>
        </layer>
        <layer name="GFS_Surface_Winds" title="GFS Surface Winds">
            <map source="maps:MODEL_DATA/GFS/WINDS/SFC_Wind_Speeds" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Wind Speeds">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Wind Speeds" name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_SFC_Wind_Barbs" title="GFS Surface Wind Barbs">
            <map source="maps:MODEL_DATA/GFS/WINDS/SFC_Wind_Barbs" clones="8" />
            <control mapping-type="select-run" min-age="4hour" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Surface Wind Barbs">
                <legend sizing-mode="fixed" default-width="225" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Wind_GTE_to_25kts" title="GFS Wind GTE to 25kts">
            <map source="maps:MODEL_DATA/GFS/WINDS/Wind_GTE_to_25kts" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Wind GTE to 25kts">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Max Wind Contour " name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Wind_GTE_to_70kts" title="GFS Wind GTE to 70kts">
            <map source="maps:MODEL_DATA/GFS/WINDS/Wind_GTE_to_70kts" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Wind GTE to 70kts">
                <legend sizing-mode="fixed" default-width="300" default-height="50" cached="true">
                    <part name="Topic" x="0%" y="50%" width="100%" height="50%" />
                    <part layer-name="Max Wind Contour " name="colors" x="0%" y="0%" width="100%" height="50%" />
                </legend>
            </style>
        </layer>
        <layer name="GFS_Wind_Streamlines" title="GFS Streamlines">
            <map source="maps:MODEL_DATA/GFS/WINDS/Streamlines" clones="8" />
            <control mapping-type="select-run" min-age="4h" fallback="latest" />
            <dimension name="RUN" mapping-type="model-parameter-query" query="runs" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="FORECAST" mapping-type="model-parameter-query" query="forecasts" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" />
            <dimension name="ELEVATION" format="&lt;value content=&quot;GridLevel&quot; show-units=&quot;false&quot;/&gt;" mapping-type="model-parameter-query" query="levels" model-id="sd:NOAA-GFS-PGRB2-0p25-RAW" parameter-id="500800" units="hPa" />
            <feature-info mapping-type="point-info" />
            <style name="default" title="GFS Streamlines">
                <legend sizing-mode="fixed" default-width="200" default-height="25" cached="true">
                    <part name="Topic" x="0%" y="0%" width="100%" height="100%" />
                </legend>
            </style>
        </layer>
    </layer-group>
</layer-group>
