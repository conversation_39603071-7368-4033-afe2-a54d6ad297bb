FROM 125100372861.dkr.ecr.us-east-2.amazonaws.com/open-weather-rocky8-base:latest AS build-image

# move local files into a /deps/ folder
COPY --chown=vwadmin:vwadmin ogc_services/config/. /deps/ogc_services-config

USER vwadmin

ENV METPATH=/opt/OpenWeather
ENV PATH=${METPATH}/config/bin:${METPATH}/bin:${METPATH}/etc:${PATH}

WORKDIR ${METPATH}

RUN umask 0002 \
    && rsync -a /deps/ogc_services-config/ config/ \
    && source ./isystem.env \


FROM build-image AS deploy_container

USER vwadmin

WORKDIR ${METPATH}

ENTRYPOINT ["/opt/OpenWeather/bin/isystem", "startwatch"]
