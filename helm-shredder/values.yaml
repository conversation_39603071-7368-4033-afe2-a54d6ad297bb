# Default values for shredder services
namespace: open-weather-shredder

# Shared environment variables (applied to all shredder pods)
globalEnvs:
  # Elasticsearch and content repository
  IELASTICSEARCHSTORAGE: "https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=objectstorage&aws-signature-v4"
  ICONTENTREPOSITORY: "es:https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=ged&aws-signature-v4"
  AWS_REGION: "us-east-2"
  ELASTIC_SEARCH_STORAGE_INDEX: "objectstorage"
  CONTENT_REPOSITORY_INDEX: "ged"
  ICLOUDPLATFORM: "aws"
  LOG_LEVEL: "info"

  # Shredder-specific environment variables
  GED_BUCKET: "s3:dev-k8s-ged"
  SPLINTER_TRIGGER_QUEUES: "DataArrived"
  OVERFLOW_QUEUE: "ShredderInput"
  SHREDDER_INPUT_QUEUE: "ShredderInput"
  SHREDDER_QUEUE: "ShredderOutput.fifo"
  COMPILER_STATE: "elasticsearch://objectstorage/"
  COMPILER_SPOOL: "s3://dev-k8s-ged/compiler/spool/"
  DATA_EXPIRATION: "P1D"
  COMPILER_QUEUE: "SelectionCompiler"
  SELECTION_TOPIC: "SelectionIssued"

# Define shredder services
replicasets:
  - name: "splinter"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SPLINTER"
    replicas: 1
    resources:
      limits:
        cpu: "500m"
        memory: "512Mi"
      requests:
        cpu: "250m"
        memory: "256Mi"

  - name: "shredder"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SHREDDER"
    replicas: 1
    resources:
      limits:
        cpu: "4000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  - name: "selection-compiler"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SELECTION_COMPILER"
    replicas: 1
    resources:
      limits:
        cpu: "6000m"
        memory: "3Gi"
      requests:
        cpu: "3000m"
        memory: "2Gi"
