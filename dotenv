# AWS TEMP CRED
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=

# GLOBAL ENVS
IELASTICSEARCHSTORAGE="https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=obejctstorage&aws-signature-v4"
ICONTENTREPOSITORY="es:https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=ged&aws-signature-v4"
AWS_REGION=us-east-2
ELASTIC_SEARCH_STORAGE_INDEX=objectstorage
CONTENT_REPOSITORY_INDEX=ged
CLOUD_PLATFORM=aws

INSTANCE_ROLE=SHREDDER

# SPLINTER ENVS
SPLINTER_TRIGGER_QUEUES=DataArrived
OVERFLOW_QUEUE=ShredderInput

# SHREDDER ENVS
GED_BUCKET=s3:dev-k8s-ged
SHREDDER_INPUT_QUEUE=ShredderInput
SHREDDER_QUEUE=ShredderOutput.fifo

# SELECTION-COMPILER ENVS
COMPILER_STATE=elasticsearch://objectstorage
COMPILER_SPOOL=s3:dev-k8s-ged/compiler/spool
DATA_EXPIRATION=P1D
SELECTION_TOPIC=SelectionIssued
COMPILER_QUEUE=SelectionCompiler

# OGC SERVICES ENVS
HOSTNAME=http://test.com 
WMS_WORKER_THREADS=2