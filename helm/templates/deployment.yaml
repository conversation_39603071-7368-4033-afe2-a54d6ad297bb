{{- range .Values.replicasets }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-weather-{{ .name }}
  labels:
    app: open-weather
    role: {{ .INSTANCE_ROLE | quote }}
spec:
  replicas: {{ .replicas }}
  selector:
    matchLabels:
      app: open-weather
      role: {{ .INSTANCE_ROLE | quote }}
  template:
    metadata:
      labels:
        app: open-weather
        role: {{ .INSTANCE_ROLE | quote }}
    spec:
      imagePullSecrets:
        - name: ecr-creds  # Use the Kubernetes secret for AWS ECR authentication

      containers:
        - name: open-weather-{{ .name }}
          image: 125100372861.dkr.ecr.us-east-2.amazonaws.com/{{ .image }}:latest
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: aws-credentials
          env:
            # Global environment variables
            {{- range $key, $value := $.Values.globalEnvs }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}

            # Set INSTANCE_ROLE for this deployment
            - name: INSTANCE_ROLE
              value: {{ .INSTANCE_ROLE | quote }}

          resources:
            {{- toYaml .resources | nindent 12 }}
---
{{- end }}
