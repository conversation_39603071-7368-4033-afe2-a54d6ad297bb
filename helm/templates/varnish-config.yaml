apiVersion: v1
kind: ConfigMap
metadata:
  name: varnish-config
  namespace: {{ .Values.namespace }}
data:
  default.vcl: |
    vcl 4.1;
    backend default {
        .host = "open-weather-proxy.{{ .Values.namespace }}.svc.cluster.local";
        .port = "80";
    }

    sub vcl_recv {
        if (req.url ~ "(?i)GetGTile") {
            set req.http.cache-control = "public, max-age=21600";  # 6 hours
        }
    }

    sub vcl_backend_response {
        if (beresp.status == 200 && beresp.http.Content-Type ~ "image/png") {
            if (bereq.url ~ "(?i)GetGTile") {
                set beresp.ttl = 6h;  # 6 hours
            } else {
                set beresp.ttl = 0s;
            }
        } else {
            set beresp.ttl = 0s;
        }
    }
