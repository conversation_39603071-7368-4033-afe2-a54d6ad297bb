apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-s3-ged
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "s3.amazonaws.com"
    - "dev-k8s-ged.s3.us-east-1.amazonaws.com"
    - "dev-k8s-ged.s3.us-east-2.amazonaws.com"
    - "dev-k8s-ged.s3.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
    - number: 80
      name: http
      protocol: HTTP
  location: MESH_EXTERNAL
  resolution: DNS
---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-metadata-service
  namespace: istio-system
spec:
  hosts:
    - "aws.local"
  addresses:
    - ***************
  ports:
    - number: 80
      name: http
      protocol: HTTP
  resolution: NONE
  location: MESH_EXTERNAL
---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-s3-shredder-input
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "s3.amazonaws.com"
    - "dev-shredder-input.s3.us-east-2.amazonaws.com"
    - "dev-shredder-input.s3.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
    - number: 80
      name: http
      protocol: HTTP
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-s3-generic
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "s3.us-east-2.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-sqs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "sqs.us-east-2.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  resolution: DNS
  location: MESH_EXTERNAL

---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-sns
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "sns.us-east-2.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  resolution: DNS
  location: MESH_EXTERNAL

---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: selection-repository
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  resolution: DNS
  location: MESH_EXTERNAL

---
apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: aws-sts
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "sts.amazonaws.com"
  ports:
    - number: 443
      name: https
      protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS
