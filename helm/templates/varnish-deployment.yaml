apiVersion: apps/v1
kind: Deployment
metadata:
  name: varnish
  namespace: {{ .Values.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: varnish
  template:
    metadata:
      labels:
        app: varnish
    spec:
      containers:
        - name: varnish
          image: varnish:latest
          ports:
            - containerPort: 80
          args:
            - "-s"
            - "malloc,{{ .Values.varnish.cacheSize }}"
          volumeMounts:
            - name: varnish-config
              mountPath: /etc/varnish/default.vcl
              subPath: default.vcl
      volumes:
        - name: varnish-config
          configMap:
            name: varnish-config
