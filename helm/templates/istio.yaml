apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: open-weather-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: open-weather-virtualservice
  namespace: {{ .Values.namespace }}
spec:
  hosts:
    - "*"
  gateways:
    - open-weather-gateway
  http:
    - match:
        - uri:
            prefix: "/ogc/WMS"
      route:
        - destination:
            host: varnish.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 80
      corsPolicy:
        allowOrigin:
          - "*"
        allowMethods:
          - GET
          - POST
          - OPTIONS
        allowHeaders:
          - "*"
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-varnish
  namespace: {{ .Values.namespace }}
spec:
  host: varnish.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-proxy
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-proxy.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: open-weather-wms-gfs
  namespace: {{ .Values.namespace }}
spec:
  host: open-weather-wms-gfs.{{ .Values.namespace }}.svc.cluster.local
  trafficPolicy:
    loadBalancer:
      consistentHash:
        httpQueryParameterName: "LAYER"
---
# Include the new ServiceEntry file
{{- include "open-weather.namespace" . }}
{{- include "open-weather.serviceentry" . }}
