{{- range .Values.replicasets }}
{{- if or (eq .INSTANCE_ROLE "WMS_GIS") (eq .INSTANCE_ROLE "WMS_GFS") (eq .INSTANCE_ROLE "PROXY") }}
apiVersion: v1
kind: Service
metadata:
  name: open-weather-{{ .name }}
  labels:
    app: open-weather
    role: {{ .INSTANCE_ROLE | quote }}
spec:
  selector:
    app: open-weather
    role: {{ .INSTANCE_ROLE | quote }}
  ports:
    - protocol: TCP
      port: {{ if eq .INSTANCE_ROLE "WMS_GIS" "WMS_GFS" }}8008{{ else }}80{{ end }}
      targetPort: {{ if eq .INSTANCE_ROLE "WMS_GIS" "WMS_GFS" }}8008{{ else }}8000{{ end }}
---
{{- end }}
{{- end }}
