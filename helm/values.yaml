# Default values for our Kubernetes deployment
namespace: open-weather

# General environment variables (applied to all pods)
globalEnvs:
  IELASTICSEARCHSTORAGE: "https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=objectstorage&aws-signature-v4"
  ICONTENTREPOSITORY: "es:https://search-dev-content-repository-bbo4ppxcgo7gappevjkxihqbdu.us-east-2.es.amazonaws.com?index=ged&aws-signature-v4"
  AWS_REGION: "us-east-2"
  ELASTIC_SEARCH_STORAGE_INDEX: "objectstorage"
  CONTENT_REPOSITORY_INDEX: "ged"
  ICLOUDPLATFORM: "aws"
  LOG_LEVEL: "info"

  # Shredder-specific environment variables
  GED_BUCKET: "s3:dev-k8s-ged"
  SPLINTER_TRIGGER_QUEUES: "DataArrived"
  OVERFLOW_QUEUE: "ShredderInput"
  SHREDDER_INPUT_QUEUE: "ShredderInput"
  SHREDDER_QUEUE: "ShredderOutput.fifo"
  COMPILER_STATE: "elasticsearch://objectstorage/"
  COMPILER_SPOOL: "s3://dev-k8s-ged/compiler/spool/"
  DATA_EXPIRATION: "P1D"
  COMPILER_QUEUE: "SelectionCompiler"
  SELECTION_TOPIC: "SelectionIssued"
  
  # OGC-specific environment variables
  HOSTNAME: "https://ogc.shyftwx.com"
  WMS_WORKER_THREADS: "2"
  WMS_GIS_URL: "http://open-weather-wms-gis.open-weather.svc.cluster.local:8008"
  WMS_GFS_URL: "http://open-weather-wms-gfs.open-weather.svc.cluster.local:8008"

# Define each replicaset with specific INSTANCE_ROLE
replicasets:
  # Shredder-related services
  - name: "splinter"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SPLINTER"
    replicas: 1
    resources:
      limits:
        cpu: "500m"
        memory: "512Mi"
      requests:
        cpu: "250m"
        memory: "256Mi"

  - name: "shredder"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SHREDDER"
    replicas: 1
    resources:
      limits:
        cpu: "4000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  - name: "selection-compiler"
    image: "open-weather-rocky8-shredder"
    INSTANCE_ROLE: "SELECTION_COMPILER"
    replicas: 1
    resources:
      limits:
        cpu: "6000m"
        memory: "3Gi"
      requests:
        cpu: "3000m"
        memory: "2Gi"

  # OGC WMS services
  - name: "wms-gis"
    image: "open-weather-rocky8-services"
    INSTANCE_ROLE: "WMS_GIS"
    replicas: 1
    resources:
      limits:
        cpu: "3000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  - name: "wms-gfs"
    image: "open-weather-rocky8-services"
    INSTANCE_ROLE: "WMS_GFS"
    replicas: 1
    resources:
      limits:
        cpu: "5000m"
        memory: "8Gi"
      requests:
        cpu: "2000m"
        memory: "4Gi"

  # OGC Proxy service (handles routing)
  - name: "proxy"
    image: "open-weather-rocky8-proxy"
    INSTANCE_ROLE: "PROXY"
    replicas: 1
    resources:
      limits:
        cpu: "250m"
        memory: "256Mi"
      requests:
        cpu: "100m"
        memory: "128Mi"

# Test deployments for IRSA comparison
# testDeployments:
#   - name: "wms-gis-irsa-test"
#     image: "open-weather-rocky8-services"
#     tag: "9.3.0"
#     INSTANCE_ROLE: "WMS_GIS"
#     useIRSA: true
#     replicas: 1
#     resources:
#       limits:
#         cpu: "1000m"
#         memory: "2Gi"
#       requests:
#         cpu: "500m"
#         memory: "1Gi"

#   - name: "wms-gis-legacy-test"
#     image: "open-weather-rocky8-services"
#     tag: "latest"
#     INSTANCE_ROLE: "WMS_GIS"
#     useIRSA: false
#     replicas: 1
#     resources:
#       limits:
#         cpu: "1000m"
#         memory: "2Gi"
#       requests:
#         cpu: "500m"
#         memory: "1Gi"

# Varnish configuration
varnish:
  enabled: true
  replicas: 1
  image: "varnish:latest"
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "250m"
      memory: "512Mi"
  cacheSize: "512M"
