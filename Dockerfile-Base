FROM rockylinux/rockylinux:8.10-ubi AS build-image

USER root

ARG AWS_REGION=us-east-2
ARG OW_INSTALLER=openweather-8.5.2-stable-20240711-enterprise-linux-rh8.run
ARG OW_GLOBAL_DAT=global-f-3.6.dat.gz

# move local files into a /deps/ folder
COPY --chown=1001:1001 ${OW_INSTALLER} base/isystem.env /deps/
COPY --chown=1001:1001 base/config/. /deps/base-config
COPY --chown=1001:1001 ${OW_GLOBAL_DAT} /deps/global.dat.gz

RUN export METPATH=/opt/OpenWeather \
    && useradd -s /bin/bash --uid 1001 vwadmin \
    && mkdir -p $METPATH \
    && chown 1001:1001 $METPATH \
    && chown -R 1001:1001 /deps \
    && chmod 775 /deps/${OW_INSTALLER} \
    # setup dependencies.
    && yum install -y vi bzip2 unzip wget rsync tar libICE libXau libxcb \
    dejavu-sans-fonts dejavu-fonts-common mesa-libGL fontconfig \
    && yum clean all \
    && wget -q "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" \
    && ls -latr \
    && unzip awscli-exe-linux-x86_64.zip \
    && ./aws/install 

USER vwadmin

ENV METPATH=/opt/OpenWeather
ENV PATH=${METPATH}/config/bin:${METPATH}/bin:${METPATH}/etc:${PATH}

WORKDIR ${METPATH}

RUN umask 0002 \
    && /deps/${OW_INSTALLER}  \
    && mkdir config \
    && mkdir -p ./share/topography \
    && rsync -a /deps/base-config/ config/ \
    && gzip -d /deps/global.dat.gz \
    && mv /deps/isystem.env . \
    && mkdir -p var/log \
    && source ./isystem.env \
    && iplugins --rebuild \
    && ipython -m pip install --upgrade pip

WORKDIR ${METPATH}

# need to create the vwadmin user w/ root so vwadmin has a dir in /config/docstorage/users
USER root

RUN source /opt/OpenWeather/isystem.env \
    && usermanager --create-super vwadmin \
    && chown -R 1001:1001 ${METPATH}/var \
    && rm -rf /deps


FROM build-image AS deploy_container

USER vwadmin

WORKDIR ${METPATH}

ENTRYPOINT ["/opt/OpenWeather/bin/isystem", "startwatch"]
